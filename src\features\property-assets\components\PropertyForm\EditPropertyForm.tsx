"use client";

import { useParams } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";

import { useProperty } from "../../hooks/useProperties";
import { PropertyForm } from "./PropertyForm";

export function EditPropertyForm() {
  const { t } = useTranslation();
  const params = useParams();
  const propertyId = params.id as string;

  const { data: property, isLoading, error } = useProperty(propertyId);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-center">
            <Loader2 className="mx-auto mb-4 size-8 animate-spin" />
            <p className="text-muted-foreground">{t("common.loading")}</p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !property) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="mb-4 text-2xl font-bold">{t("properties.notFound")}</h1>
          <p className="text-muted-foreground">{t("properties.notFoundDescription")}</p>
        </div>
      </div>
    );
  }

  return <PropertyForm initialData={property} isEditing={true} />;
}
