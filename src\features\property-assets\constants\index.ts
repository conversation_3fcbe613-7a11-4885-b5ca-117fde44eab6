// Property type options
export const PROPERTY_TYPES = [
  { value: "residential", label: "Residential" },
  { value: "commercial", label: "Commercial" },
  { value: "mixed", label: "Mixed Use" },
] as const;

// Unit type options
export const UNIT_TYPES = [
  { value: "studio", label: "Studio" },
  { value: "1br", label: "1 Bedroom" },
  { value: "2br", label: "2 Bedrooms" },
  { value: "3br", label: "3 Bedrooms" },
  { value: "commercial", label: "Commercial Space" },
  { value: "office", label: "Office" },
  { value: "retail", label: "Retail" },
] as const;

// Contract type options
export const CONTRACT_TYPES = [
  { value: "monthly", label: "Monthly" },
  { value: "annual", label: "Annual" },
  { value: "profit_sharing", label: "Profit Sharing" },
  { value: "revenue_sharing", label: "Revenue Sharing" },
] as const;

// Status options
export const PROPERTY_STATUSES = [
  { value: "active", label: "Active" },
  { value: "inactive", label: "Inactive" },
] as const;

export const UNIT_STATUSES = [
  { value: "available", label: "Available" },
  { value: "occupied", label: "Occupied" },
  { value: "maintenance", label: "Under Maintenance" },
  { value: "inactive", label: "Inactive" },
] as const;

export const CONTRACT_STATUSES = [
  { value: "active", label: "Active" },
  { value: "terminated", label: "Terminated" },
  { value: "expired", label: "Expired" },
  { value: "pending", label: "Pending" },
] as const;

export const TENANT_STATUSES = [
  { value: "active", label: "Active" },
  { value: "inactive", label: "Inactive" },
  { value: "blacklisted", label: "Blacklisted" },
] as const;

export const MAINTENANCE_STATUSES = [
  { value: "open", label: "Open" },
  { value: "in_progress", label: "In Progress" },
  { value: "completed", label: "Completed" },
  { value: "cancelled", label: "Cancelled" },
] as const;

// Priority options
export const MAINTENANCE_PRIORITIES = [
  { value: "low", label: "Low" },
  { value: "medium", label: "Medium" },
  { value: "high", label: "High" },
  { value: "urgent", label: "Urgent" },
] as const;

// Category options
export const MAINTENANCE_CATEGORIES = [
  { value: "plumbing", label: "Plumbing" },
  { value: "electrical", label: "Electrical" },
  { value: "hvac", label: "HVAC" },
  { value: "appliance", label: "Appliance" },
  { value: "structural", label: "Structural" },
  { value: "cosmetic", label: "Cosmetic" },
  { value: "other", label: "Other" },
] as const;

// Payment method options
export const PAYMENT_METHODS = [
  { value: "cash", label: "Cash" },
  { value: "bank_transfer", label: "Bank Transfer" },
  { value: "credit_card", label: "Credit Card" },
  { value: "check", label: "Check" },
] as const;

// Payment type options
export const PAYMENT_TYPES = [
  { value: "rent", label: "Rent" },
  { value: "deposit", label: "Deposit" },
  { value: "late_fee", label: "Late Fee" },
  { value: "maintenance", label: "Maintenance" },
  { value: "other", label: "Other" },
] as const;

// Payment status options
export const PAYMENT_STATUSES = [
  { value: "pending", label: "Pending" },
  { value: "completed", label: "Completed" },
  { value: "failed", label: "Failed" },
  { value: "refunded", label: "Refunded" },
] as const;

// Identification type options
export const IDENTIFICATION_TYPES = [
  { value: "passport", label: "Passport" },
  { value: "driver_license", label: "Driver License" },
  { value: "national_id", label: "National ID" },
] as const;

// Employment status options
export const EMPLOYMENT_STATUSES = [
  { value: "employed", label: "Employed" },
  { value: "self_employed", label: "Self Employed" },
  { value: "student", label: "Student" },
  { value: "unemployed", label: "Unemployed" },
  { value: "retired", label: "Retired" },
] as const;

// Document type options
export const DOCUMENT_TYPES = [
  { value: "id", label: "ID Document" },
  { value: "income_proof", label: "Income Proof" },
  { value: "reference", label: "Reference Letter" },
  { value: "other", label: "Other" },
] as const;

// Transaction categories
export const INCOME_CATEGORIES = [
  { value: "rent", label: "Rent" },
  { value: "deposit", label: "Security Deposit" },
  { value: "late_fee", label: "Late Fee" },
  { value: "utility_reimbursement", label: "Utility Reimbursement" },
  { value: "other_income", label: "Other Income" },
] as const;

export const EXPENSE_CATEGORIES = [
  { value: "maintenance", label: "Maintenance" },
  { value: "utilities", label: "Utilities" },
  { value: "insurance", label: "Insurance" },
  { value: "property_tax", label: "Property Tax" },
  { value: "management_fee", label: "Management Fee" },
  { value: "marketing", label: "Marketing" },
  { value: "legal", label: "Legal Fees" },
  { value: "other_expense", label: "Other Expense" },
] as const;

// Filter options for lists
export const PROPERTY_FILTER_OPTIONS = [
  { value: "all", label: "All Properties" },
  { value: "residential", label: "Residential" },
  { value: "commercial", label: "Commercial" },
  { value: "mixed", label: "Mixed Use" },
] as const;

export const UNIT_FILTER_OPTIONS = [
  { value: "all", label: "All Units" },
  { value: "available", label: "Available" },
  { value: "occupied", label: "Occupied" },
  { value: "maintenance", label: "Under Maintenance" },
] as const;

export const CONTRACT_FILTER_OPTIONS = [
  { value: "all", label: "All Contracts" },
  { value: "active", label: "Active" },
  { value: "pending", label: "Pending" },
  { value: "expired", label: "Expired" },
  { value: "terminated", label: "Terminated" },
] as const;

// Date format constants
export const DATE_FORMATS = {
  DISPLAY: "MMM dd, yyyy",
  INPUT: "yyyy-MM-dd",
  FULL: "MMMM dd, yyyy",
  SHORT: "MM/dd/yyyy",
} as const;

// Currency format
export const CURRENCY_FORMAT = {
  SYMBOL: "$",
  LOCALE: "en-US",
  CURRENCY: "USD",
} as const;

// Pagination defaults
export const PAGINATION_DEFAULTS = {
  PAGE: 1,
  LIMIT: 10,
  LIMITS: [10, 25, 50, 100],
} as const;
