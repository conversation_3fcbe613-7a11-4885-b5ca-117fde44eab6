import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { contractApi } from "@/lib/apis/property-assets";

import type { Contract, CreateContract } from "../types";
import { contractKeys, tenantKeys, unitKeys, type IGetContractsParams } from "./keys";

// List Contracts Hook
export const useContracts = (params?: IGetContractsParams) => {
  return useQuery({
    queryKey: contractKeys.list(params || {}),
    queryFn: () => contractApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Contract by ID Hook
export const useContract = (id: string) => {
  return useQuery({
    queryKey: contractKeys.detail(id),
    queryFn: () => contractApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Contracts by Tenant Hook
export const useTenantContracts = (tenantId: string, params?: IGetContractsParams) => {
  return useQuery({
    queryKey: tenantKeys.contracts(tenantId),
    queryFn: () => contractApi.getByTenant(tenantId, params),
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Contracts by Unit Hook
export const useUnitContracts = (unitId: string, params?: IGetContractsParams) => {
  return useQuery({
    queryKey: unitKeys.contracts(unitId),
    queryFn: () => contractApi.getByUnit(unitId, params),
    enabled: !!unitId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create Contract Hook
export const useCreateContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateContract) => contractApi.create(data),
    onSuccess: (data) => {
      // Invalidate and refetch contracts list
      queryClient.invalidateQueries({ queryKey: contractKeys.lists() });

      // Invalidate tenant contracts if tenant_id is available
      if (data.data.data.tenant_id) {
        queryClient.invalidateQueries({
          queryKey: tenantKeys.contracts(data.data.data.tenant_id),
        });
      }

      // Invalidate unit contracts if unit_id is available
      if (data.data.data.unit_id) {
        queryClient.invalidateQueries({
          queryKey: unitKeys.contracts(data.data.data.unit_id),
        });
      }

      // Add the new contract to the cache
      queryClient.setQueryData(contractKeys.detail(data.data.data.id), data);

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};

// Update Contract Hook
export const useUpdateContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateContract> }) =>
      contractApi.update(id, data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch contracts list
      queryClient.invalidateQueries({ queryKey: contractKeys.lists() });

      // Update the specific contract in cache
      queryClient.setQueryData(contractKeys.detail(variables.id), data);

      // Invalidate related tenant and unit contracts
      if (data.data.data.tenant_id) {
        queryClient.invalidateQueries({
          queryKey: tenantKeys.contracts(data.data.data.tenant_id),
        });
      }

      if (data.data.data.unit_id) {
        queryClient.invalidateQueries({
          queryKey: unitKeys.contracts(data.data.data.unit_id),
        });
      }

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};

// Delete Contract Hook
export const useDeleteContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => contractApi.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: contractKeys.detail(id) });

      // Invalidate all related lists
      queryClient.invalidateQueries({ queryKey: contractKeys.lists() });
      queryClient.invalidateQueries({ queryKey: tenantKeys.all() });
      queryClient.invalidateQueries({ queryKey: unitKeys.all() });

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};
