import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { propertyApi } from "@/lib/apis/property-assets";

import type { CreateProperty, Property } from "../types";
import { propertyKeys, type IGetPropertiesParams } from "./keys";

// List Properties Hook
export const useProperties = (params?: IGetPropertiesParams) => {
  return useQuery({
    queryKey: propertyKeys.list(params || {}),
    queryFn: () => propertyApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Property by ID Hook
export const useProperty = (id: string) => {
  return useQuery({
    queryKey: propertyKeys.detail(id),
    queryFn: () => propertyApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Properties for Select Dropdown Hook
export const usePropertiesForSelect = () => {
  return useQuery({
    queryKey: propertyKeys.select(),
    queryFn: () => propertyApi.list({ limit: 100 }),
    select: (data) =>
      data.data.items.map((property) => ({
        id: property.id,
        name: property.name,
      })),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

// Create Property Hook
export const useCreateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateProperty) => propertyApi.create(data),
    onSuccess: (data) => {
      // Invalidate and refetch properties list
      queryClient.invalidateQueries({ queryKey: propertyKeys.lists() });

      // Add the new property to the cache
      queryClient.setQueryData(propertyKeys.detail(data.data.data.id), data);

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};

// Update Property Hook
export const useUpdateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateProperty> }) =>
      propertyApi.update(id, data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch properties list
      queryClient.invalidateQueries({ queryKey: propertyKeys.lists() });

      // Update the specific property in cache
      queryClient.setQueryData(propertyKeys.detail(variables.id), data);

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};

// Delete Property Hook
export const useDeleteProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => propertyApi.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: propertyKeys.detail(id) });

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: propertyKeys.lists() });

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};
