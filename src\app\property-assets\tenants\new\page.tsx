import { Suspense } from "react";
import { <PERSON>adata } from "next";

import { TenantForm } from "@/features/property-assets/components/TenantForm";

export const metadata: Metadata = {
  title: "Add New Tenant | OneX ERP",
  description: "Add a new tenant with profile information and contact details",
};

export default function NewTenantPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TenantForm />
    </Suspense>
  );
}
