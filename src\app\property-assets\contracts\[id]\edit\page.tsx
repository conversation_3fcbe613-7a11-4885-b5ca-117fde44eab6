"use client";

import { Suspense } from "react";
import { Metadata } from "next";

import { ContractForm } from "@/features/property-assets/components/ContractForm";
import { useContract } from "@/features/property-assets/hooks/useContracts";

interface EditContractPageProps {
  params: {
    id: string;
  };
}

function EditContractContent({ contractId }: { contractId: string }) {
  const { data: contract, isLoading, error } = useContract(contractId);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading contract data...</div>
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center text-red-500">
          Error loading contract data. Please try again.
        </div>
      </div>
    );
  }

  return <ContractForm initialData={contract} isEditing={true} />;
}

export default function EditContractPage({ params }: EditContractPageProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EditContractContent contractId={params.id} />
    </Suspense>
  );
}
