import { NextRequest, NextResponse } from "next/server";

import type { CreateUnit, Unit } from "@/features/property-assets/types";

import { unitsMockData } from "../mock-data";

// GET /api/property-assets/units
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") as Unit["status"] | null;
    const unit_type = searchParams.get("unit_type") as Unit["unit_type"] | null;
    const property_id = searchParams.get("propertyId") || searchParams.get("property_id") || "";

    let filteredUnits = [...unitsMockData];

    // Apply property filter
    if (property_id) {
      filteredUnits = filteredUnits.filter((unit) => unit.property_id === property_id);
    }

    // Apply search filter
    if (search) {
      filteredUnits = filteredUnits.filter(
        (unit) =>
          unit.unit_number.toLowerCase().includes(search.toLowerCase()) ||
          unit.unit_type.toLowerCase().includes(search.toLowerCase()) ||
          (unit.description && unit.description.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply status filter
    if (status) {
      filteredUnits = filteredUnits.filter((unit) => unit.status === status);
    }

    // Apply unit type filter
    if (unit_type) {
      filteredUnits = filteredUnits.filter((unit) => unit.unit_type === unit_type);
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUnits = filteredUnits.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 400));

    return NextResponse.json({
      items: paginatedUnits,
      total: filteredUnits.length,
      page,
      limit,
      totalPages: Math.ceil(filteredUnits.length / limit),
    });
  } catch (error) {
    console.error("Error fetching units:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/property-assets/units
export async function POST(request: NextRequest) {
  try {
    const body: CreateUnit = await request.json();

    // Validate required fields
    if (
      !body.property_id ||
      !body.unit_number ||
      !body.unit_type ||
      !body.square_footage ||
      !body.rent_amount
    ) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Check if unit number already exists for this property
    const existingUnit = unitsMockData.find(
      (unit) => unit.property_id === body.property_id && unit.unit_number === body.unit_number
    );

    if (existingUnit) {
      return NextResponse.json(
        { error: "Unit number already exists for this property" },
        { status: 409 }
      );
    }

    // Transform images to match Unit format
    const transformedImages = body.images?.map((img, index) => ({
      id: `img_${Date.now()}_${index}`,
      name: img.name,
      url: img.image,
      is_primary: index === 0,
    }));

    // Create new unit
    const { images: _, ...bodyWithoutImages } = body;
    const newUnit: Unit = {
      id: `unit_${Date.now()}`,
      ...bodyWithoutImages,
      images: transformedImages,
      status: "available",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company1", // Mock company ID
    };

    // Add to mock data
    unitsMockData.push(newUnit);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 600));

    return NextResponse.json({
      data: newUnit,
      success: true,
      message: "Unit created successfully",
    });
  } catch (error) {
    console.error("Error creating unit:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
