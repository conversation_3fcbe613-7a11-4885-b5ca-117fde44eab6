"use client";

import { useState } from "react";
import {
  Calendar,
  Download,
  Edit,
  Eye,
  File,
  File as FilePdf,
  FileSpreadsheet,
  FileText,
  Filter,
  FolderOpen,
  Grid,
  Image,
  List,
  Lock,
  MoreHorizontal,
  Plus,
  Search,
  SortAsc,
  SortDesc,
  Trash2,
  Unlock,
  Upload,
  User,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface DocumentLibraryProps {
  propertyId?: string;
  className?: string;
}

export function DocumentLibrary({ propertyId, className }: DocumentLibraryProps) {
  const { t } = useTranslation();
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<"name" | "date" | "size">("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [isUploadOpen, setIsUploadOpen] = useState(false);

  // Mock document data
  const documents = [
    {
      id: "1",
      name: "Property Purchase Agreement.pdf",
      category: "Contracts",
      property: "Sunset Apartments",
      type: "pdf",
      size: "2.4 MB",
      uploadDate: "2024-01-15",
      uploadedBy: "John Smith",
      version: "1.0",
      isPublic: false,
      description: "Original purchase agreement for the property",
      tags: ["purchase", "legal", "original"],
      thumbnail: null,
    },
    {
      id: "2",
      name: "Building Inspection Report 2024.pdf",
      category: "Inspections",
      property: "Sunset Apartments",
      type: "pdf",
      size: "5.1 MB",
      uploadDate: "2024-01-10",
      uploadedBy: "Property Inspector",
      version: "1.0",
      isPublic: true,
      description: "Annual building inspection report",
      tags: ["inspection", "maintenance", "annual"],
      thumbnail: null,
    },
    {
      id: "3",
      name: "Floor Plans - Unit 101.jpg",
      category: "Floor Plans",
      property: "Downtown Lofts",
      type: "image",
      size: "1.8 MB",
      uploadDate: "2024-01-08",
      uploadedBy: "Architecture Team",
      version: "2.1",
      isPublic: true,
      description: "Updated floor plan for unit 101",
      tags: ["floorplan", "unit101", "layout"],
      thumbnail: "/api/thumbnails/3.jpg",
    },
    {
      id: "4",
      name: "Insurance Policy 2024.pdf",
      category: "Insurance",
      property: "Garden View Complex",
      type: "pdf",
      size: "800 KB",
      uploadDate: "2024-01-05",
      uploadedBy: "Insurance Agent",
      version: "1.0",
      isPublic: false,
      description: "Current property insurance policy",
      tags: ["insurance", "policy", "2024"],
      thumbnail: null,
    },
    {
      id: "5",
      name: "Maintenance Schedule Q1.xlsx",
      category: "Maintenance",
      property: "All Properties",
      type: "spreadsheet",
      size: "450 KB",
      uploadDate: "2024-01-03",
      uploadedBy: "Maintenance Manager",
      version: "1.2",
      isPublic: true,
      description: "Q1 maintenance schedule for all properties",
      tags: ["maintenance", "schedule", "q1"],
      thumbnail: null,
    },
    {
      id: "6",
      name: "Tenant Handbook.pdf",
      category: "Tenant Relations",
      property: "All Properties",
      type: "pdf",
      size: "3.2 MB",
      uploadDate: "2023-12-20",
      uploadedBy: "Property Manager",
      version: "3.0",
      isPublic: true,
      description: "Comprehensive tenant handbook and guidelines",
      tags: ["tenant", "handbook", "guidelines"],
      thumbnail: null,
    },
  ];

  const categories = [
    { id: "all", name: "All Categories", count: documents.length },
    {
      id: "Contracts",
      name: "Contracts",
      count: documents.filter((d) => d.category === "Contracts").length,
    },
    {
      id: "Inspections",
      name: "Inspections",
      count: documents.filter((d) => d.category === "Inspections").length,
    },
    {
      id: "Floor Plans",
      name: "Floor Plans",
      count: documents.filter((d) => d.category === "Floor Plans").length,
    },
    {
      id: "Insurance",
      name: "Insurance",
      count: documents.filter((d) => d.category === "Insurance").length,
    },
    {
      id: "Maintenance",
      name: "Maintenance",
      count: documents.filter((d) => d.category === "Maintenance").length,
    },
    {
      id: "Tenant Relations",
      name: "Tenant Relations",
      count: documents.filter((d) => d.category === "Tenant Relations").length,
    },
  ];

  const getFileIcon = (type: string) => {
    switch (type) {
      case "pdf":
        return <FilePdf className="size-8 text-destructive" />;
      case "image":
        return <Image className="size-8 text-primary" />;
      case "spreadsheet":
        return <FileSpreadsheet className="size-8 text-success" />;
      default:
        return <FileText className="size-8 text-muted-foreground" />;
    }
  };

  const getSmallFileIcon = (type: string) => {
    switch (type) {
      case "pdf":
        return <FilePdf className="size-4 text-destructive" />;
      case "image":
        return <Image className="size-4 text-primary" />;
      case "spreadsheet":
        return <FileSpreadsheet className="size-4 text-success" />;
      default:
        return <FileText className="size-4 text-muted-foreground" />;
    }
  };

  const filteredDocuments = documents.filter((doc) => {
    const matchesProperty =
      selectedProperty === "all" ||
      doc.property === selectedProperty ||
      doc.property === "All Properties";
    const matchesCategory = selectedCategory === "all" || doc.category === selectedCategory;
    const matchesSearch =
      searchTerm === "" ||
      doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      doc.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesProperty && matchesCategory && matchesSearch;
  });

  const sortedDocuments = [...filteredDocuments].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case "name":
        comparison = a.name.localeCompare(b.name);
        break;
      case "date":
        comparison = new Date(a.uploadDate).getTime() - new Date(b.uploadDate).getTime();
        break;
      case "size":
        const aSize = parseFloat(a.size.replace(/[^\d.]/g, ""));
        const bSize = parseFloat(b.size.replace(/[^\d.]/g, ""));
        comparison = aSize - bSize;
        break;
    }

    return sortOrder === "asc" ? comparison : -comparison;
  });

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Document Library</h2>
          <p className="text-sm text-muted-foreground">
            Centralized document management for all property-related files
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
            <DialogTrigger asChild>
              <Button>
                <Upload className="mr-2 size-4" />
                Upload Documents
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Upload Documents</DialogTitle>
                <DialogDescription>Add new documents to the library</DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="property">Property</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select property" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Properties</SelectItem>
                      <SelectItem value="sunset">Sunset Apartments</SelectItem>
                      <SelectItem value="downtown">Downtown Lofts</SelectItem>
                      <SelectItem value="garden">Garden View Complex</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories
                        .filter((c) => c.id !== "all")
                        .map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="files">Files</Label>
                  <div className="rounded-lg border-2 border-dashed border-muted-foreground/25 p-6 text-center">
                    <Upload className="mx-auto mb-2 size-8 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">
                      Drag and drop files here, or click to browse
                    </p>
                    <Input type="file" multiple className="hidden" />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsUploadOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsUploadOpen(false)}>Upload</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
        {/* Sidebar - Categories */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full rounded-lg p-2 text-left transition-colors ${
                    selectedCategory === category.id
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted"
                  }`}>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{category.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {category.count}
                    </Badge>
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>

          {/* Document Stats */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-base">Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Total Documents</span>
                <span className="font-medium">{documents.length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Public</span>
                <span className="font-medium">{documents.filter((d) => d.isPublic).length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Private</span>
                <span className="font-medium">{documents.filter((d) => !d.isPublic).length}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Total Size</span>
                <span className="font-medium">12.8 MB</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="lg:col-span-3">
          {/* Filters and Controls */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search documents..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Select value={selectedProperty} onValueChange={setSelectedProperty}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="All Properties" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Properties</SelectItem>
                  <SelectItem value="Sunset Apartments">Sunset Apartments</SelectItem>
                  <SelectItem value="Downtown Lofts">Downtown Lofts</SelectItem>
                  <SelectItem value="Garden View Complex">Garden View Complex</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="date">Date</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="size">Size</SelectItem>
                </SelectContent>
              </Select>

              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}>
                {sortOrder === "asc" ? (
                  <SortAsc className="size-4" />
                ) : (
                  <SortDesc className="size-4" />
                )}
              </Button>

              <div className="flex rounded-lg border">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none">
                  <Grid className="size-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none">
                  <List className="size-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Documents Display */}
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
              {sortedDocuments.map((doc) => (
                <Card key={doc.id} className="cursor-pointer transition-shadow hover:shadow-md">
                  <CardContent className="p-4">
                    <div className="space-y-3">
                      {/* File Icon and Name */}
                      <div className="flex items-start space-x-3">
                        <div className="shrink-0">{getFileIcon(doc.type)}</div>
                        <div className="min-w-0 flex-1">
                          <h4 className="truncate text-sm font-medium" title={doc.name}>
                            {doc.name}
                          </h4>
                          <p className="text-xs text-muted-foreground">{doc.size}</p>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="size-8 p-0">
                              <MoreHorizontal className="size-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 size-4" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="mr-2 size-4" />
                              Download
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 size-4" />
                              Edit Details
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-destructive">
                              <Trash2 className="mr-2 size-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      {/* Category and Privacy */}
                      <div className="flex items-center justify-between">
                        <Badge variant="outline" className="text-xs">
                          {doc.category}
                        </Badge>
                        {doc.isPublic ? (
                          <Unlock className="size-3 text-success" />
                        ) : (
                          <Lock className="size-3 text-destructive" />
                        )}
                      </div>

                      {/* Property and Date */}
                      <div className="space-y-1">
                        <p className="text-xs text-muted-foreground">{doc.property}</p>
                        <p className="text-xs text-muted-foreground">
                          {new Date(doc.uploadDate).toLocaleDateString()}
                        </p>
                      </div>

                      {/* Description */}
                      <p className="line-clamp-2 text-xs text-muted-foreground">
                        {doc.description}
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-0">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Category</TableHead>
                      <TableHead>Property</TableHead>
                      <TableHead>Size</TableHead>
                      <TableHead>Upload Date</TableHead>
                      <TableHead>Uploaded By</TableHead>
                      <TableHead>Privacy</TableHead>
                      <TableHead className="w-[50px]"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {sortedDocuments.map((doc) => (
                      <TableRow key={doc.id}>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getSmallFileIcon(doc.type)}
                            <span className="font-medium">{doc.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{doc.category}</Badge>
                        </TableCell>
                        <TableCell>{doc.property}</TableCell>
                        <TableCell>{doc.size}</TableCell>
                        <TableCell>{new Date(doc.uploadDate).toLocaleDateString()}</TableCell>
                        <TableCell>{doc.uploadedBy}</TableCell>
                        <TableCell>
                          {doc.isPublic ? (
                            <Badge className="bg-success/10 text-success">
                              <Unlock className="mr-1 size-3" />
                              Public
                            </Badge>
                          ) : (
                            <Badge className="bg-destructive/10 text-destructive">
                              <Lock className="mr-1 size-3" />
                              Private
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="size-8 p-0">
                                <MoreHorizontal className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem>
                                <Eye className="mr-2 size-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="mr-2 size-4" />
                                Download
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 size-4" />
                                Edit Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-destructive">
                                <Trash2 className="mr-2 size-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          )}

          {sortedDocuments.length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <FileText className="mx-auto mb-4 size-12 text-muted-foreground" />
                <h3 className="mb-2 text-lg font-medium text-foreground">No documents found</h3>
                <p className="mb-4 text-muted-foreground">
                  Try adjusting your search criteria or upload new documents.
                </p>
                <Button onClick={() => setIsUploadOpen(true)}>
                  <Upload className="mr-2 size-4" />
                  Upload Documents
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
