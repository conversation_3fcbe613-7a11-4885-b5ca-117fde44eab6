// Vietnamese mock data
import { getLayoutById, getLayoutsByPropertyId } from "@/app/api/property-assets/layouts-mock-data";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import type { LayoutData } from "../components/Layout/LayoutViewer";
// Types
import type {
  ApiResponse,
  CreatePropertyLayout,
  PaginatedResponse,
  PropertyLayout,
} from "../types";

// Layout API functions
const layoutsApi = {
  // Get layouts for a property
  getLayouts: async (
    propertyId: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
    }
  ): Promise<PaginatedResponse<LayoutData>> => {
    await new Promise((resolve) => setTimeout(resolve, 600));

    // Get Vietnamese mock layouts for the specific property
    const mockLayouts = getLayoutsByPropertyId(propertyId);

    // If no layouts found for the property, return empty array
    if (!mockLayouts.length) {
      return {
        items: [],
        total: 0,
        page: params?.page || 1,
        limit: params?.limit || 10,
      };
    }

    let filteredLayouts = mockLayouts;

    if (params?.search) {
      filteredLayouts = filteredLayouts.filter((l) =>
        l.name.toLowerCase().includes(params.search!.toLowerCase())
      );
    }

    return {
      items: filteredLayouts,
      total: filteredLayouts.length,
      page: params?.page || 1,
      limit: params?.limit || 10,
    };
  },

  // Get layout by ID
  getLayout: async (id: string): Promise<LayoutData> => {
    await new Promise((resolve) => setTimeout(resolve, 400));

    // Find layout across all properties
    const allPropertyIds = ["prop1", "prop2", "prop3", "prop4", "prop5"];

    for (const propertyId of allPropertyIds) {
      const layout = getLayoutById(propertyId, id);
      if (layout) {
        return layout;
      }
    }

    // Fallback if layout not found
    return {
      id,
      name: "Layout Not Found",
      imageUrl:
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgZmlsbD0iI2YwZjBmMCIgc3Ryb2tlPSIjZGRkIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8dGV4dCB4PSI0MDAiIHk9IjMwMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zNWVtIj5MYXlvdXQgTm90IEZvdW5kPC90ZXh0Pgo8L3N2Zz4=",
      width: 800,
      height: 600,
      unitPositions: [],
      scale: 1,
      offsetX: 0,
      offsetY: 0,
    };
  },

  // Create layout
  createLayout: async (
    propertyId: string,
    data: {
      name: string;
      description?: string;
      floorNumber?: number;
      imageUrl?: string;
      imageWidth: number;
      imageHeight: number;
    }
  ): Promise<LayoutData> => {
    await new Promise((resolve) => setTimeout(resolve, 1500));

    return {
      id: `layout_${Date.now()}`,
      name: data.name,
      imageUrl:
        data.imageUrl ||
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICA8cmVjdCB3aWR0aD0iODAwIiBoZWlnaHQ9IjYwMCIgZmlsbD0iI2YwZjBmMCIgc3Ryb2tlPSIjZGRkIiBzdHJva2Utd2lkdGg9IjIiLz4KICA8dGV4dCB4PSI0MDAiIHk9IjMwMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iIzY2NiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zNWVtIj5OZXcgTGF5b3V0PC90ZXh0Pgo8L3N2Zz4=",
      width: data.imageWidth,
      height: data.imageHeight,
      unitPositions: [],
      scale: 1,
      offsetX: 0,
      offsetY: 0,
    };
  },

  // Update layout
  updateLayout: async (id: string, data: Partial<LayoutData>): Promise<LayoutData> => {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const existing = await layoutsApi.getLayout(id);
    return {
      ...existing,
      ...data,
    };
  },

  // Delete layout
  deleteLayout: async (id: string): Promise<void> => {
    await new Promise((resolve) => setTimeout(resolve, 800));
  },

  // Upload layout image
  uploadLayoutImage: async (
    file: File
  ): Promise<{ url: string; width: number; height: number }> => {
    await new Promise((resolve) => setTimeout(resolve, 2000));

    // Mock image upload - in real app would upload to cloud storage
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = () => {
        const img = new Image();
        img.onload = () => {
          resolve({
            url: reader.result as string,
            width: img.width,
            height: img.height,
          });
        };
        img.src = reader.result as string;
      };
      reader.readAsDataURL(file);
    });
  },

  // Save unit positions
  saveUnitPositions: async (
    layoutId: string,
    unitPositions: LayoutData["unitPositions"]
  ): Promise<LayoutData> => {
    await new Promise((resolve) => setTimeout(resolve, 800));

    const existing = await layoutsApi.getLayout(layoutId);
    return {
      ...existing,
      unitPositions,
    };
  },
};

// Query keys
export const layoutsKeys = {
  all: ["layouts"] as const,
  lists: () => [...layoutsKeys.all, "list"] as const,
  list: (propertyId: string, params?: any) => [...layoutsKeys.lists(), propertyId, params] as const,
  details: () => [...layoutsKeys.all, "detail"] as const,
  detail: (id: string) => [...layoutsKeys.details(), id] as const,
};

// Hooks
export const useLayouts = (
  propertyId: string,
  params?: {
    page?: number;
    limit?: number;
    search?: string;
  },
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: layoutsKeys.list(propertyId, params),
    queryFn: () => layoutsApi.getLayouts(propertyId, params),
    enabled: enabled && !!propertyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useLayout = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: layoutsKeys.detail(id),
    queryFn: () => layoutsApi.getLayout(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateLayout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      propertyId,
      data,
    }: {
      propertyId: string;
      data: {
        name: string;
        description?: string;
        floorNumber?: number;
        imageUrl?: string;
        imageWidth: number;
        imageHeight: number;
      };
    }) => layoutsApi.createLayout(propertyId, data),
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: layoutsKeys.list(variables.propertyId) });
      toast.success("Tạo layout thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi tạo layout: ${error.message}`);
    },
  });
};

export const useUpdateLayout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<LayoutData> }) =>
      layoutsApi.updateLayout(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: layoutsKeys.lists() });
      queryClient.setQueryData(layoutsKeys.detail(data.id), data);
      toast.success("Cập nhật layout thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi cập nhật layout: ${error.message}`);
    },
  });
};

export const useDeleteLayout = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: layoutsApi.deleteLayout,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: layoutsKeys.lists() });
      toast.success("Xóa layout thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi xóa layout: ${error.message}`);
    },
  });
};

export const useUploadLayoutImage = () => {
  return useMutation({
    mutationFn: layoutsApi.uploadLayoutImage,
    onError: (error: Error) => {
      toast.error(`Lỗi tải ảnh: ${error.message}`);
    },
  });
};

export const useSaveUnitPositions = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      layoutId,
      unitPositions,
    }: {
      layoutId: string;
      unitPositions: LayoutData["unitPositions"];
    }) => layoutsApi.saveUnitPositions(layoutId, unitPositions),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: layoutsKeys.lists() });
      queryClient.setQueryData(layoutsKeys.detail(data.id), data);
      toast.success("Lưu vị trí đơn vị thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi lưu vị trí đơn vị: ${error.message}`);
    },
  });
};
