import { NextRequest, NextResponse } from "next/server";

import type { CreatePayment, Payment } from "@/features/property-assets/types";

import { paymentsMockData } from "../mock-data";

// GET /api/property-assets/payments
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") as Payment["status"] | null;
    const payment_type = searchParams.get("payment_type") as Payment["payment_type"] | null;
    const payment_method = searchParams.get("payment_method") as Payment["payment_method"] | null;
    const contract_id = searchParams.get("contract_id") || "";
    const start_date = searchParams.get("start_date") || "";
    const end_date = searchParams.get("end_date") || "";

    let filteredPayments = [...paymentsMockData];

    // Apply contract filter
    if (contract_id) {
      filteredPayments = filteredPayments.filter((payment) => payment.contract_id === contract_id);
    }

    // Apply date range filter
    if (start_date) {
      filteredPayments = filteredPayments.filter(
        (payment) => new Date(payment.payment_date) >= new Date(start_date)
      );
    }
    if (end_date) {
      filteredPayments = filteredPayments.filter(
        (payment) => new Date(payment.payment_date) <= new Date(end_date)
      );
    }

    // Apply search filter
    if (search) {
      filteredPayments = filteredPayments.filter(
        (payment) =>
          payment.id.toLowerCase().includes(search.toLowerCase()) ||
          payment.contract_id.toLowerCase().includes(search.toLowerCase()) ||
          (payment.description &&
            payment.description.toLowerCase().includes(search.toLowerCase())) ||
          (payment.reference_number &&
            payment.reference_number.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply status filter
    if (status) {
      filteredPayments = filteredPayments.filter((payment) => payment.status === status);
    }

    // Apply payment type filter
    if (payment_type) {
      filteredPayments = filteredPayments.filter(
        (payment) => payment.payment_type === payment_type
      );
    }

    // Apply payment method filter
    if (payment_method) {
      filteredPayments = filteredPayments.filter(
        (payment) => payment.payment_method === payment_method
      );
    }

    // Sort by payment date (newest first)
    filteredPayments.sort(
      (a, b) => new Date(b.payment_date).getTime() - new Date(a.payment_date).getTime()
    );

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedPayments = filteredPayments.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 320));

    return NextResponse.json({
      items: paginatedPayments,
      total: filteredPayments.length,
      page,
      limit,
      totalPages: Math.ceil(filteredPayments.length / limit),
    });
  } catch (error) {
    console.error("Error fetching payments:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/property-assets/payments
export async function POST(request: NextRequest) {
  try {
    const body: CreatePayment = await request.json();

    // Validate required fields
    if (
      !body.contract_id ||
      !body.amount ||
      !body.payment_date ||
      !body.payment_method ||
      !body.payment_type
    ) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Validate amount is positive
    if (body.amount <= 0) {
      return NextResponse.json({ error: "Payment amount must be positive" }, { status: 400 });
    }

    // Validate payment date is not in the future
    const paymentDate = new Date(body.payment_date);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today

    if (paymentDate > today) {
      return NextResponse.json({ error: "Payment date cannot be in the future" }, { status: 400 });
    }

    // Create new payment
    const newPayment: Payment = {
      id: `payment_${Date.now()}`,
      ...body,
      status: "completed", // Default status for new payments
      reference_number: body.reference_number || `REF${Date.now()}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company1", // Mock company ID
    };

    // Add to mock data
    paymentsMockData.push(newPayment);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 480));

    return NextResponse.json({
      data: newPayment,
      success: true,
      message: "Payment created successfully",
    });
  } catch (error) {
    console.error("Error creating payment:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
