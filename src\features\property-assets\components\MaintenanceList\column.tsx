import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import { DateColumn, TextColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { authProtectedPaths } from "@/constants/paths";

import { MaintenanceRequest } from "../../types";

const ActionGroupMaintenance = ({
  useDeleteMaintenanceMutation,
  row,
  isDeleting,
}: {
  useDeleteMaintenanceMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<MaintenanceRequest>;
}) => {
  const router = useRouter();
  const maintenance = row.original;
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(authProtectedPaths.MAINTENANCE_ID.replace(":id", maintenance.id) as any);
    });
  }, [router, maintenance.id]);

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.MAINTENANCE_ID.replace(":id", maintenance.id) as any);
  }, [router, maintenance.id]);

  const handleDelete = useCallback(async () => {
    return useDeleteMaintenanceMutation.mutateAsync(maintenance.id);
  }, [useDeleteMaintenanceMutation, maintenance.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};

export const columns = (
  useDeleteMaintenanceMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<MaintenanceRequest>[] => [
  {
    id: "request",
    accessorKey: "request",
    header: t("pages.maintenance.headers.requestInfo"),
    sorter: true,
    isMainColumn: true,
    sortKey: "title",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => {
      const maintenance = row.original;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={maintenance?.title} className="font-medium" />
          <TextColumn
            text={`#${maintenance?.id?.slice(-8)}`}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "property",
    accessorKey: "property",
    header: t("pages.maintenance.headers.property"),
    sorter: true,
    sortKey: "property_id",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => {
      const maintenance = row.original;
      const propertyName = maintenance?.property?.name || "N/A";
      const unitNumber = maintenance?.unit?.unit_number;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={propertyName} className="font-medium" />
          <TextColumn
            text={unitNumber ? `Unit: ${unitNumber}` : "Property-wide"}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "requester",
    accessorKey: "requester",
    header: t("pages.maintenance.headers.requester"),
    sorter: true,
    sortKey: "tenant_id",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => {
      const maintenance = row.original;
      const tenantName = maintenance?.tenant
        ? `${maintenance.tenant.first_name} ${maintenance.tenant.last_name}`
        : "Manager";
      const requestType = maintenance?.tenant_id ? "Tenant" : "Management";
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={tenantName} className="font-medium" />
          <TextColumn text={requestType} className="text-xs text-muted-foreground opacity-60" />
        </div>
      );
    },
  },
  {
    id: "category",
    accessorKey: "category",
    header: t("pages.maintenance.headers.category"),
    sorter: true,
    sortKey: "category",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => {
      const maintenance = row.original;
      const category = maintenance?.category || "general";
      return (
        <TextColumn text={t(`pages.maintenance.categories.${category}`)} className="capitalize" />
      );
    },
  },
  {
    id: "priority",
    accessorKey: "priority",
    header: t("pages.maintenance.headers.priority"),
    sorter: true,
    sortKey: "priority",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => {
      const maintenance = row.original;
      const priority = maintenance?.priority || "medium";
      return (
        <TextColumn text={t(`pages.maintenance.priorities.${priority}`)} className="capitalize" />
      );
    },
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("pages.maintenance.headers.status"),
    sorter: true,
    sortKey: "status",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => {
      const maintenance = row.original;
      const status = maintenance?.status || "pending";
      return <TextColumn text={t(`pages.maintenance.status.${status}`)} className="capitalize" />;
    },
  },
  {
    id: "assignedTo",
    accessorKey: "assignedTo",
    header: t("pages.maintenance.headers.assignedTo"),
    sorter: true,
    sortKey: "contractor_name",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => {
      const maintenance = row.original;
      const contractorName = maintenance?.contractor_name || "Unassigned";
      const contractorPhone = maintenance?.contractor_phone || "";
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={contractorName} className="font-medium" />
          <TextColumn text={contractorPhone} className="text-xs text-muted-foreground opacity-60" />
        </div>
      );
    },
  },
  {
    id: "estimatedCost",
    accessorKey: "estimatedCost",
    header: t("pages.maintenance.headers.estimatedCost"),
    sorter: true,
    sortKey: "estimated_cost",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => {
      const maintenance = row.original;
      const estimatedCost = maintenance?.estimated_cost
        ? `$${maintenance.estimated_cost.toLocaleString()}`
        : "N/A";
      const actualCost = maintenance?.actual_cost
        ? `$${maintenance.actual_cost.toLocaleString()}`
        : "";
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={estimatedCost} className="font-medium" />
          <TextColumn
            text={actualCost ? `Actual: ${actualCost}` : ""}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "dueDate",
    accessorKey: "dueDate",
    header: t("pages.maintenance.headers.dueDate"),
    sorter: true,
    sortKey: "scheduled_date",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => (
      <DateColumn date={row.original.scheduled_date} />
    ),
  },
  {
    id: "created_at",
    accessorKey: "created_at",
    header: t("pages.maintenance.headers.createdAt"),
    sorter: true,
    sortKey: "created_at",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => (
      <DateColumn date={row.original.created_at} />
    ),
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.maintenance.headers.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => (
      <DateColumn date={row.original.updated_at} />
    ),
  },
  {
    id: "actions",
    header: t("pages.maintenance.headers.actions"),
    cell: ({ row }: { row: Row<MaintenanceRequest> }) => (
      <ActionGroupMaintenance
        useDeleteMaintenanceMutation={useDeleteMaintenanceMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];
