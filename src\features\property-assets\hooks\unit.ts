import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { unitApi } from "@/lib/apis/property-assets";

import type { CreateUnit, Unit } from "../types";
import { propertyKeys, unitKeys, type IGetUnitsParams } from "./keys";

// List Units Hook
export const useUnits = (params?: IGetUnitsParams) => {
  return useQuery({
    queryKey: unitKeys.list(params || {}),
    queryFn: () => unitApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Unit by ID Hook
export const useUnit = (id: string) => {
  return useQuery({
    queryKey: unitKeys.detail(id),
    queryFn: () => unitApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Units by Property Hook
export const usePropertyUnits = (propertyId: string, params?: IGetUnitsParams) => {
  return useQuery({
    queryKey: propertyKeys.units(propertyId),
    queryFn: () => unitApi.getByProperty(propertyId, params),
    enabled: !!propertyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create Unit Hook
export const useCreateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUnit) => unitApi.create(data),
    onSuccess: (data) => {
      // Invalidate and refetch units list
      queryClient.invalidateQueries({ queryKey: unitKeys.lists() });

      // Invalidate property units if property_id is available
      if (data.data.data.property_id) {
        queryClient.invalidateQueries({
          queryKey: propertyKeys.units(data.data.data.property_id),
        });
      }

      // Add the new unit to the cache
      queryClient.setQueryData(unitKeys.detail(data.data.data.id), data);
    },
    onError: (error: any) => {
      throw new Error(error?.response?.data?.message || "Failed to create unit");
    },
  });
};

// Update Unit Hook
export const useUpdateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: { id: string } & Partial<CreateUnit>) => {
      const { id, ...updateData } = data;
      return unitApi.update(id, updateData);
    },
    onSuccess: (data, variables) => {
      // Invalidate and refetch units list
      queryClient.invalidateQueries({ queryKey: unitKeys.lists() });

      // Update the specific unit in cache
      queryClient.setQueryData(unitKeys.detail(variables.id), data);

      // Invalidate property units if property_id is available
      if (data.data.data.property_id) {
        queryClient.invalidateQueries({
          queryKey: propertyKeys.units(data.data.data.property_id),
        });
      }
    },
    onError: (error: any) => {
      throw new Error(error?.response?.data?.message || "Failed to update unit");
    },
  });
};

// Delete Unit Hook
export const useDeleteUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => unitApi.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: unitKeys.detail(id) });

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: unitKeys.lists() });
      queryClient.invalidateQueries({ queryKey: propertyKeys.all() });

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};
