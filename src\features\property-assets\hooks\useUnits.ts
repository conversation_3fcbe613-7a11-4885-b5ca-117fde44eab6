import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
import type { ApiResponse, CreateUnit, PaginatedResponse, Unit } from "../types";

// API functions (connecting to Vietnamese units API endpoints)
const unitsApi = {
  // Get all units
  getUnits: async (
    propertyId?: string,
    params?: {
      page?: number;
      limit?: number;
      search?: string;
      status?: Unit["status"];
      unit_type?: Unit["unit_type"];
    }
  ): Promise<PaginatedResponse<Unit>> => {
    const searchParams = new URLSearchParams();

    if (propertyId) searchParams.append("propertyId", propertyId);
    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.limit) searchParams.append("limit", params.limit.toString());
    if (params?.search) searchParams.append("search", params.search);
    if (params?.status) searchParams.append("status", params.status);
    if (params?.unit_type) searchParams.append("unit_type", params.unit_type);

    const response = await fetch(`/api/property-assets/units?${searchParams.toString()}`);
    if (!response.ok) {
      throw new Error("Failed to fetch units");
    }

    return response.json();
  },

  // Get unit by ID
  getUnit: async (id: string): Promise<Unit> => {
    const response = await fetch(`/api/property-assets/units/${id}`);
    if (!response.ok) {
      throw new Error("Failed to fetch unit");
    }

    const result = await response.json();
    return result.data;
  },

  // Create unit
  createUnit: async (data: CreateUnit): Promise<Unit> => {
    const response = await fetch("/api/property-assets/units", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to create unit");
    }

    const result = await response.json();
    return result.data;
  },

  // Update unit
  updateUnit: async (id: string, data: Partial<CreateUnit>): Promise<Unit> => {
    const response = await fetch(`/api/property-assets/units/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update unit");
    }

    const result = await response.json();
    return result.data;
  },

  // Delete unit
  deleteUnit: async (id: string): Promise<void> => {
    const response = await fetch(`/api/property-assets/units/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error("Failed to delete unit");
    }
  },

  // Bulk update units
  bulkUpdateUnits: async (
    updates: Array<{ id: string; data: Partial<CreateUnit> }>
  ): Promise<Unit[]> => {
    const response = await fetch("/api/property-assets/units/bulk", {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ updates }),
    });

    if (!response.ok) {
      throw new Error("Failed to bulk update units");
    }

    const result = await response.json();
    return result.data;
  },
};

// Query keys
export const unitsKeys = {
  all: ["units"] as const,
  lists: () => [...unitsKeys.all, "list"] as const,
  list: (propertyId: string, params?: any) => [...unitsKeys.lists(), propertyId, params] as const,
  details: () => [...unitsKeys.all, "detail"] as const,
  detail: (id: string) => [...unitsKeys.details(), id] as const,
};

// Hooks
export const useUnits = (
  propertyId?: string,
  params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: Unit["status"];
    unit_type?: Unit["unit_type"];
  },
  enabled: boolean = true
) => {
  return useQuery({
    queryKey: unitsKeys.list(propertyId || "all", params),
    queryFn: () => unitsApi.getUnits(propertyId, params),
    enabled: enabled,
    staleTime: 3 * 60 * 1000, // 3 minutes
  });
};

export const useUnit = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: unitsKeys.detail(id),
    queryFn: () => unitsApi.getUnit(id),
    enabled: enabled && !!id,
    staleTime: 3 * 60 * 1000,
  });
};

export const useCreateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: unitsApi.createUnit,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: unitsKeys.lists() });
      toast.success("Tạo đơn vị thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi tạo đơn vị: ${error.message}`);
    },
  });
};

export const useUpdateUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateUnit> }) =>
      unitsApi.updateUnit(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: unitsKeys.lists() });
      queryClient.setQueryData(unitsKeys.detail(data.id), data);
      toast.success("Cập nhật đơn vị thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi cập nhật đơn vị: ${error.message}`);
    },
  });
};

export const useDeleteUnit = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: unitsApi.deleteUnit,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: unitsKeys.lists() });
      toast.success("Xóa đơn vị thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi xóa đơn vị: ${error.message}`);
    },
  });
};

export const useBulkUpdateUnits = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: unitsApi.bulkUpdateUnits,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: unitsKeys.lists() });
      toast.success("Cập nhật đơn vị thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi cập nhật đơn vị: ${error.message}`);
    },
  });
};
