"use client";

import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";

interface BasicInformationSectionProps {
  form: any;
}

export function BasicInformationSection({ form }: BasicInformationSectionProps) {
  const { t } = useTranslation();

  // Property type options
  const propertyTypeOptions = [
    { value: "residential", label: t("pages.properties.types.residential") },
    { value: "commercial", label: t("pages.properties.types.commercial") },
    { value: "mixed", label: t("pages.properties.types.mixed") },
  ];

  return (
    <Card className="border-border shadow-sm">
      <CardHeader className="border-b border-border bg-muted/50 py-3">
        <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
          <div className="size-2 rounded-full bg-primary"></div>
          {t("pages.properties.basicInformation")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 bg-card p-4">
        {/* Property Name */}
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.headers.name")} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.properties.placeholders.name")}
                  className="h-9"
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Property Type */}
        <FormField
          control={form.control}
          name="property_type"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.headers.type")} <span className="text-destructive">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="h-9 border-input focus:border-primary focus:ring-ring">
                    <SelectValue placeholder={t("pages.properties.placeholders.type")} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {propertyTypeOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Description */}
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.headers.description")}
              </FormLabel>
              <FormControl>
                <Textarea
                  placeholder={t("pages.properties.placeholders.description")}
                  rows={3}
                  className="resize-none border-input focus:border-primary focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
