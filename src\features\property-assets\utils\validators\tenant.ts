import { z } from "zod";

export const createTenantSchema = z.object({
  first_name: z.string().min(1, "validation.firstNameRequired"),
  last_name: z.string().min(1, "validation.lastNameRequired"),
  email: z.string().email("validation.invalidEmail"),
  phone: z.string().min(1, "validation.phoneRequired"),
  emergency_contact_name: z.string().optional(),
  emergency_contact_phone: z.string().optional(),
  identification_type: z.enum(["passport", "driver_license", "national_id"], {
    errorMap: () => ({ message: "validation.identificationTypeRequired" }),
  }),
  identification_number: z.string().min(1, "validation.identificationNumberRequired"),
  date_of_birth: z.string().optional(),
  employment_status: z.enum(["employed", "self_employed", "student", "unemployed", "retired"], {
    errorMap: () => ({ message: "validation.employmentStatusRequired" }),
  }),
  employer_name: z.string().optional(),
  monthly_income: z.number().positive("validation.monthlyIncomeMustBePositive").optional(),
  documents: z
    .array(
      z.object({
        document_type: z.string(),
        file_name: z.string(),
        file_data: z.string(),
      })
    )
    .optional(),
});

export const updateTenantSchema = createTenantSchema.partial().extend({
  id: z.string().min(1, "validation.idRequired"),
});

export const tenantFilterSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  query: z.string().optional(),
  status: z.enum(["active", "inactive", "blacklisted"]).optional(),
  employment_status: z
    .enum(["employed", "self_employed", "student", "unemployed", "retired"])
    .optional(),
});

export type CreateTenantFormValues = z.infer<typeof createTenantSchema>;
export type UpdateTenantFormValues = z.infer<typeof updateTenantSchema>;
export type TenantFilterValues = z.infer<typeof tenantFilterSchema>;
