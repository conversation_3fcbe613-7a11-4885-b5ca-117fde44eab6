"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2, Upload, X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// Form and validation
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { ImageUpload } from "@/components/ui/image-upload";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { authProtectedPaths } from "@/constants/paths";

import {
  useCreateMaintenanceRequest,
  useUpdateMaintenanceRequest,
} from "../../hooks/useMaintenance";
import { useProperties } from "../../hooks/useProperties";
import { useTenants } from "../../hooks/useTenants";
import { useUnits } from "../../hooks/useUnits";
import type { MaintenanceRequest } from "../../types";
// Maintenance specific imports
import {
  createMaintenanceRequestSchema,
  updateMaintenanceRequestSchema,
  type CreateMaintenanceRequestFormValues,
  type UpdateMaintenanceRequestFormValues,
} from "../../utils/validators/maintenance";

interface MaintenanceFormProps {
  initialData?: MaintenanceRequest;
  isEditing?: boolean;
  preselectedPropertyId?: string;
  preselectedUnitId?: string;
  preselectedTenantId?: string;
}

export function MaintenanceForm({
  initialData,
  isEditing = false,
  preselectedPropertyId,
  preselectedUnitId,
  preselectedTenantId,
}: MaintenanceFormProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>(
    preselectedPropertyId || initialData?.property_id || ""
  );
  const [uploadedImages, setUploadedImages] = useState<{ name: string; image: string }[]>([]);

  // Data hooks
  const { data: propertiesData } = useProperties({ limit: 1000 });
  const { data: unitsData } = useUnits(selectedPropertyId, { limit: 1000 });
  const { data: tenantsData } = useTenants({ limit: 1000 });

  // Extract arrays from response data
  const properties = propertiesData?.items || [];
  const units = unitsData?.items || [];
  const tenants = tenantsData?.items || [];

  // Mutations
  const createMaintenanceMutation = useCreateMaintenanceRequest();
  const updateMaintenanceMutation = useUpdateMaintenanceRequest();

  // Form setup
  const form = useZodForm({
    schema: isEditing ? updateMaintenanceRequestSchema : createMaintenanceRequestSchema,
    defaultValues:
      isEditing && initialData
        ? {
            id: initialData.id,
            property_id: initialData.property_id,
            unit_id: initialData.unit_id || "property-wide",
            tenant_id: initialData.tenant_id || "no-tenant",
            title: initialData.title,
            description: initialData.description,
            priority: initialData.priority,
            category: initialData.category,
            scheduled_date: initialData.scheduled_date || "",
            estimated_cost: initialData.estimated_cost || undefined,
            contractor_name: initialData.contractor_name || "",
            contractor_phone: initialData.contractor_phone || "",
            notes: initialData.notes || "",
            status: initialData.status,
            completed_date: initialData.completed_date || "",
            actual_cost: initialData.actual_cost || undefined,
          }
        : {
            property_id: preselectedPropertyId || "",
            unit_id: preselectedUnitId || "property-wide",
            tenant_id: preselectedTenantId || "no-tenant",
            title: "",
            description: "",
            priority: "medium" as const,
            category: "other" as const,
            scheduled_date: "",
            estimated_cost: undefined,
            contractor_name: "",
            contractor_phone: "",
            notes: "",
            images: [],
          },
  });

  const watchedPropertyId = form.watch("property_id");

  // Update selectedPropertyId when form value changes
  useEffect(() => {
    if (watchedPropertyId !== selectedPropertyId) {
      setSelectedPropertyId(watchedPropertyId);
      // Reset unit selection when property changes
      if (!isEditing) {
        form.setValue("unit_id", "property-wide");
      }
    }
  }, [watchedPropertyId, selectedPropertyId, form, isEditing]);

  const onSubmit = useCallback(
    async (values: CreateMaintenanceRequestFormValues | UpdateMaintenanceRequestFormValues) => {
      if (isSubmitting) return;

      setIsSubmitting(true);
      try {
        // Add uploaded images to form data
        const formData = {
          ...values,
          images: uploadedImages,
        };

        if (isEditing && "id" in values) {
          await updateMaintenanceMutation.mutateAsync({
            id: values.id,
            data: formData,
          } as any);
          toast.success(t("pages.maintenance.messages.updateSuccess"));
          router.push(authProtectedPaths.MAINTENANCE_ID.replace(":id", values.id) as any);
        } else {
          const result = await createMaintenanceMutation.mutateAsync(
            formData as CreateMaintenanceRequestFormValues
          );
          toast.success(t("pages.maintenance.messages.createSuccess"));
          router.push(authProtectedPaths.MAINTENANCE_ID.replace(":id", result.id) as any);
        }
      } catch (error) {
        // TODO: Implement proper error logging
        toast.error(
          isEditing
            ? t("pages.maintenance.messages.updateError")
            : t("pages.maintenance.messages.createError")
        );
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      isSubmitting,
      isEditing,
      updateMaintenanceMutation,
      createMaintenanceMutation,
      t,
      router,
      uploadedImages,
    ]
  );

  const handleImageUpload = useCallback((images: { name: string; image: string }[]) => {
    setUploadedImages(images);
  }, []);

  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <div className="flex-none border-b bg-card">
        <div className="px-6 py-4">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              {isEditing
                ? t("pages.maintenance.editRequest")
                : t("pages.maintenance.createRequest")}
            </h1>
            <p className="mt-1 text-sm text-muted-foreground">
              {isEditing
                ? t("pages.maintenance.editDescription")
                : t("pages.maintenance.createDescription")}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 bg-background p-6">
        <Form {...form}>
          <form id="maintenance-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Property and Unit Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.maintenance.sections.propertyUnit")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="property_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("pages.maintenance.fields.property")}
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className="border-input bg-background text-foreground">
                            <SelectValue
                              placeholder={t("pages.maintenance.placeholders.property")}
                            />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {properties.map((property) => (
                            <SelectItem key={property.id} value={property.id}>
                              {property.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="unit_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("pages.maintenance.fields.unit")}
                      </FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                        disabled={!selectedPropertyId}>
                        <FormControl>
                          <SelectTrigger className="border-input bg-background text-foreground">
                            <SelectValue placeholder={t("pages.maintenance.placeholders.unit")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="property-wide">
                            {t("pages.maintenance.options.propertyWide")}
                          </SelectItem>
                          {units.map((unit) => (
                            <SelectItem key={unit.id} value={unit.id}>
                              {unit.unit_number} - {unit.unit_type}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="tenant_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("pages.maintenance.fields.tenant")}
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className="border-input bg-background text-foreground">
                            <SelectValue placeholder={t("pages.maintenance.placeholders.tenant")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="no-tenant">
                            {t("pages.maintenance.options.noTenant")}
                          </SelectItem>
                          {tenants.map((tenant) => (
                            <SelectItem key={tenant.id} value={tenant.id}>
                              {tenant.first_name} {tenant.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Request Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.maintenance.sections.requestDetails")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("pages.maintenance.fields.title")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("pages.maintenance.placeholders.title")}
                          className="border-input bg-background text-foreground"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("pages.maintenance.fields.description")}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t("pages.maintenance.placeholders.description")}
                          className="min-h-[120px] border-input bg-background text-foreground"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="priority"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.maintenance.fields.priority")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue
                                placeholder={t("pages.maintenance.placeholders.priority")}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="low">
                              {t("pages.maintenance.priorities.low")}
                            </SelectItem>
                            <SelectItem value="medium">
                              {t("pages.maintenance.priorities.medium")}
                            </SelectItem>
                            <SelectItem value="high">
                              {t("pages.maintenance.priorities.high")}
                            </SelectItem>
                            <SelectItem value="urgent">
                              {t("pages.maintenance.priorities.urgent")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.maintenance.fields.category")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue
                                placeholder={t("pages.maintenance.placeholders.category")}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="plumbing">
                              {t("pages.maintenance.categories.plumbing")}
                            </SelectItem>
                            <SelectItem value="electrical">
                              {t("pages.maintenance.categories.electrical")}
                            </SelectItem>
                            <SelectItem value="hvac">
                              {t("pages.maintenance.categories.hvac")}
                            </SelectItem>
                            <SelectItem value="appliance">
                              {t("pages.maintenance.categories.appliance")}
                            </SelectItem>
                            <SelectItem value="structural">
                              {t("pages.maintenance.categories.structural")}
                            </SelectItem>
                            <SelectItem value="cosmetic">
                              {t("pages.maintenance.categories.cosmetic")}
                            </SelectItem>
                            <SelectItem value="other">
                              {t("pages.maintenance.categories.other")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {isEditing && (
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.maintenance.fields.status")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue
                                placeholder={t("pages.maintenance.placeholders.status")}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="open">
                              {t("pages.maintenance.status.open")}
                            </SelectItem>
                            <SelectItem value="in_progress">
                              {t("pages.maintenance.status.inProgress")}
                            </SelectItem>
                            <SelectItem value="completed">
                              {t("pages.maintenance.status.completed")}
                            </SelectItem>
                            <SelectItem value="cancelled">
                              {t("pages.maintenance.status.cancelled")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}
              </CardContent>
            </Card>

            {/* Scheduling and Contractor */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.maintenance.sections.scheduling")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="scheduled_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.maintenance.fields.scheduledDate")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {isEditing && (
                    <FormField
                      control={form.control}
                      name="completed_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-foreground">
                            {t("pages.maintenance.fields.completedDate")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="date"
                              className="border-input bg-background text-foreground"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="contractor_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.maintenance.fields.contractorName")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("pages.maintenance.placeholders.contractorName")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="contractor_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.maintenance.fields.contractorPhone")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("pages.maintenance.placeholders.contractorPhone")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Cost Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.maintenance.sections.costInfo")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="estimated_cost"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.maintenance.fields.estimatedCost")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={t("pages.maintenance.placeholders.estimatedCost")}
                            className="border-input bg-background text-foreground"
                            {...field}
                            onChange={(e) =>
                              field.onChange(
                                e.target.value ? parseFloat(e.target.value) : undefined
                              )
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {isEditing && (
                    <FormField
                      control={form.control}
                      name="actual_cost"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-foreground">
                            {t("pages.maintenance.fields.actualCost")}
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder={t("pages.maintenance.placeholders.actualCost")}
                              className="border-input bg-background text-foreground"
                              {...field}
                              onChange={(e) =>
                                field.onChange(
                                  e.target.value ? parseFloat(e.target.value) : undefined
                                )
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Additional Notes and Images */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.maintenance.sections.additional")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="notes"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("pages.maintenance.fields.notes")}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t("pages.maintenance.placeholders.notes")}
                          className="min-h-[120px] border-input bg-background text-foreground"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div>
                  <FormLabel className="text-foreground">
                    {t("pages.maintenance.fields.images")}
                  </FormLabel>
                  <ImageUpload value={null} onChange={handleImageUpload as any} className="mt-2" />
                </div>
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>

      {/* Sticky Footer - Following OneX ERP Standard - Full Width */}
      <div className="sticky bottom-0 z-50 w-full flex-none border-t border-border bg-card shadow-lg">
        <div className="mx-auto max-w-7xl px-6 py-4">
          <div className="flex justify-end">
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                className="border-border bg-background px-6 text-foreground hover:bg-muted">
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                form="maintenance-form"
                className="bg-primary px-6 text-primary-foreground hover:bg-primary-hover disabled:opacity-50">
                {isSubmitting && <Loader2 className="mr-2 size-4 animate-spin" />}
                {isEditing ? t("common.update") : t("common.create")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
