import { NextRequest, NextResponse } from "next/server";

import type {
  CreateMaintenanceRequest,
  MaintenanceRequest,
} from "@/features/property-assets/types";

import { maintenanceRequestsMockData } from "../../mock-data";

// GET /api/property-assets/maintenance/[id]
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const request_data = maintenanceRequestsMockData.find((r) => r.id === id);

    if (!request_data) {
      return NextResponse.json({ error: "Maintenance request not found" }, { status: 404 });
    }

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 200));

    return NextResponse.json({
      data: request_data,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching maintenance request:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PUT /api/property-assets/maintenance/[id]
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    const body: Partial<MaintenanceRequest> = await request.json();

    const requestIndex = maintenanceRequestsMockData.findIndex((r) => r.id === id);

    if (requestIndex === -1) {
      return NextResponse.json({ error: "Maintenance request not found" }, { status: 404 });
    }

    // Validate estimated cost and actual cost if provided
    if (body.estimated_cost !== undefined && body.estimated_cost < 0) {
      return NextResponse.json({ error: "Estimated cost cannot be negative" }, { status: 400 });
    }

    if (body.actual_cost !== undefined && body.actual_cost < 0) {
      return NextResponse.json({ error: "Actual cost cannot be negative" }, { status: 400 });
    }

    // Validate date logic if dates are being updated
    const currentRequest = maintenanceRequestsMockData[requestIndex];
    const reportedDate = new Date(body.reported_date || currentRequest.reported_date);

    if (body.scheduled_date) {
      const scheduledDate = new Date(body.scheduled_date);
      if (scheduledDate < reportedDate) {
        return NextResponse.json(
          { error: "Scheduled date cannot be before reported date" },
          { status: 400 }
        );
      }
    }

    if (body.completed_date) {
      const completedDate = new Date(body.completed_date);
      if (completedDate < reportedDate) {
        return NextResponse.json(
          { error: "Completed date cannot be before reported date" },
          { status: 400 }
        );
      }
    }

    // Auto-update status based on completed_date
    const updatedBody = { ...body };
    if (body.completed_date && !body.status) {
      updatedBody.status = "completed";
    } else if (body.status === "completed" && !body.completed_date) {
      updatedBody.completed_date = new Date().toISOString().split("T")[0];
    }

    // Update maintenance request
    const updatedRequest: MaintenanceRequest = {
      ...maintenanceRequestsMockData[requestIndex],
      ...updatedBody,
      updated_at: new Date().toISOString(),
    };

    maintenanceRequestsMockData[requestIndex] = updatedRequest;

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 420));

    return NextResponse.json({
      data: updatedRequest,
      success: true,
      message: "Maintenance request updated successfully",
    });
  } catch (error) {
    console.error("Error updating maintenance request:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE /api/property-assets/maintenance/[id]
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const requestIndex = maintenanceRequestsMockData.findIndex((r) => r.id === id);

    if (requestIndex === -1) {
      return NextResponse.json({ error: "Maintenance request not found" }, { status: 404 });
    }

    // Check if request is in progress (you might want to restrict this)
    const maintenanceRequest = maintenanceRequestsMockData[requestIndex];
    if (maintenanceRequest.status === "in_progress") {
      return NextResponse.json(
        { error: "Cannot delete maintenance request that is in progress" },
        { status: 400 }
      );
    }

    // Remove maintenance request
    maintenanceRequestsMockData.splice(requestIndex, 1);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 290));

    return NextResponse.json({
      success: true,
      message: "Maintenance request deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting maintenance request:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
