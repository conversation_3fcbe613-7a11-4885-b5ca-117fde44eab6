// Payment hooks for property assets feature
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import type { CreatePayment, Payment, PaymentResponse } from "../types";

// Mock API functions - replace with actual API calls
const paymentApi = {
  getPayment: async (id: string): Promise<Payment> => {
    // Mock implementation
    return {
      id,
      contract_id: "contract-1",
      amount: 1000,
      payment_date: new Date().toISOString(),
      payment_method: "bank_transfer",
      payment_type: "rent",
      description: "Monthly rent payment",
      status: "completed",
      reference_number: "REF-" + Date.now(),
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company-1",
    };
  },

  getPayments: async (params: {
    contractId?: string;
    page?: number;
    limit?: number;
  }): Promise<PaymentResponse> => {
    // Mock implementation
    return {
      items: [],
      total: 0,
      page: params.page || 1,
      limit: params.limit || 10,
    };
  },

  createPayment: async (data: CreatePayment): Promise<Payment> => {
    // Mock implementation
    return {
      id: "payment-" + Date.now(),
      ...data,
      status: "pending",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company-1",
    };
  },

  updatePayment: async (id: string, data: Partial<CreatePayment>): Promise<Payment> => {
    // Mock implementation
    return {
      id,
      contract_id: data.contract_id || "",
      amount: data.amount || 0,
      payment_date: data.payment_date || new Date().toISOString(),
      payment_method: data.payment_method || "cash",
      payment_type: data.payment_type || "rent",
      description: data.description,
      reference_number: data.reference_number,
      status: "completed",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company-1",
    };
  },

  deletePayment: async (id: string): Promise<void> => {
    // TODO: Implement actual API call for payment deletion
    // Mock implementation for now
  },
};

// Hook to get single payment
export function usePayment(id: string) {
  return useQuery({
    queryKey: ["payments", id],
    queryFn: () => paymentApi.getPayment(id),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!id,
  });
}

// Hook to get payments
export function usePayments(params: { contractId?: string; page?: number; limit?: number } = {}) {
  return useQuery({
    queryKey: ["payments", params],
    queryFn: () => paymentApi.getPayments(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook to create payment
export function useCreatePayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: paymentApi.createPayment,
    onSuccess: () => {
      // Toast success message should be handled in component with translation context
      queryClient.invalidateQueries({ queryKey: ["payments"] });
    },
    onError: (error) => {
      // TODO: Implement proper error logging
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
}

// Hook to update payment
export function useUpdatePayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreatePayment> }) =>
      paymentApi.updatePayment(id, data),
    onSuccess: () => {
      // Toast success message should be handled in component with translation context
      queryClient.invalidateQueries({ queryKey: ["payments"] });
    },
    onError: (error) => {
      // TODO: Implement proper error logging
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
}

// Hook to delete payment
export function useDeletePayment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: paymentApi.deletePayment,
    onSuccess: () => {
      // Toast success message should be handled in component with translation context
      queryClient.invalidateQueries({ queryKey: ["payments"] });
    },
    onError: (error) => {
      // TODO: Implement proper error logging
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
}
