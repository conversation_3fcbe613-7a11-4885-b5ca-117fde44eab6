import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import { DateColumn, TextColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { authProtectedPaths } from "@/constants/paths";

import { Tenant } from "../../types";

const ActionGroupTenant = ({
  useDeleteTenantMutation,
  row,
  isDeleting,
}: {
  useDeleteTenantMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Tenant>;
}) => {
  const router = useRouter();
  const tenant = row.original;
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(authProtectedPaths.TENANTS_ID.replace(":id", tenant.id) as any);
    });
  }, [router, tenant.id]);

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.TENANTS_ID_EDIT.replace(":id", tenant.id) as any);
  }, [router, tenant.id]);

  const handleDelete = useCallback(async () => {
    return useDeleteTenantMutation.mutateAsync(tenant.id);
  }, [useDeleteTenantMutation, tenant.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};

export const columns = (
  useDeleteTenantMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<Tenant>[] => [
  {
    id: "tenant",
    accessorKey: "tenant",
    header: t("pages.tenants.headers.tenantInfo"),
    sorter: true,
    isMainColumn: true,
    sortKey: "first_name",
    cell: ({ row }: { row: Row<Tenant> }) => {
      const tenant = row.original;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={`${tenant?.first_name} ${tenant?.last_name}`} className="font-medium" />
          <TextColumn text={tenant?.email} className="text-xs text-muted-foreground opacity-60" />
        </div>
      );
    },
  },
  {
    id: "contact",
    accessorKey: "contact",
    header: t("pages.tenants.headers.contact"),
    sorter: true,
    sortKey: "phone",
    cell: ({ row }: { row: Row<Tenant> }) => {
      const tenant = row.original;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={tenant?.phone} className="font-medium" />
          <TextColumn
            text={tenant?.identification_number}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "employmentStatus",
    accessorKey: "employment_status",
    header: t("pages.tenants.headers.employmentStatus"),
    sorter: true,
    sortKey: "employment_status",
    cell: ({ row }: { row: Row<Tenant> }) => {
      const tenant = row.original;
      return (
        <TextColumn
          text={t(`pages.tenants.employmentStatus.${tenant?.employment_status}`)}
          className="capitalize"
        />
      );
    },
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("pages.tenants.headers.status"),
    sorter: true,
    sortKey: "status",
    cell: ({ row }: { row: Row<Tenant> }) => {
      const tenant = row.original;
      return (
        <TextColumn text={t(`pages.tenants.status.${tenant?.status}`)} className="capitalize" />
      );
    },
  },
  {
    id: "emergencyContact",
    accessorKey: "emergencyContact",
    header: t("pages.tenants.headers.emergencyContact"),
    sorter: true,
    sortKey: "emergency_contact_name",
    cell: ({ row }: { row: Row<Tenant> }) => {
      const tenant = row.original;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={tenant?.emergency_contact_name || "N/A"} className="font-medium" />
          <TextColumn
            text={tenant?.emergency_contact_phone || ""}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "created_at",
    accessorKey: "created_at",
    header: t("pages.tenants.headers.joinDate"),
    sorter: true,
    sortKey: "created_at",
    cell: ({ row }: { row: Row<Tenant> }) => <DateColumn date={row.original.created_at} />,
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.tenants.headers.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Tenant> }) => <DateColumn date={row.original.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.tenants.headers.actions"),
    cell: ({ row }: { row: Row<Tenant> }) => (
      <ActionGroupTenant
        useDeleteTenantMutation={useDeleteTenantMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];
