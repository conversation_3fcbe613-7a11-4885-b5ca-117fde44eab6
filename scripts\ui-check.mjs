#!/usr/bin/env node

/**
 * Simple UI check for Unit Form
 */

import puppeteer from 'puppeteer';

async function quickUICheck() {
  const browser = await puppeteer.launch({ headless: false });
  const page = await browser.newPage();
  
  try {
    console.log('🔍 Quick UI Check for Units Form');
    
    // Go to the form directly
    await page.goto('http://localhost:3001/property-assets/units/new', { 
      waitUntil: 'domcontentloaded',
      timeout: 15000 
    });
    
    console.log('📍 Current URL:', page.url());
    
    // Take a screenshot regardless of what loads
    await page.screenshot({ path: 'unit-form-check.png', fullPage: true });
    console.log('📸 Screenshot saved: unit-form-check.png');
    
    // Get page title and some content
    const title = await page.title();
    console.log('📄 Page title:', title);
    
    // Check what's actually loaded
    const bodyText = await page.evaluate(() => {
      return document.body.textContent?.substring(0, 500) || 'No content';
    });
    console.log('📝 Page content preview:');
    console.log(bodyText);
    
    // Keep browser open for inspection
    console.log('\n👀 Browser open for manual inspection. Press Ctrl+C to close.');
    await new Promise(() => {});
    
  } catch (error) {
    console.error('Error:', error.message);
  } finally {
    await browser.close();
  }
}

quickUICheck();