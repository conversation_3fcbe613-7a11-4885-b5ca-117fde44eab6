import { Metadata } from "next";

import { EditUnitForm } from "@/features/property-assets/components/UnitForm/EditUnitForm";

export const metadata: Metadata = {
  title: "Edit Unit | OneX ERP",
  description: "Edit unit specifications, amenities, pricing, and availability settings",
};

interface EditUnitPageProps {
  params: {
    id: string;
  };
}

export default function EditUnitPage({ params }: EditUnitPageProps) {
  return <EditUnitForm unitId={params.id} />;
}
