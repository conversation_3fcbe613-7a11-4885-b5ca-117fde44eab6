#!/usr/bin/env node

/**
 * Test Financial Page Translations
 * Verifies that financial dashboard translations are working correctly
 */

import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

const SCREENSHOTS_DIR = 'screenshots/financial-translations';
const FINANCIAL_URL = 'http://localhost:3000/property-assets/financial';

async function testFinancialTranslations() {
  console.log('🎯 Testing Financial Page Translations');
  console.log('=====================================\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Test both languages
    for (const language of ['en', 'vi']) {
      console.log(`\n🌐 Testing ${language.toUpperCase()} translations:`);
      console.log('─'.repeat(40));
      
      // Navigate to financial page with auth
      const url = `${FINANCIAL_URL}?lang=${language}`;
      const success = await navigateWithAuth(page, url);
      
      if (!success) {
        console.log('❌ Failed to access financial page');
        continue;
      }
      
      // Wait for content to load
      await page.waitForSelector('h1, [data-testid="page-title"]', { timeout: 10000 });
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check for key financial elements
      const checks = {
        hasTitle: await page.$('h1'),
        hasMetrics: await page.$$('[class*="metric"], [class*="card"]'),
        hasCharts: await page.$$('[class*="chart"], svg'),
        hasButtons: await page.$$('button')
      };
      
      console.log(`📊 Financial Page Elements:`);
      console.log(`   - Page title: ${checks.hasTitle ? '✅' : '❌'}`);
      console.log(`   - Metrics cards: ${checks.hasMetrics.length}`);
      console.log(`   - Charts: ${checks.hasCharts.length}`);
      console.log(`   - Buttons: ${checks.hasButtons.length}`);
      
      // Take screenshot
      const filename = `${SCREENSHOTS_DIR}/${language}/financial-dashboard.png`;
      await page.screenshot({ 
        path: filename, 
        fullPage: true 
      });
      console.log(`   📸 Screenshot saved: ${filename}`);
      
      // Check for specific translations
      const pageText = await page.evaluate(() => document.body.innerText);
      
      if (language === 'en') {
        const hasEnglishTerms = [
          'Financial Dashboard',
          'Total Revenue',
          'Net Income',
          'Occupancy Rate',
          'Export Report'
        ].every(term => pageText.includes(term));
        
        console.log(`   ✅ English translations: ${hasEnglishTerms ? 'Present' : 'Missing'}`);
      } else {
        const hasVietnameseTerms = [
          'Bảng điều khiển tài chính',
          'Tổng doanh thu',
          'Thu nhập ròng',
          'Tỷ lệ lấp đầy',
          'Xuất báo cáo'
        ].some(term => pageText.includes(term));
        
        console.log(`   ✅ Vietnamese translations: ${hasVietnameseTerms ? 'Present' : 'Missing'}`);
        
        // Check for untranslated English
        const untranslatedEnglish = [
          'Financial Dashboard',
          'Total Revenue',
          'Net Income'
        ].some(term => pageText.includes(term));
        
        if (untranslatedEnglish) {
          console.log(`   ⚠️ Untranslated English content detected`);
        } else {
          console.log(`   ✅ No untranslated English content found`);
        }
      }
    }
    
    console.log('\n🎉 Financial translation testing completed!');
    console.log(`📁 Screenshots saved in: ${SCREENSHOTS_DIR}/`);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Create screenshots directory
import { mkdirSync } from 'fs';
try {
  mkdirSync(`${SCREENSHOTS_DIR}/en`, { recursive: true });
  mkdirSync(`${SCREENSHOTS_DIR}/vi`, { recursive: true });
} catch (error) {
  // Directory already exists
}

// Run the test
testFinancialTranslations().catch(console.error);