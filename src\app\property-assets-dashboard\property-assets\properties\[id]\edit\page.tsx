import { Metadata } from "next";

import { EditPropertyForm } from "@/features/property-assets/components/PropertyForm";

export const metadata: Metadata = {
  title: "Edit Property | OneX ERP",
  description: "Edit property information, specifications, and configuration",
};

interface EditPropertyPageProps {
  params: {
    id: string;
  };
}

export default function EditPropertyPage({ params }: EditPropertyPageProps) {
  return <EditPropertyForm />;
}
