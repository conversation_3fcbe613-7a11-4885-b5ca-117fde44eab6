export interface IGetPropertiesParams {
  page?: number;
  limit?: number;
  query?: string;
  [key: string]: unknown;
}

export interface IGetUnitsParams {
  page?: number;
  limit?: number;
  query?: string;
  property_id?: string;
  [key: string]: unknown;
}

export interface IGetContractsParams {
  page?: number;
  limit?: number;
  query?: string;
  status?: string;
  property_id?: string;
  unit_id?: string;
  [key: string]: unknown;
}

export interface IGetTenantsParams {
  page?: number;
  limit?: number;
  query?: string;
  status?: string;
  [key: string]: unknown;
}

export interface IGetMaintenanceParams {
  page?: number;
  limit?: number;
  query?: string;
  status?: string;
  property_id?: string;
  unit_id?: string;
  [key: string]: unknown;
}

export const propertyKeys = {
  all: () => ["property"] as const,
  lists: () => [...propertyKeys.all(), "list"] as const,
  list: (params: IGetPropertiesParams) => [...propertyKeys.lists(), params] as const,
  details: () => [...propertyKeys.all(), "detail"] as const,
  detail: (id: string) => [...propertyKeys.details(), id] as const,
  select: () => [...propertyKeys.all(), "select"] as const,
  units: (propertyId: string) => [...propertyKeys.all(), "units", propertyId] as const,
};

export const unitKeys = {
  all: () => ["unit"] as const,
  lists: () => [...unitKeys.all(), "list"] as const,
  list: (params: IGetUnitsParams) => [...unitKeys.lists(), params] as const,
  details: () => [...unitKeys.all(), "detail"] as const,
  detail: (id: string) => [...unitKeys.details(), id] as const,
  contracts: (unitId: string) => [...unitKeys.all(), "contracts", unitId] as const,
};

export const contractKeys = {
  all: () => ["contract"] as const,
  lists: () => [...contractKeys.all(), "list"] as const,
  list: (params: IGetContractsParams) => [...contractKeys.lists(), params] as const,
  details: () => [...contractKeys.all(), "detail"] as const,
  detail: (id: string) => [...contractKeys.details(), id] as const,
  payments: (contractId: string) => [...contractKeys.all(), "payments", contractId] as const,
};

export const tenantKeys = {
  all: () => ["tenant"] as const,
  lists: () => [...tenantKeys.all(), "list"] as const,
  list: (params: IGetTenantsParams) => [...tenantKeys.lists(), params] as const,
  details: () => [...tenantKeys.all(), "detail"] as const,
  detail: (id: string) => [...tenantKeys.details(), id] as const,
  contracts: (tenantId: string) => [...tenantKeys.all(), "contracts", tenantId] as const,
};

export const maintenanceKeys = {
  all: () => ["maintenance"] as const,
  lists: () => [...maintenanceKeys.all(), "list"] as const,
  list: (params: IGetMaintenanceParams) => [...maintenanceKeys.lists(), params] as const,
  details: () => [...maintenanceKeys.all(), "detail"] as const,
  detail: (id: string) => [...maintenanceKeys.details(), id] as const,
};

export const financialKeys = {
  all: () => ["financial"] as const,
  dashboard: () => [...financialKeys.all(), "dashboard"] as const,
  reports: () => [...financialKeys.all(), "reports"] as const,
  transactions: () => [...financialKeys.all(), "transactions"] as const,
  profitLoss: (params: {
    start_date?: string;
    end_date?: string;
    property_id?: string;
    time_range?: string;
  }) => [...financialKeys.all(), "profit-loss", params] as const,
  revenue: (params: {
    start_date?: string;
    end_date?: string;
    property_id?: string;
    time_range?: string;
  }) => [...financialKeys.all(), "revenue", params] as const,
};

export const QUERY_KEYS = {
  PROPERTIES: ["properties"] as const,
  UNITS: ["units"] as const,
  CONTRACTS: ["contracts"] as const,
  TENANTS: ["tenants"] as const,
  MAINTENANCE: ["maintenance"] as const,
  FINANCIAL: ["financial"] as const,
} as const;
