import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

async function validateFixedTranslations() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('🔐 Authenticating and navigating to property-assets-dashboard...');
    await navigateWithAuth(page, 'http://localhost:3000/property-assets-dashboard');
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📸 Taking final English screenshot...');
    await page.screenshot({ 
      path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/translations-fixed-en.png',
      fullPage: true 
    });
    
    // Check for remaining translation key issues
    const translationIssues = await page.evaluate(() => {
      const text = document.body.innerText;
      const issues = [];
      
      // Look for specific translation issues we found
      if (text.includes('common.month')) issues.push('common.month still visible');
      if (text.includes('common.units')) issues.push('common.units still visible'); 
      if (text.includes('common.week')) issues.push('common.week still visible');
      if (text.includes('common.quarter')) issues.push('common.quarter still visible');
      if (text.includes('common.year')) issues.push('common.year still visible');
      
      // Look for any other translation key patterns
      const keyPatterns = [
        /pages\.\w+\.\w+/g,
        /nav\.\w+\.\w+/g,
        /common\.\w+/g
      ];
      
      keyPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
          matches.forEach(match => {
            if (!issues.includes(match)) {
              issues.push(match);
            }
          });
        }
      });
      
      return issues;
    });
    
    console.log('🔍 Translation issues found:', translationIssues);
    
    // Test key interface elements
    const interfaceElements = await page.evaluate(() => {
      const elements = {};
      
      // Check month dropdown
      const monthDropdown = document.querySelector('select, [role="combobox"]');
      if (monthDropdown) {
        elements.monthDropdown = monthDropdown.textContent?.trim();
      }
      
      // Check units text in occupancy section
      const unitsText = Array.from(document.querySelectorAll('*'))
        .find(el => el.textContent?.includes('6 ') && el.textContent?.includes('units'))?.textContent;
      if (unitsText) {
        elements.unitsText = unitsText;
      }
      
      // Check main title
      const title = document.querySelector('h1');
      if (title) {
        elements.title = title.textContent?.trim();
      }
      
      // Check tab names
      const tabs = Array.from(document.querySelectorAll('[role="tablist"] button, .tabs button'))
        .map(tab => tab.textContent?.trim())
        .filter(text => text && text.length > 0);
      elements.tabs = tabs;
      
      return elements;
    });
    
    console.log('🎯 Interface elements:', interfaceElements);
    
    // Test all tabs to ensure they work
    const tabs = ['Overview', 'Financial', 'Properties', 'Units'];
    
    for (let tabName of tabs) {
      console.log(`🔍 Testing ${tabName} tab...`);
      
      try {
        const tabButtons = await page.$$('[role="tablist"] button, .tabs button, button[data-state]');
        let targetTab = null;
        
        for (let tab of tabButtons) {
          const text = await tab.evaluate(el => el.textContent?.trim());
          if (text && text.toLowerCase().includes(tabName.toLowerCase())) {
            targetTab = tab;
            break;
          }
        }
        
        if (targetTab) {
          await targetTab.click();
          await new Promise(resolve => setTimeout(resolve, 2000));
          
          await page.screenshot({ 
            path: `/Users/<USER>/Desktop/Projects/onex-erp/screenshots/translations-fixed-${tabName.toLowerCase()}.png`,
            fullPage: true 
          });
          
          console.log(`✅ ${tabName} tab tested successfully`);
        } else {
          console.log(`⚠️ Could not find ${tabName} tab`);
        }
        
      } catch (error) {
        console.log(`❌ Error testing ${tabName} tab: ${error.message}`);
      }
    }
    
    console.log('✅ Translation fix validation completed!');
    
    return {
      success: true,
      translationIssues,
      interfaceElements,
      screenshots: [
        'translations-fixed-en.png',
        'translations-fixed-overview.png',
        'translations-fixed-financial.png',
        'translations-fixed-properties.png',
        'translations-fixed-units.png'
      ]
    };
    
  } catch (error) {
    console.error('❌ Error validating fixed translations:', error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run validation
validateFixedTranslations().then(result => {
  if (result.success) {
    console.log('\n🎉 Translation fix validation COMPLETED!');
    console.log(`✅ Screenshots captured: ${result.screenshots.length}`);
    
    if (result.translationIssues.length === 0) {
      console.log('✅ NO translation issues found - All fixed!');
    } else {
      console.log('⚠️ Remaining translation issues:');
      result.translationIssues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }
    
    console.log('\n📋 Interface Elements:');
    console.log(`   Title: ${result.interfaceElements.title}`);
    console.log(`   Month Dropdown: ${result.interfaceElements.monthDropdown}`);
    console.log(`   Units Text: ${result.interfaceElements.unitsText}`);
    console.log(`   Tabs: ${result.interfaceElements.tabs?.join(', ')}`);
    
  } else {
    console.log('\n❌ Translation fix validation FAILED!');
    console.log(`Error: ${result.error}`);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
});