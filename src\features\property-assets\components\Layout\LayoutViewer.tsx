"use client";

import { use<PERSON><PERSON>back, useEffect, useRef, useState } from "react";
import {
  Download,
  Maximize2,
  Mouse<PERSON>ointer,
  Move,
  Plus,
  RotateCcw,
  Save,
  Settings,
  Upload,
  ZoomIn,
  ZoomOut,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { TransformComponent, TransformWrapper } from "react-zoom-pan-pinch";

import { Badge } from "@/components/ui/badge";
// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ImageUpload } from "@/components/ui/image-upload";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Types
import type { Unit } from "../../types";

export interface UnitPosition {
  id: string;
  unitId: string;
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number;
}

export interface LayoutData {
  id: string;
  name: string;
  imageUrl: string;
  width: number;
  height: number;
  unitPositions: UnitPosition[];
  scale: number;
  offsetX: number;
  offsetY: number;
}

interface LayoutViewerProps {
  layoutData?: LayoutData;
  units: Unit[];
  onSaveLayout?: (layout: LayoutData) => void;
  onUnitClick?: (unit: Unit) => void;
  onUnitPositionChange?: (unitPosition: UnitPosition) => void;
  isEditable?: boolean;
  showControls?: boolean;
  className?: string;
}

type ViewMode = "view" | "edit" | "select";

export function LayoutViewer({
  layoutData,
  units,
  onSaveLayout,
  onUnitClick,
  onUnitPositionChange,
  isEditable = false,
  showControls = true,
  className = "",
}: LayoutViewerProps) {
  const { t } = useTranslation();

  // Debug logging
  useEffect(() => {
    console.log("LayoutViewer props:", {
      layoutData: layoutData
        ? {
            id: layoutData.id,
            name: layoutData.name,
            imageUrl: layoutData.imageUrl?.substring(0, 100) + "...",
            unitPositions: layoutData.unitPositions?.length || 0,
          }
        : null,
      units: units?.length || 0,
      isEditable,
      showControls,
    });
  }, [layoutData, units, isEditable, showControls]);

  // State management
  const [viewMode, setViewMode] = useState<ViewMode>("view");
  const [selectedUnit, setSelectedUnit] = useState<UnitPosition | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [layoutImage, setLayoutImage] = useState<string | null>(layoutData?.imageUrl || null);
  const [imageError, setImageError] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);

  // Refs
  const containerRef = useRef<HTMLDivElement>(null);
  const transformRef = useRef<any>(null);

  // Reset image error when layout image changes
  useEffect(() => {
    if (layoutImage) {
      setImageError(false);
    }
  }, [layoutImage]);

  // Reset image error when layoutData changes
  useEffect(() => {
    if (layoutData?.imageUrl) {
      setLayoutImage(layoutData.imageUrl);
      setImageError(false);
    }
  }, [layoutData?.imageUrl]);

  // Handle image upload
  const handleImageUpload = useCallback((value: string | null) => {
    setLayoutImage(value);
    setImageError(false);
  }, []);

  // Handle zoom controls
  const handleZoomIn = useCallback(() => {
    transformRef.current?.zoomIn(0.2);
  }, []);

  const handleZoomOut = useCallback(() => {
    transformRef.current?.zoomOut(0.2);
  }, []);

  const handleResetTransform = useCallback(() => {
    transformRef.current?.resetTransform();
  }, []);

  const handleFitToScreen = useCallback(() => {
    transformRef.current?.centerView();
  }, []);

  // Handle unit selection and dragging
  const handleUnitMouseDown = useCallback(
    (unitPosition: UnitPosition, event: React.MouseEvent) => {
      if (viewMode !== "edit") return;

      event.preventDefault();
      event.stopPropagation();

      const rect = event.currentTarget.getBoundingClientRect();
      const containerRect = containerRef.current?.getBoundingClientRect();

      if (!containerRect) return;

      setSelectedUnit(unitPosition);
      setIsDragging(true);
      setDragOffset({
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      });
    },
    [viewMode]
  );

  const handleMouseMove = useCallback(
    (event: React.MouseEvent) => {
      if (!isDragging || !selectedUnit || viewMode !== "edit") return;

      const containerRect = containerRef.current?.getBoundingClientRect();
      if (!containerRect) return;

      const newX = event.clientX - containerRect.left - dragOffset.x;
      const newY = event.clientY - containerRect.top - dragOffset.y;

      const updatedUnit = {
        ...selectedUnit,
        x: Math.max(0, Math.min(newX, containerRect.width - selectedUnit.width)),
        y: Math.max(0, Math.min(newY, containerRect.height - selectedUnit.height)),
      };

      setSelectedUnit(updatedUnit);
      onUnitPositionChange?.(updatedUnit);
    },
    [isDragging, selectedUnit, viewMode, dragOffset, onUnitPositionChange]
  );

  const handleMouseUp = useCallback(() => {
    if (isDragging) {
      setIsDragging(false);
      setSelectedUnit(null);
    }
  }, [isDragging]);

  // Handle unit click
  const handleUnitClick = useCallback(
    (unitPosition: UnitPosition) => {
      const unit = units.find((u) => u.id === unitPosition.unitId);
      if (unit && onUnitClick) {
        onUnitClick(unit);
      }
    },
    [units, onUnitClick]
  );

  // Handle drag and drop for unit assignment
  const handleDragOver = useCallback(
    (e: React.DragEvent) => {
      if (!isEditable) return;
      e.preventDefault();
      e.dataTransfer.dropEffect = "copy";
      setIsDragOver(true);
    },
    [isEditable]
  );

  const handleDragLeave = useCallback(
    (e: React.DragEvent) => {
      if (!isEditable) return;
      // Check if we're leaving the container, not just entering a child
      const rect = containerRef.current?.getBoundingClientRect();
      if (
        rect &&
        (e.clientX < rect.left ||
          e.clientX > rect.right ||
          e.clientY < rect.top ||
          e.clientY > rect.bottom)
      ) {
        setIsDragOver(false);
      }
    },
    [isEditable]
  );

  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      if (!isEditable) return;
      e.preventDefault();
      setIsDragOver(false);

      try {
        const data = JSON.parse(e.dataTransfer.getData("text/plain"));
        const { unitId, unitNumber, unitType } = data;

        // Get drop position relative to the container
        const rect = containerRef.current?.getBoundingClientRect();
        if (!rect) return;

        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        // Create new unit position
        const newUnitPosition: UnitPosition = {
          id: `pos_${Date.now()}_${unitId}`,
          unitId,
          x: Math.max(0, x - 50), // Center the unit on cursor
          y: Math.max(0, y - 30),
          width: 100,
          height: 60,
          rotation: 0,
        };

        onUnitPositionChange?.(newUnitPosition);
      } catch (error) {
        // TODO: Implement proper error logging for unit drop operations
      }
    },
    [isEditable, onUnitPositionChange]
  );

  // Get unit status color
  const getUnitStatusColor = useCallback(
    (unitId: string) => {
      const unit = units.find((u) => u.id === unitId);
      if (!unit) return "bg-muted-foreground";

      switch (unit.status) {
        case "available":
          return "bg-success";
        case "occupied":
          return "bg-primary";
        case "maintenance":
          return "bg-destructive";
        case "inactive":
          return "bg-muted-foreground";
        default:
          return "bg-muted-foreground";
      }
    },
    [units]
  );

  // Get unit info
  const getUnitInfo = useCallback(
    (unitId: string) => {
      return units.find((u) => u.id === unitId);
    },
    [units]
  );

  // Save layout
  const handleSaveLayout = useCallback(() => {
    if (!layoutImage || !onSaveLayout) return;

    const layout: LayoutData = {
      id: layoutData?.id || "",
      name: layoutData?.name || "Floor Plan",
      imageUrl: layoutImage,
      width: containerRef.current?.clientWidth || 800,
      height: containerRef.current?.clientHeight || 600,
      unitPositions: layoutData?.unitPositions || [],
      scale: 1,
      offsetX: 0,
      offsetY: 0,
    };

    onSaveLayout(layout);
  }, [layoutImage, layoutData, onSaveLayout]);

  // Handle image error
  const handleImageError = () => {
    console.error("Layout image failed to load:", layoutImage);
    setImageError(true);
  };

  return (
    <TooltipProvider>
      <div className={`relative h-full w-full ${className}`}>
        {/* Controls Bar */}
        {showControls && (
          <Card className="absolute left-4 top-4 z-10 shadow-lg">
            <CardContent className="p-3">
              <div className="flex items-center gap-2">
                {/* View Mode Selector */}
                <Select value={viewMode} onValueChange={(value: ViewMode) => setViewMode(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="view">
                      <div className="flex items-center gap-2">
                        <MousePointer className="size-4" />
                        {t("common.view")}
                      </div>
                    </SelectItem>
                    {isEditable && (
                      <SelectItem value="edit">
                        <div className="flex items-center gap-2">
                          <Move className="size-4" />
                          {t("common.edit")}
                        </div>
                      </SelectItem>
                    )}
                    <SelectItem value="select">
                      <div className="flex items-center gap-2">
                        <Plus className="size-4" />
                        {t("common.select")}
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>

                <Separator orientation="vertical" className="h-6" />

                {/* Zoom Controls */}
                <div className="flex items-center gap-1">
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm" onClick={handleZoomIn}>
                        <ZoomIn className="size-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t("common.zoomIn")}</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm" onClick={handleZoomOut}>
                        <ZoomOut className="size-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t("common.zoomOut")}</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm" onClick={handleFitToScreen}>
                        <Maximize2 className="size-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t("common.fitToScreen")}</TooltipContent>
                  </Tooltip>

                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button variant="outline" size="sm" onClick={handleResetTransform}>
                        <RotateCcw className="size-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>{t("common.reset")}</TooltipContent>
                  </Tooltip>
                </div>

                {/* Action Controls */}
                {isEditable && (
                  <>
                    <Separator orientation="vertical" className="h-6" />
                    <div className="flex items-center gap-1">
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button variant="outline" size="sm" onClick={handleSaveLayout}>
                            <Save className="size-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>{t("common.save")}</TooltipContent>
                      </Tooltip>
                    </div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Layout Status */}
        {showControls && (
          <Card className="absolute right-4 top-4 z-10 shadow-lg">
            <CardContent className="p-3">
              <div className="flex items-center gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <div className="size-3 rounded-full bg-success"></div>
                  <span>{t("pages.units.status.available")}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="size-3 rounded-full bg-primary"></div>
                  <span>{t("pages.units.status.occupied")}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="size-3 rounded-full bg-destructive"></div>
                  <span>{t("pages.units.status.maintenance")}</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="size-3 rounded-full bg-muted-foreground"></div>
                  <span>{t("pages.units.status.inactive")}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Layout Area */}
        <div
          ref={containerRef}
          className={`h-full w-full overflow-hidden rounded-lg border border-border bg-muted/50 transition-colors ${
            isDragOver ? "border-primary bg-primary/5" : ""
          }`}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}>
          {!layoutImage ? (
            /* Image Upload Area */
            <div className="flex h-full items-center justify-center">
              <div className="max-w-md space-y-4 text-center">
                <Upload className="mx-auto size-16 text-muted-foreground" />
                <div>
                  <h3 className="mb-2 text-lg font-semibold text-foreground">
                    {t("pages.layouts.uploadImage")}
                  </h3>
                  <p className="mb-4 text-sm text-muted-foreground">
                    {t("pages.layouts.uploadImageDescription")}
                  </p>
                  <ImageUpload
                    value={layoutImage}
                    onChange={handleImageUpload}
                    className="mx-auto max-w-sm"
                  />
                </div>
              </div>
            </div>
          ) : (
            /* Layout Viewer */
            <TransformWrapper
              ref={transformRef}
              initialScale={1}
              minScale={0.1}
              maxScale={5}
              wheel={{ wheelDisabled: viewMode === "edit" }}
              panning={{ disabled: viewMode === "edit" }}
              doubleClick={{ disabled: true }}>
              <TransformComponent wrapperClass="w-full h-full" contentClass="w-full h-full">
                <div className="relative h-full w-full">
                  {/* Layout Image */}
                  {!imageError ? (
                    <img
                      src={layoutImage}
                      alt="Floor Plan"
                      className="h-full w-full object-contain"
                      onError={handleImageError}
                      draggable={false}
                      crossOrigin="anonymous"
                    />
                  ) : (
                    <div className="flex h-full w-full items-center justify-center bg-muted">
                      <div className="text-center">
                        <Settings className="mx-auto mb-4 size-16 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground">
                          {t("common.imageLoadError") || "Failed to load image"}
                        </p>
                        <p className="mt-1 text-xs text-muted-foreground">
                          Layout: {layoutData?.name || "Unknown"}
                        </p>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => {
                            setLayoutImage(null);
                            setImageError(false);
                          }}>
                          {t("common.uploadNew") || "Upload New"}
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Drop Zone Indicator */}
                  {isDragOver && isEditable && (
                    <div className="absolute inset-0 z-50 flex items-center justify-center border-2 border-dashed border-primary bg-primary/10">
                      <div className="text-center">
                        <Plus className="mx-auto mb-2 size-12 text-primary" />
                        <p className="text-sm font-medium text-primary">
                          {t("pages.layouts.dropUnitHere")}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* Unit Overlays */}
                  {layoutData?.unitPositions.map((unitPosition) => {
                    const unit = getUnitInfo(unitPosition.unitId);
                    const statusColor = getUnitStatusColor(unitPosition.unitId);

                    return (
                      <Tooltip key={unitPosition.id}>
                        <TooltipTrigger asChild>
                          <div
                            className={`absolute cursor-pointer rounded border-2 border-white shadow-lg transition-all duration-200 ${statusColor} ${
                              selectedUnit?.id === unitPosition.id
                                ? "ring-2 ring-primary ring-offset-2"
                                : "hover:ring-2 hover:ring-primary/50"
                            }`}
                            style={{
                              left: unitPosition.x,
                              top: unitPosition.y,
                              width: unitPosition.width,
                              height: unitPosition.height,
                              transform: unitPosition.rotation
                                ? `rotate(${unitPosition.rotation}deg)`
                                : undefined,
                              opacity: viewMode === "edit" ? 0.8 : 0.7,
                            }}
                            onMouseDown={(e) => handleUnitMouseDown(unitPosition, e)}
                            onClick={() => handleUnitClick(unitPosition)}>
                            <div className="flex h-full w-full items-center justify-center text-xs font-semibold text-white">
                              {unit?.unit_number || unitPosition.unitId}
                            </div>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <div className="text-sm">
                            <div className="font-semibold">{unit?.unit_number}</div>
                            <div className="text-xs text-muted-foreground">
                              {unit?.unit_type} • {unit?.status}
                            </div>
                            {unit?.rent_amount && (
                              <div className="text-xs">
                                ${unit.rent_amount.toLocaleString()}/month
                              </div>
                            )}
                          </div>
                        </TooltipContent>
                      </Tooltip>
                    );
                  })}
                </div>
              </TransformComponent>
            </TransformWrapper>
          )}
        </div>
      </div>
    </TooltipProvider>
  );
}
