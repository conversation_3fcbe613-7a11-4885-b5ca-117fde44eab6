#!/usr/bin/env node

/**
 * Visual Comparison Script
 * Compares screenshots for visual regression testing and Figma design validation
 */

import { readFileSync, existsSync } from 'fs';
import { writeFile, mkdir } from 'fs/promises';
import { join, dirname, basename, extname } from 'path';

/**
 * Load image comparison library
 */
async function loadPixelmatch() {
  try {
    const pixelmatch = await import('pixelmatch');
    const { PNG } = await import('pngjs');
    return { pixelmatch: pixelmatch.default, PNG };
  } catch (error) {
    throw new Error('Image comparison dependencies not installed. Run: npm install pixelmatch pngjs');
  }
}

/**
 * Load image data from file
 */
async function loadImage(imagePath) {
  const { PNG } = await loadPixelmatch();
  
  if (!existsSync(imagePath)) {
    throw new Error(`Image not found: ${imagePath}`);
  }

  const imageBuffer = readFileSync(imagePath);
  const image = PNG.sync.read(imageBuffer);
  
  return {
    data: image.data,
    width: image.width,
    height: image.height,
    path: imagePath
  };
}

/**
 * Compare two images and generate diff
 */
async function compareImages(baselinePath, currentPath, options = {}) {
  const {
    threshold = 0.1,
    includeAA = false,
    outputDiffPath = null,
    diffColor = { r: 255, g: 0, b: 255 }, // Magenta for differences
    alpha = 0.8
  } = options;

  console.log(`🔍 Comparing images:`);
  console.log(`  📄 Baseline: ${baselinePath}`);
  console.log(`  📄 Current:  ${currentPath}`);

  const { pixelmatch, PNG } = await loadPixelmatch();
  
  const baseline = await loadImage(baselinePath);
  const current = await loadImage(currentPath);

  // Check dimensions match
  if (baseline.width !== current.width || baseline.height !== current.height) {
    return {
      match: false,
      error: 'Image dimensions do not match',
      baseline: { width: baseline.width, height: baseline.height },
      current: { width: current.width, height: current.height },
      pixelDifference: null,
      similarity: 0
    };
  }

  const { width, height } = baseline;
  const diffBuffer = new Uint8Array(width * height * 4);

  // Perform pixel comparison
  const pixelDifference = pixelmatch(
    baseline.data,
    current.data,
    diffBuffer,
    width,
    height,
    {
      threshold,
      includeAA,
      alpha,
      diffColor,
      aaColor: { r: 255, g: 255, b: 0 }, // Yellow for anti-aliasing differences
      diffColorAlt: { r: 0, g: 255, b: 255 } // Cyan for alternative differences
    }
  );

  const totalPixels = width * height;
  const similarity = ((totalPixels - pixelDifference) / totalPixels) * 100;
  const match = similarity >= 98; // 98% similarity threshold

  console.log(`  📊 Pixel difference: ${pixelDifference} / ${totalPixels}`);
  console.log(`  📈 Similarity: ${similarity.toFixed(2)}%`);
  console.log(`  ${match ? '✅' : '❌'} Match: ${match}`);

  // Generate diff image if requested
  let diffImagePath = null;
  if (outputDiffPath && pixelDifference > 0) {
    const diffPng = new PNG({ width, height });
    diffPng.data = diffBuffer;

    await mkdir(dirname(outputDiffPath), { recursive: true });
    const diffImageBuffer = PNG.sync.write(diffPng);
    await writeFile(outputDiffPath, diffImageBuffer);
    
    diffImagePath = outputDiffPath;
    console.log(`  🖼️  Diff image: ${diffImagePath}`);
  }

  return {
    match,
    pixelDifference,
    totalPixels,
    similarity,
    threshold: 98,
    baseline: {
      path: baselinePath,
      width: baseline.width,
      height: baseline.height
    },
    current: {
      path: currentPath,
      width: current.width,
      height: current.height
    },
    diff: {
      path: diffImagePath,
      pixelsChanged: pixelDifference
    },
    comparedAt: new Date().toISOString()
  };
}

/**
 * Batch compare multiple image pairs
 */
async function batchCompareImages(comparisons = []) {
  const results = [];
  
  for (const comparison of comparisons) {
    const {
      name,
      baselinePath,
      currentPath,
      diffPath = null,
      ...options
    } = comparison;

    console.log(`\n🔍 Comparing: ${name}`);
    
    try {
      const result = await compareImages(baselinePath, currentPath, {
        outputDiffPath: diffPath,
        ...options
      });
      
      results.push({
        name,
        ...result
      });
      
    } catch (error) {
      console.error(`❌ Error comparing ${name}:`, error.message);
      results.push({
        name,
        match: false,
        error: error.message,
        comparedAt: new Date().toISOString()
      });
    }
  }
  
  return results;
}

/**
 * Compare component across multiple viewports
 */
async function compareComponentViewports(componentName, baselineDir, currentDir, viewports = ['mobile', 'tablet', 'desktop']) {
  const comparisons = viewports.map(viewport => ({
    name: `${componentName}-${viewport}`,
    baselinePath: join(baselineDir, `${componentName}-${viewport}.png`),
    currentPath: join(currentDir, `${componentName}-${viewport}.png`),
    diffPath: join(currentDir, 'diffs', `${componentName}-${viewport}-diff.png`)
  }));

  return await batchCompareImages(comparisons);
}

/**
 * Generate visual test report
 */
async function generateVisualTestReport(results, outputPath) {
  const totalTests = results.length;
  const passedTests = results.filter(r => r.match && !r.error).length;
  const failedTests = totalTests - passedTests;
  const averageSimilarity = results
    .filter(r => r.similarity !== undefined)
    .reduce((sum, r) => sum + r.similarity, 0) / totalTests;

  const report = {
    summary: {
      totalTests,
      passedTests,
      failedTests,
      passRate: (passedTests / totalTests) * 100,
      averageSimilarity,
      generatedAt: new Date().toISOString()
    },
    results: results.map(result => ({
      ...result,
      status: result.match && !result.error ? 'PASS' : 'FAIL'
    })),
    recommendations: generateRecommendations(results)
  };

  await mkdir(dirname(outputPath), { recursive: true });
  await writeFile(outputPath, JSON.stringify(report, null, 2));
  
  console.log(`\n📋 Visual Test Report`);
  console.log(`  📊 Total tests: ${totalTests}`);
  console.log(`  ✅ Passed: ${passedTests}`);
  console.log(`  ❌ Failed: ${failedTests}`);
  console.log(`  📈 Pass rate: ${report.summary.passRate.toFixed(1)}%`);
  console.log(`  📈 Avg similarity: ${averageSimilarity.toFixed(1)}%`);
  console.log(`  📄 Report: ${outputPath}`);

  return report;
}

/**
 * Generate recommendations based on test results
 */
function generateRecommendations(results) {
  const recommendations = [];
  
  const failedTests = results.filter(r => !r.match || r.error);
  const lowSimilarityTests = results.filter(r => r.similarity && r.similarity < 95);
  
  if (failedTests.length > 0) {
    recommendations.push({
      type: 'FAILURE',
      message: `${failedTests.length} test(s) failed. Review diff images and update implementation.`,
      tests: failedTests.map(t => t.name)
    });
  }
  
  if (lowSimilarityTests.length > 0) {
    recommendations.push({
      type: 'LOW_SIMILARITY',
      message: `${lowSimilarityTests.length} test(s) have similarity below 95%. Consider reviewing for minor differences.`,
      tests: lowSimilarityTests.map(t => ({ name: t.name, similarity: t.similarity }))
    });
  }
  
  const dimensionErrors = results.filter(r => r.error && r.error.includes('dimensions'));
  if (dimensionErrors.length > 0) {
    recommendations.push({
      type: 'DIMENSION_MISMATCH',
      message: 'Some images have dimension mismatches. Ensure consistent viewport sizes.',
      tests: dimensionErrors.map(t => t.name)
    });
  }
  
  if (recommendations.length === 0) {
    recommendations.push({
      type: 'SUCCESS',
      message: 'All visual tests passed! Components match the expected design.'
    });
  }
  
  return recommendations;
}

/**
 * Update baseline images
 */
async function updateBaselines(sourceDir, baselineDir, pattern = '*.png') {
  // This would copy current images to baseline directory
  // Implementation depends on the file system operations needed
  console.log(`📋 Updating baselines from ${sourceDir} to ${baselineDir}`);
  
  // For now, just log the action
  // In a real implementation, you'd copy the files
  return {
    updated: true,
    message: 'Baseline update functionality to be implemented',
    sourceDir,
    baselineDir
  };
}

/**
 * Compare with Figma design export
 */
async function compareWithFigmaExport(componentScreenshot, figmaExportPath, options = {}) {
  console.log('\n🎨 Comparing with Figma design export...');
  
  const result = await compareImages(figmaExportPath, componentScreenshot, {
    threshold: 0.05, // Stricter threshold for design comparison
    outputDiffPath: join(dirname(componentScreenshot), 'figma-diff.png'),
    ...options
  });
  
  // Add Figma-specific analysis
  result.designMatch = result.similarity >= 95; // Stricter for design compliance
  result.designCompliance = {
    excellent: result.similarity >= 98,
    good: result.similarity >= 95,
    needsWork: result.similarity < 95
  };
  
  return result;
}

/**
 * Create test template for component visual testing
 */
function generateTestTemplate(componentName, viewports = ['mobile', 'tablet', 'desktop']) {
  const template = {
    component: componentName,
    testSuite: `${componentName} Visual Tests`,
    viewports,
    baseline: {
      directory: `screenshots/baseline/${componentName}`,
      files: viewports.map(v => `${componentName}-${v}.png`)
    },
    current: {
      directory: `screenshots/current/${componentName}`,
      files: viewports.map(v => `${componentName}-${v}.png`)
    },
    diff: {
      directory: `screenshots/current/${componentName}/diffs`,
      files: viewports.map(v => `${componentName}-${v}-diff.png`)
    },
    thresholds: {
      similarity: 98,
      pixelThreshold: 0.1
    },
    figmaComparison: {
      enabled: true,
      designFile: `designs/figma-exports/${componentName}.png`,
      strictMode: true
    }
  };
  
  return template;
}

// CLI Interface
async function main() {
  const [,, command, ...args] = process.argv;

  try {
    switch (command) {
      case 'compare': {
        const [baseline, current, diffPath] = args;
        if (!baseline || !current) {
          console.error('Usage: node visual-compare.mjs compare <baseline> <current> [diff-output]');
          process.exit(1);
        }

        const result = await compareImages(baseline, current, {
          outputDiffPath: diffPath
        });

        console.log(`\n📊 Comparison Result:`);
        console.log(`  Match: ${result.match ? '✅' : '❌'}`);
        console.log(`  Similarity: ${result.similarity.toFixed(2)}%`);
        
        if (diffPath) {
          console.log(`  Diff image: ${diffPath}`);
        }
        break;
      }

      case 'batch': {
        const [configFile] = args;
        if (!configFile) {
          console.error('Usage: node visual-compare.mjs batch <config-file>');
          process.exit(1);
        }

        const config = JSON.parse(readFileSync(configFile, 'utf8'));
        const results = await batchCompareImages(config.comparisons);
        
        const reportPath = join(dirname(configFile), 'visual-test-report.json');
        await generateVisualTestReport(results, reportPath);
        break;
      }

      case 'component': {
        const [componentName, baselineDir, currentDir] = args;
        if (!componentName || !baselineDir || !currentDir) {
          console.error('Usage: node visual-compare.mjs component <name> <baseline-dir> <current-dir>');
          process.exit(1);
        }

        const results = await compareComponentViewports(componentName, baselineDir, currentDir);
        
        const reportPath = join(currentDir, `${componentName}-visual-report.json`);
        await generateVisualTestReport(results, reportPath);
        break;
      }

      case 'figma': {
        const [componentScreenshot, figmaExport] = args;
        if (!componentScreenshot || !figmaExport) {
          console.error('Usage: node visual-compare.mjs figma <component-screenshot> <figma-export>');
          process.exit(1);
        }

        const result = await compareWithFigmaExport(componentScreenshot, figmaExport);
        
        console.log(`\n🎨 Figma Design Comparison:`);
        console.log(`  Design Match: ${result.designMatch ? '✅' : '❌'}`);
        console.log(`  Similarity: ${result.similarity.toFixed(2)}%`);
        console.log(`  Compliance: ${Object.keys(result.designCompliance).find(k => result.designCompliance[k])}`);
        break;
      }

      case 'template': {
        const [componentName] = args;
        if (!componentName) {
          console.error('Usage: node visual-compare.mjs template <component-name>');
          process.exit(1);
        }

        const template = generateTestTemplate(componentName);
        console.log(JSON.stringify(template, null, 2));
        break;
      }

      case 'test': {
        console.log('🔍 Testing visual comparison setup...');
        
        // Create test images (would need actual test images in practice)
        console.log('✅ Visual comparison dependencies loaded successfully');
        console.log('💡 To test comparison, use: node visual-compare.mjs compare <baseline> <current>');
        break;
      }

      default:
        console.log(`
🔍 Visual Comparison Tool

Usage:
  node visual-compare.mjs <command> [args]

Commands:
  test                                      Test visual comparison setup
  compare <baseline> <current> [diff]      Compare two images
  batch <config-file>                      Run batch comparison from config
  component <name> <baseline> <current>    Compare component across viewports
  figma <screenshot> <figma-export>        Compare with Figma design export
  template <component-name>                Generate test template

Examples:
  node visual-compare.mjs test
  node visual-compare.mjs compare baseline.png current.png diff.png
  node visual-compare.mjs component Button ./baseline ./current
  node visual-compare.mjs figma component.png figma-design.png

Thresholds:
  - Component matching: 98% similarity required
  - Figma design matching: 95% similarity required
  - Pixel difference threshold: 0.1 (adjustable)
        `);
        break;
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    process.exit(1);
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export {
  compareImages,
  batchCompareImages,
  compareComponentViewports,
  compareWithFigmaExport,
  generateVisualTestReport,
  generateTestTemplate,
  updateBaselines
};