#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

const TEST_URL = 'http://localhost:3000';

async function testOverlapInfiniteLoopFix() {
  console.log('🧪 Testing Overlap Warning Infinite Loop Fix...\\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  const page = await browser.newPage();

  try {
    // Listen for console errors and runtime errors
    const runtimeErrors = [];
    const consoleErrors = [];
    
    page.on('pageerror', (error) => {
      runtimeErrors.push(error.message);
      console.log('❌ Runtime Error:', error.message);
    });
    
    page.on('console', (msg) => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
        console.log('⚠️ Console Error:', msg.text());
      }
    });

    // Navigate to unit mapping interface
    console.log('🗺️ Navigating to Unit Mapping Interface...');
    const authSuccess = await navigateWithAuth(page, `${TEST_URL}/property-assets/layout`);
    if (!authSuccess) throw new Error('Authentication failed');
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Click Open Mapping
    const openMappingFound = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const openMappingBtn = buttons.find(btn => btn.textContent.includes('Open Mapping'));
      if (openMappingBtn) {
        openMappingBtn.click();
        return true;
      }
      return false;
    });
    
    if (!openMappingFound) throw new Error('Open Mapping button not found');
    console.log('✅ Navigated to Unit Mapping Interface\\n');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Enable edit mode
    console.log('✏️ Enabling edit mode...');
    const editModeEnabled = await page.evaluate(() => {
      const allButtons = Array.from(document.querySelectorAll('button'));
      const gridButton = allButtons.find(btn => {
        const hasGridIcon = btn.querySelector('svg[data-lucide="grid-3-x-3"]');
        return hasGridIcon;
      });
      
      if (gridButton) {
        gridButton.click();
        return true;
      }
      return false;
    });
    
    if (editModeEnabled) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const editClicked = await page.evaluate(() => {
        const menuItems = Array.from(document.querySelectorAll('[role="menuitem"]'));
        const editItem = menuItems.find(item => 
          item.textContent.includes('Edit') || item.querySelector('svg[data-lucide="edit"]')
        );
        
        if (editItem) {
          editItem.click();
          return true;
        }
        return false;
      });
      
      if (editClicked) {
        console.log('✅ Edit mode enabled successfully');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Wait and monitor for any runtime errors
        console.log('⏱️ Monitoring for runtime errors for 10 seconds...');
        await new Promise(resolve => setTimeout(resolve, 10000));
        
        // Check if any runtime errors occurred
        const hasInfiniteLoopError = runtimeErrors.some(error => 
          error.includes('Maximum update depth exceeded') ||
          error.includes('infinite loop') ||
          error.includes('Too many re-renders')
        );
        
        const hasReactErrors = consoleErrors.some(error =>
          error.includes('Warning: Maximum update depth exceeded') ||
          error.includes('Warning: Cannot update a component')
        );
        
        console.log('\\n📊 Error Analysis:');
        console.log(`   - Runtime errors detected: ${runtimeErrors.length}`);
        console.log(`   - Console errors detected: ${consoleErrors.length}`);
        console.log(`   - Infinite loop errors: ${hasInfiniteLoopError ? 'YES' : 'NO'}`);
        console.log(`   - React warning errors: ${hasReactErrors ? 'YES' : 'NO'}`);
        
        if (runtimeErrors.length > 0) {
          console.log('\\n❌ Runtime Errors Found:');
          runtimeErrors.forEach((error, index) => {
            console.log(`   ${index + 1}. ${error.substring(0, 100)}...`);
          });
        }
        
        if (consoleErrors.length > 0) {
          console.log('\\n⚠️ Console Errors Found:');
          consoleErrors.forEach((error, index) => {
            console.log(`   ${index + 1}. ${error.substring(0, 100)}...`);
          });
        }
        
        // Take screenshot of current state
        await page.screenshot({ 
          path: 'screenshots/overlap-infinite-loop-test.png', 
          fullPage: true 
        });
        
        if (!hasInfiniteLoopError && !hasReactErrors && runtimeErrors.length === 0) {
          console.log('\\n🎉 SUCCESS: No infinite loop errors detected!');
          console.log('   ✅ Overlap warning timeout logic fixed');
          console.log('   ✅ No maximum update depth exceeded errors');
          console.log('   ✅ Component renders stable');
        } else {
          console.log('\\n❌ FAILED: Still experiencing render issues');
        }
      }
    }

  } catch (error) {
    console.error('❌ Error during infinite loop test:', error.message);
    await page.screenshot({ path: 'screenshots/overlap-infinite-loop-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

// Run the test
testOverlapInfiniteLoopFix().catch(console.error);