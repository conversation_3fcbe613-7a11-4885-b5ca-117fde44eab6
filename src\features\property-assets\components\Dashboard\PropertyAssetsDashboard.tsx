"use client";

import { useCallback, useState } from "react";
import Link from "next/link";
import Loading from "@/app/loading";
import {
  Activity,
  AlertCircle,
  Calendar,
  DollarSign,
  Eye,
  FileText,
  Home,
  Plus,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { authProtectedPaths } from "@/constants/paths";

import { useContracts } from "../../hooks/useContracts";
import { useFinancialSummary } from "../../hooks/useFinancial";
// Property Assets hooks
import { useProperties } from "../../hooks/useProperties";
import { useTenants } from "../../hooks/useTenants";
import { useUnits } from "../../hooks/useUnits";

interface PropertyAssetsDashboardProps {
  className?: string;
}

export function PropertyAssetsDashboard({ className }: PropertyAssetsDashboardProps) {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState<"week" | "month" | "quarter" | "year">("month");

  // Data hooks
  const { data: propertiesData, isLoading: loadingProperties } = useProperties({ limit: 1000 });
  const { data: unitsData, isLoading: loadingUnits } = useUnits(undefined, { limit: 1000 });
  const { data: contractsData, isLoading: loadingContracts } = useContracts({ limit: 1000 });
  const { data: tenantsData, isLoading: loadingTenants } = useTenants({ limit: 1000 });
  const { data: financialSummary, isLoading: loadingFinancial } = useFinancialSummary({});

  // Extract arrays from response data
  const properties = propertiesData?.items || [];
  const units = unitsData?.items || [];
  const contracts = contractsData?.items || [];
  const tenants = tenantsData?.items || [];

  // Calculate metrics
  const totalProperties = properties.length;
  const totalUnits = units.length;
  const totalContracts = contracts.length;
  const totalTenants = tenants.length;

  // Calculate occupancy
  const occupiedUnits = units.filter((unit) => unit.status === "occupied").length;
  const vacantUnits = totalUnits - occupiedUnits;
  const occupancyRate = totalUnits > 0 ? (occupiedUnits / totalUnits) * 100 : 0;

  // Calculate financial metrics
  const monthlyRevenue = financialSummary?.monthly_rental_income || 0;
  const monthlyExpenses = financialSummary?.monthly_expenses || 0;
  const netIncome = monthlyRevenue - monthlyExpenses;
  const profitMargin = monthlyRevenue > 0 ? (netIncome / monthlyRevenue) * 100 : 0;

  // Calculate active contracts
  const activeContracts = contracts.filter(
    (contract) => contract.status === "active" || contract.status === "pending"
  ).length;

  // Recent activities (mock data for now)
  const recentActivities = [
    {
      id: 1,
      type: "contract_signed",
      title: "New contract signed",
      description: "Unit 101 - Downtown Lofts",
      time: "2 hours ago",
      icon: FileText,
      color: "text-success",
    },
    {
      id: 2,
      type: "maintenance_request",
      title: "Maintenance request",
      description: "AC repair - Garden View Complex",
      time: "4 hours ago",
      icon: AlertCircle,
      color: "text-warning",
    },
    {
      id: 3,
      type: "payment_received",
      title: "Payment received",
      description: "$2,500 - Monthly rent",
      time: "1 day ago",
      icon: DollarSign,
      color: "text-primary",
    },
  ];

  const isLoading =
    loadingProperties || loadingUnits || loadingContracts || loadingTenants || loadingFinancial;

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header with Controls - Always Visible */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="space-y-1">
          <h1 className="text-3xl font-bold text-foreground">
            {t("pages.dashboard.propertyAssets.title")}
          </h1>
          <p className="text-muted-foreground">{t("pages.dashboard.propertyAssets.description")}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select
            value={timeRange}
            onValueChange={(value: any) => setTimeRange(value)}
            disabled={isLoading}>
            <SelectTrigger className="w-36 border-input bg-background text-foreground">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="week">{t("common.week")}</SelectItem>
              <SelectItem value="month">{t("common.month")}</SelectItem>
              <SelectItem value="quarter">{t("common.quarter")}</SelectItem>
              <SelectItem value="year">{t("common.year")}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Content Area - Shows Loading or Dashboard Content */}
      {isLoading ? (
        <div className="flex h-96 items-center justify-center">
          <Loading />
        </div>
      ) : (
        <div className="space-y-8">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {/* Total Properties */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-3">
                    <p className="text-sm font-medium text-muted-foreground">
                      {t("pages.dashboard.metrics.totalProperties")}
                    </p>
                    <div className="flex items-center space-x-3">
                      <p className="text-3xl font-bold text-foreground">{totalProperties}</p>
                      <Badge className="border-primary/20 bg-primary/10 text-primary">
                        <Home className="mr-1 size-3" />
                        Active
                      </Badge>
                    </div>
                  </div>
                  <div className="rounded-full bg-primary/10 p-3">
                    <Home className="size-6 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Total Units */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-3">
                    <p className="text-sm font-medium text-muted-foreground">
                      {t("pages.dashboard.metrics.totalUnits")}
                    </p>
                    <div className="flex items-center space-x-3">
                      <p className="text-3xl font-bold text-foreground">{totalUnits}</p>
                      <Badge className="border-success/20 bg-success/10 text-success">
                        {occupiedUnits}/{totalUnits}
                      </Badge>
                    </div>
                  </div>
                  <div className="rounded-full bg-success/10 p-3">
                    <Activity className="size-6 text-success" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Active Contracts */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-3">
                    <p className="text-sm font-medium text-muted-foreground">
                      {t("pages.dashboard.metrics.activeContracts")}
                    </p>
                    <div className="flex items-center space-x-3">
                      <p className="text-3xl font-bold text-foreground">{activeContracts}</p>
                      <Badge className="border-secondary/20 bg-secondary/10 text-secondary-foreground">
                        <FileText className="mr-1 size-3" />
                        Current
                      </Badge>
                    </div>
                  </div>
                  <div className="rounded-full bg-secondary/10 p-3">
                    <FileText className="size-6 text-secondary-foreground" />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Monthly Revenue */}
            <Card className="border-0 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-3">
                    <p className="text-sm font-medium text-muted-foreground">
                      {t("pages.dashboard.metrics.monthlyRevenue")}
                    </p>
                    <div className="flex items-center space-x-3">
                      <p className="text-3xl font-bold text-foreground">
                        ${monthlyRevenue.toLocaleString()}
                      </p>
                      <Badge
                        className={`${netIncome >= 0 ? "border-success/20 bg-success/10 text-success" : "border-destructive/20 bg-destructive/10 text-destructive"}`}>
                        {netIncome >= 0 ? (
                          <TrendingUp className="mr-1 size-3" />
                        ) : (
                          <TrendingDown className="mr-1 size-3" />
                        )}
                        {profitMargin.toFixed(1)}%
                      </Badge>
                    </div>
                  </div>
                  <div className="rounded-full bg-warning/10 p-3">
                    <DollarSign className="size-6 text-warning" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts and Analytics Section */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Occupancy Overview */}
            <Card className="border-0 shadow-sm lg:col-span-2">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Activity className="size-5" />
                  <span>{t("pages.dashboard.charts.occupancyOverview")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">
                    {t("pages.dashboard.occupancy.occupied")}
                  </span>
                  <span className="font-semibold">
                    {occupiedUnits} {t("common.units")}
                  </span>
                </div>
                <Progress value={occupancyRate} className="h-3" />
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">
                    {t("pages.dashboard.occupancy.vacant")}
                  </span>
                  <span className="font-semibold">
                    {vacantUnits} {t("common.units")}
                  </span>
                </div>
                <div className="grid grid-cols-2 gap-4 pt-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-success">{occupancyRate.toFixed(1)}%</p>
                    <p className="text-sm text-muted-foreground">
                      {t("pages.dashboard.occupancy.rate")}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-foreground">{totalTenants}</p>
                    <p className="text-sm text-muted-foreground">
                      {t("pages.dashboard.occupancy.tenants")}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Plus className="size-5" />
                  <span>{t("pages.dashboard.quickActions.title")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href={authProtectedPaths.PROPERTIES_NEW as any}>
                  <Button variant="outline" className="w-full justify-start">
                    <Home className="mr-2 size-4" />
                    {t("pages.dashboard.quickActions.addProperty")}
                  </Button>
                </Link>
                <Link href={authProtectedPaths.UNITS_NEW as any}>
                  <Button variant="outline" className="w-full justify-start">
                    <Activity className="mr-2 size-4" />
                    {t("pages.dashboard.quickActions.addUnit")}
                  </Button>
                </Link>
                <Link href={authProtectedPaths.CONTRACTS_NEW as any}>
                  <Button variant="outline" className="w-full justify-start">
                    <FileText className="mr-2 size-4" />
                    {t("pages.dashboard.quickActions.createContract")}
                  </Button>
                </Link>
                <Link href={authProtectedPaths.TENANTS_NEW as any}>
                  <Button variant="outline" className="w-full justify-start">
                    <Users className="mr-2 size-4" />
                    {t("pages.dashboard.quickActions.addTenant")}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activities and Overview */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Recent Activities */}
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Calendar className="size-5" />
                  <span>{t("pages.dashboard.recentActivities.title")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivities.map((activity) => (
                    <div
                      key={activity.id}
                      className="flex items-start space-x-4 rounded-lg bg-muted/30 p-4 transition-colors hover:bg-muted/50">
                      <div className="rounded-lg bg-background p-2 shadow-sm">
                        <activity.icon className={`h-4 w-4 ${activity.color}`} />
                      </div>
                      <div className="min-w-0 flex-1 space-y-1">
                        <div className="font-medium text-foreground">{activity.title}</div>
                        <div className="text-sm text-muted-foreground">{activity.description}</div>
                        <div className="text-xs text-muted-foreground">{activity.time}</div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="pt-6 text-center">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-muted-foreground hover:text-foreground">
                    <Eye className="mr-2 size-4" />
                    {t("pages.dashboard.recentActivities.viewAll")}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Property Portfolio Summary */}
            <Card className="border-0 shadow-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-2 text-lg">
                  <Home className="size-5" />
                  <span>{t("pages.dashboard.portfolio.title")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="rounded-lg bg-primary/10 p-3 text-center">
                    <p className="text-lg font-bold text-primary">{totalProperties}</p>
                    <p className="text-xs text-primary/90">
                      {t("pages.dashboard.portfolio.properties")}
                    </p>
                  </div>
                  <div className="rounded-lg bg-success/10 p-3 text-center">
                    <p className="text-lg font-bold text-success">{totalUnits}</p>
                    <p className="text-xs text-success/90">
                      {t("pages.dashboard.portfolio.units")}
                    </p>
                  </div>
                  <div className="rounded-lg bg-secondary/10 p-3 text-center">
                    <p className="text-lg font-bold text-secondary-foreground">{activeContracts}</p>
                    <p className="text-xs text-secondary-foreground/90">
                      {t("pages.dashboard.portfolio.contracts")}
                    </p>
                  </div>
                  <div className="rounded-lg bg-warning/10 p-3 text-center">
                    <p className="text-lg font-bold text-warning">
                      ${monthlyRevenue.toLocaleString()}
                    </p>
                    <p className="text-xs text-warning/90">
                      {t("pages.dashboard.portfolio.revenue")}
                    </p>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("pages.dashboard.portfolio.performance")}
                    </span>
                    <span className="text-sm font-semibold text-foreground">
                      {occupancyRate.toFixed(1)}% {t("pages.dashboard.occupancy.rate")}
                    </span>
                  </div>
                  <Progress value={occupancyRate} className="mt-2 h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
