import { z } from "zod";

const addressSchema = z.object({
  street: z.string().min(1, "validation.streetRequired"),
  city: z.string().min(1, "validation.cityRequired"),
  state: z.string().min(1, "validation.stateRequired"),
  zip_code: z.string().min(1, "validation.zipCodeRequired"),
  country: z.string().min(1, "validation.countryRequired"),
});

export const createPropertySchema = z.object({
  name: z.string().min(1, "validation.propertyNameRequired"),
  address: addressSchema,
  property_type: z.enum(["residential", "commercial", "mixed"], {
    errorMap: () => ({ message: "validation.propertyTypeRequired" }),
  }),
  description: z.string().optional(),
  owner_name: z.string().min(1, "validation.ownerNameRequired"),
  owner_email: z.string().email("validation.invalidEmail"),
  owner_phone: z.string().min(1, "validation.ownerPhoneRequired"),
  purchase_price: z.number().positive("validation.purchasePriceMustBePositive").optional(),
  purchase_date: z.string().optional(),
  images: z
    .array(
      z.object({
        name: z.string(),
        image: z.string(),
      })
    )
    .optional(),
});

export const updatePropertySchema = createPropertySchema.partial().extend({
  id: z.string().min(1, "validation.idRequired"),
});

export const propertyFilterSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  query: z.string().optional(),
  property_type: z.enum(["residential", "commercial", "mixed"]).optional(),
  status: z.enum(["active", "inactive"]).optional(),
});

export type CreatePropertyFormValues = z.infer<typeof createPropertySchema>;
export type UpdatePropertyFormValues = z.infer<typeof updatePropertySchema>;
export type PropertyFilterValues = z.infer<typeof propertyFilterSchema>;
