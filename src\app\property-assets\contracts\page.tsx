"use client";

import { useMemo, useState } from "react";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/property-assets/components/ContractList/column";
import { useContracts, useDeleteContract } from "@/features/property-assets/hooks/useContracts";
import { Contract } from "@/features/property-assets/types";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function ContractsPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { data: contractsData, isLoading, isFetching, refetch } = useContracts(options);

  const deleteContractMutation = useDeleteContract();

  const contracts = contractsData?.items || [];
  const total = contractsData?.total || 0;
  const isTableLoading = isLoading || isFetching;

  const filters = useMemo(
    () => [
      {
        id: "status",
        type: "selectBox" as const,
        title: t("pages.contracts.filters.status"),
        defaultValue: getInitialParams["status"],
        options: [
          { value: "draft", label: t("pages.contracts.status.draft") },
          { value: "active", label: t("pages.contracts.status.active") },
          { value: "expired", label: t("pages.contracts.status.expired") },
          { value: "terminated", label: t("pages.contracts.status.terminated") },
          { value: "pending", label: t("pages.contracts.status.pending") },
        ],
      },
      {
        id: "contract_type",
        type: "selectBox" as const,
        title: t("pages.contracts.filters.contractType"),
        defaultValue: getInitialParams["contract_type"],
        options: [
          { value: "monthly", label: t("pages.contracts.types.monthly") },
          { value: "annual", label: t("pages.contracts.types.annual") },
          { value: "profit_sharing", label: t("pages.contracts.types.profitSharing") },
          { value: "revenue_sharing", label: t("pages.contracts.types.revenueSharing") },
        ],
      },
      {
        id: "property_id",
        type: "selectBox" as const,
        title: t("pages.contracts.filters.property"),
        defaultValue: getInitialParams["property_id"],
        options: [
          // This would typically be populated from a properties list
          { value: "1", label: "Property 1" },
          { value: "2", label: "Property 2" },
        ],
      },
      {
        id: "start_date",
        type: "date" as const,
        title: t("pages.contracts.filters.startDate"),
        defaultValue: {
          from: getInitialParams["start_date_from"],
          to: getInitialParams["start_date_to"],
        },
      },
      {
        id: "end_date",
        type: "date" as const,
        title: t("pages.contracts.filters.endDate"),
        defaultValue: {
          from: getInitialParams["end_date_from"],
          to: getInitialParams["end_date_to"],
        },
      },
      {
        id: "created_at",
        type: "date" as const,
        title: t("pages.contracts.headers.createdAt"),
        defaultValue: {
          from: getInitialParams["created_at_from"],
          to: getInitialParams["created_at_to"],
        },
      },
      {
        id: "updated_at",
        type: "date" as const,
        title: t("pages.contracts.headers.updatedAt"),
        defaultValue: {
          from: getInitialParams["updated_at_from"],
          to: getInitialParams["updated_at_to"],
        },
      },
    ],
    [t, getInitialParams]
  );

  const filterConfig = useMemo(
    () => ({
      showSearch: true,
      filterType: "contracts",
      searchPlaceHolder: t("pages.contracts.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter: filters,
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [t, getInitialParams, filters, handleParamSearch, isTableLoading]
  );

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "dropdown" as const,
        title: t("pages.contracts.add"),
        icon: PlusIcon,
        items: [
          {
            type: "add_manual",
            title: t("pages.contracts.actions.addManual"),
            icon: PlusIcon,
            href: authProtectedPaths.CONTRACTS_NEW,
          },
        ],
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.contracts.title")}
        filterType="contracts"
        data={contracts}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(deleteContractMutation, isFetching, t)}
        data={contracts}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          // Bulk delete functionality can be added later
          console.log("Bulk delete selected:", listIndexId);
          handleRestRows();
        }}
      />
    </TableCard>
  );
}
