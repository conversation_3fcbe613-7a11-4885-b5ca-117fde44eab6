import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

async function validatePropertiesPage() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('🔐 Authenticating and navigating to properties page...');
    await navigateWithAuth(page, 'http://localhost:3000/property-assets/properties');
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    console.log('📸 Taking screenshot of properties page...');
    await page.screenshot({ 
      path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-page-validation.png',
      fullPage: true 
    });
    
    // Validate page elements
    console.log('✅ Validating page elements...');
    
    // Check page title
    const title = await page.$eval('h1', el => el.textContent);
    console.log(`📝 Page title: ${title}`);
    
    // Check metrics cards
    const metricsCards = await page.$$('.grid .card');
    console.log(`📊 Found ${metricsCards.length} metrics cards`);
    
    // Check search functionality
    const searchInput = await page.$('input[placeholder*="Search"]');
    console.log(`🔍 Search input found: ${searchInput ? 'Yes' : 'No'}`);
    
    // Check property cards
    const propertyCards = await page.$$('.grid .card, .space-y-4 .card');
    console.log(`🏢 Found ${propertyCards.length} property-related cards`);
    
    // Check filters
    const filters = await page.$$('select');
    console.log(`🎛️ Found ${filters.length} filter dropdowns`);
    
    // Check view mode buttons
    const viewModeButtons = await page.$$('button[class*="rounded"]');
    console.log(`👁️ Found ${viewModeButtons.length} view mode buttons`);
    
    // Test responsive design
    console.log('📱 Testing mobile responsive design...');
    await page.setViewport({ width: 375, height: 667 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-page-mobile.png',
      fullPage: true 
    });
    
    // Test tablet design
    console.log('📟 Testing tablet responsive design...');
    await page.setViewport({ width: 768, height: 1024 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-page-tablet.png',
      fullPage: true 
    });
    
    console.log('✅ Properties page validation completed successfully!');
    console.log('📸 Screenshots saved:');
    console.log('   - properties-page-validation.png (desktop)');
    console.log('   - properties-page-mobile.png');
    console.log('   - properties-page-tablet.png');
    
    return {
      success: true,
      title,
      metricsCards: metricsCards.length,
      searchInput: !!searchInput,
      propertyCards: propertyCards.length,
      filters: filters.length,
      viewModeButtons: viewModeButtons.length
    };
    
  } catch (error) {
    console.error('❌ Error validating properties page:', error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run validation
validatePropertiesPage().then(result => {
  if (result.success) {
    console.log('\n🎉 Properties page validation PASSED!');
    console.log(`✅ Title: ${result.title}`);
    console.log(`✅ Metrics cards: ${result.metricsCards}`);
    console.log(`✅ Search input: ${result.searchInput}`);
    console.log(`✅ Property cards: ${result.propertyCards}`);
    console.log(`✅ Filters: ${result.filters}`);
    console.log(`✅ View mode buttons: ${result.viewModeButtons}`);
  } else {
    console.log('\n❌ Properties page validation FAILED!');
    console.log(`Error: ${result.error}`);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
});