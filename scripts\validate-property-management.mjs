import puppeteer from 'puppeteer';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get current file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import auth utility
const authUtilPath = join(__dirname, 'utils', 'auth.mjs');
const { navigateWithAuth } = await import(authUtilPath);

async function validatePropertyManagement() {
  let browser;
  
  try {
    console.log('🚀 Starting Property Management Enhancement UI validation...');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: null,
      args: ['--start-maximized', '--no-sandbox']
    });

    const page = await browser.newPage();
    
    // Test Property Valuation Component
    console.log('📊 Testing Property Valuation Interface...');
    const valuationSuccess = await navigateWithAuth(page, 'http://localhost:3000/property-assets/valuation');
    
    if (!valuationSuccess) {
      throw new Error('Failed to navigate to Property Valuation');
    }

    await new Promise(resolve => setTimeout(resolve, 2000));
    await page.screenshot({ 
      path: 'screenshots/property-valuation-main.png', 
      fullPage: true 
    });

    // Test Document Management
    console.log('📁 Testing Document Management System...');
    const docSuccess = await navigateWithAuth(page, 'http://localhost:3000/property-assets/documents');
    
    if (!docSuccess) {
      throw new Error('Failed to navigate to Document Management');
    }

    await new Promise(resolve => setTimeout(resolve, 2000));
    await page.screenshot({ 
      path: 'screenshots/document-management-main.png', 
      fullPage: true 
    });

    // Test Asset Categories
    console.log('🏷️ Testing Asset Categories Management...');
    const categorySuccess = await navigateWithAuth(page, 'http://localhost:3000/property-assets/categories');
    
    if (!categorySuccess) {
      throw new Error('Failed to navigate to Asset Categories');
    }

    await new Promise(resolve => setTimeout(resolve, 2000));
    await page.screenshot({ 
      path: 'screenshots/asset-categories-main.png', 
      fullPage: true 
    });

    // Test Property Gallery
    console.log('🖼️ Testing Property Image Gallery...');
    const gallerySuccess = await navigateWithAuth(page, 'http://localhost:3000/property-assets/gallery');
    
    if (!gallerySuccess) {
      throw new Error('Failed to navigate to Property Gallery');
    }

    await new Promise(resolve => setTimeout(resolve, 2000));
    await page.screenshot({ 
      path: 'screenshots/property-gallery-main.png', 
      fullPage: true 
    });

    // Validation Results
    const validationResults = [];
    
    // Test component rendering and functionality
    const components = [
      { name: 'Property Valuation', url: 'http://localhost:3000/property-assets/valuation' },
      { name: 'Document Management', url: 'http://localhost:3000/property-assets/documents' },
      { name: 'Asset Categories', url: 'http://localhost:3000/property-assets/categories' },
      { name: 'Property Gallery', url: 'http://localhost:3000/property-assets/gallery' }
    ];

    for (const component of components) {
      try {
        await navigateWithAuth(page, component.url);
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Check for main title/header
        const hasHeader = await page.$('h1, h2, h3');
        
        // Check for interactive elements
        const buttons = await page.$$('button');
        const inputs = await page.$$('input');
        const selects = await page.$$('select, [role="combobox"]');
        
        // Check for cards/content areas
        const cards = await page.$$('.card, [class*="card"]');
        
        validationResults.push({
          component: component.name,
          passed: hasHeader && buttons.length > 0 && cards.length > 0,
          details: {
            hasHeader: !!hasHeader,
            buttonCount: buttons.length,
            inputCount: inputs.length,
            selectCount: selects.length,
            cardCount: cards.length
          }
        });
        
        console.log(`✅ ${component.name}: Header=${!!hasHeader}, Buttons=${buttons.length}, Cards=${cards.length}`);
        
      } catch (error) {
        validationResults.push({
          component: component.name,
          passed: false,
          details: `Error: ${error.message}`
        });
        console.log(`❌ ${component.name}: ${error.message}`);
      }
    }

    // Test responsive design for first component
    console.log('📱 Testing responsive design...');
    await navigateWithAuth(page, 'http://localhost:3000/property-assets/valuation');
    
    // Mobile view
    await page.setViewport({ width: 375, height: 667 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: 'screenshots/property-management-mobile.png', 
      fullPage: true 
    });
    
    // Tablet view
    await page.setViewport({ width: 768, height: 1024 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: 'screenshots/property-management-tablet.png', 
      fullPage: true 
    });
    
    // Desktop view
    await page.setViewport({ width: 1920, height: 1080 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: 'screenshots/property-management-desktop.png', 
      fullPage: true 
    });

    validationResults.push({
      component: 'Responsive Design',
      passed: true,
      details: 'Screenshots taken for mobile, tablet, and desktop views'
    });

    // Generate validation report
    const report = {
      timestamp: new Date().toISOString(),
      testSuite: 'Property Management Enhancement UI Validation',
      totalTests: validationResults.length,
      passedTests: validationResults.filter(r => r.passed).length,
      failedTests: validationResults.filter(r => !r.passed).length,
      results: validationResults
    };
    
    const reportPath = 'test-reports/property-management-validation.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📋 Property Management Enhancement Validation Report:');
    console.log(`✅ Passed: ${report.passedTests}/${report.totalTests} tests`);
    console.log(`❌ Failed: ${report.failedTests}/${report.totalTests} tests`);
    
    if (report.failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      validationResults.filter(r => !r.passed).forEach(result => {
        console.log(`  - ${result.component}: ${typeof result.details === 'string' ? result.details : JSON.stringify(result.details)}`);
      });
    }
    
    console.log(`\n📁 Report saved to: ${reportPath}`);
    console.log('📁 Screenshots saved to: screenshots/');
    
    return report;
    
  } catch (error) {
    console.error('❌ Error during validation:', error);
    throw error;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run validation
validatePropertyManagement()
  .then(report => {
    console.log('\n🎉 Property Management Enhancement validation completed!');
    process.exit(report.failedTests > 0 ? 1 : 0);
  })
  .catch(error => {
    console.error('💥 Validation failed:', error);
    process.exit(1);
  });