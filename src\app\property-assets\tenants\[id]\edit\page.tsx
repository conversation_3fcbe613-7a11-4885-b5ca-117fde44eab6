"use client";

import { Suspense } from "react";
import { Metadata } from "next";

import { TenantForm } from "@/features/property-assets/components/TenantForm";
import { useTenant } from "@/features/property-assets/hooks/useTenants";

interface EditTenantPageProps {
  params: {
    id: string;
  };
}

function EditTenantContent({ tenantId }: { tenantId: string }) {
  const { data: tenant, isLoading, error } = useTenant(tenantId);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading tenant data...</div>
      </div>
    );
  }

  if (error || !tenant) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center text-red-500">Error loading tenant data. Please try again.</div>
      </div>
    );
  }

  return <TenantForm initialData={tenant} isEditing={true} />;
}

export default function EditTenantPage({ params }: EditTenantPageProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EditTenantContent tenantId={params.id} />
    </Suspense>
  );
}
