#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';

const CONFIG = {
  LOGIN_URL: 'http://localhost:3000/login',
  FORM_URL: 'http://localhost:3000/property-assets/properties/new',
  LOGIN_CREDENTIALS: {
    username: 'onexapis_admin',
    password: 'Admin@123'
  },
  SCREENSHOT_DIR: './screenshots/form-review'
};

// Ensure screenshot directory exists
if (!fs.existsSync(CONFIG.SCREENSHOT_DIR)) {
  fs.mkdirSync(CONFIG.SCREENSHOT_DIR, { recursive: true });
}

async function performLogin(page) {
  console.log('🔐 Logging in...');
  
  try {
    await page.goto(CONFIG.LOGIN_URL, { waitUntil: 'networkidle2', timeout: 30000 });
    
    await page.waitForSelector('input[name="email"], input[type="email"], input[placeholder*="email" i]', { timeout: 10000 });
    
    const usernameInput = await page.$('input[name="email"]') || 
                         await page.$('input[type="email"]') || 
                         await page.$('input[placeholder*="email" i]');
    
    if (!usernameInput) {
      throw new Error('Username input field not found');
    }
    
    await usernameInput.click({ clickCount: 3 });
    await usernameInput.type(CONFIG.LOGIN_CREDENTIALS.username);
    
    const passwordInput = await page.$('input[name="password"]') || await page.$('input[type="password"]');
    if (!passwordInput) {
      throw new Error('Password input field not found');
    }
    
    await passwordInput.click({ clickCount: 3 });
    await passwordInput.type(CONFIG.LOGIN_CREDENTIALS.password);
    
    const loginButton = await page.$('button[type="submit"]') || await page.$('input[type="submit"]');
    
    if (!loginButton) {
      throw new Error('Login button not found');
    }
    
    await loginButton.click();
    
    await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });
    
    console.log('✅ Logged in successfully');
    return true;
  } catch (error) {
    console.log(`❌ Login failed: ${error.message}`);
    return false;
  }
}

async function captureFormScreenshots(page) {
  console.log('📸 Capturing form screenshots...');
  
  await page.goto(CONFIG.FORM_URL, { waitUntil: 'networkidle2', timeout: 30000 });
  
  // Wait for form to load
  await page.waitForSelector('form, .form-container', { timeout: 10000 });
  
  const viewports = [
    { name: 'desktop', width: 1400, height: 900 },
    { name: 'tablet', width: 768, height: 1024 },
    { name: 'mobile', width: 375, height: 667 }
  ];
  
  for (const viewport of viewports) {
    console.log(`📱 Capturing ${viewport.name} view (${viewport.width}x${viewport.height})`);
    
    await page.setViewport(viewport);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Let page adjust
    
    const screenshotPath = path.join(CONFIG.SCREENSHOT_DIR, `property-form-${viewport.name}.png`);
    await page.screenshot({ 
      path: screenshotPath,
      fullPage: true
    });
    
    console.log(`   ✅ ${viewport.name} screenshot saved: ${screenshotPath}`);
  }
}

async function analyzeFormStructure(page) {
  console.log('🔍 Analyzing form structure...');
  
  const formAnalysis = await page.evaluate(() => {
    // Find the main form container
    const form = document.querySelector('form') || document.querySelector('.form-container');
    if (!form) return { error: 'Form not found' };
    
    // Analyze form sections/cards
    const cards = Array.from(document.querySelectorAll('.card, [class*="card"], .bg-card, .border'));
    const formSections = cards.map((card, index) => {
      const title = card.querySelector('h1, h2, h3, h4, .font-semibold, .font-bold, .text-lg, .text-xl');
      const inputs = Array.from(card.querySelectorAll('input, select, textarea, button[role="combobox"]'));
      
      return {
        index,
        title: title?.textContent?.trim() || `Section ${index + 1}`,
        inputCount: inputs.length,
        inputs: inputs.map(input => ({
          type: input.type || input.tagName.toLowerCase(),
          name: input.name || input.getAttribute('name'),
          placeholder: input.placeholder,
          label: input.labels?.[0]?.textContent?.trim() || 
                 input.closest('div')?.querySelector('label')?.textContent?.trim(),
          classes: input.className
        }))
      };
    });
    
    // Check overall layout classes
    const formClasses = form.className;
    const containerClasses = form.parentElement?.className || '';
    
    // Look for spacing and layout issues
    const spacingElements = Array.from(document.querySelectorAll('[class*="gap"], [class*="space"], [class*="p-"], [class*="m-"]'));
    
    return {
      formFound: true,
      formClasses,
      containerClasses,
      sectionCount: formSections.length,
      sections: formSections,
      totalInputs: formSections.reduce((sum, section) => sum + section.inputCount, 0),
      layoutInfo: {
        hasGrid: formClasses.includes('grid') || containerClasses.includes('grid'),
        hasFlex: formClasses.includes('flex') || containerClasses.includes('flex'),
        spacingClasses: spacingElements.length
      }
    };
  });
  
  console.log('\n📋 Form Structure Analysis:');
  console.log('================================');
  
  if (formAnalysis.error) {
    console.log(`❌ ${formAnalysis.error}`);
    return;
  }
  
  console.log(`📦 Form Container Classes: ${formAnalysis.formClasses}`);
  console.log(`🏠 Parent Container Classes: ${formAnalysis.containerClasses}`);
  console.log(`📊 Total Sections: ${formAnalysis.sectionCount}`);
  console.log(`📝 Total Inputs: ${formAnalysis.totalInputs}`);
  console.log(`🎨 Layout Type: ${formAnalysis.layoutInfo.hasGrid ? 'Grid' : formAnalysis.layoutInfo.hasFlex ? 'Flex' : 'Block'}`);
  
  console.log('\n📑 Form Sections:');
  formAnalysis.sections.forEach((section, i) => {
    console.log(`  ${i + 1}. ${section.title} (${section.inputCount} inputs)`);
    section.inputs.forEach((input, j) => {
      console.log(`     ${j + 1}. ${input.type} - ${input.label || input.name || 'No label'}`);
    });
  });
  
  return formAnalysis;
}

async function checkResponsiveLayout(page) {
  console.log('\n📱 Checking responsive layout...');
  
  const viewports = [
    { name: 'Mobile', width: 375, height: 667 },
    { name: 'Tablet', width: 768, height: 1024 },
    { name: 'Desktop', width: 1400, height: 900 }
  ];
  
  for (const viewport of viewports) {
    await page.setViewport(viewport);
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const layoutInfo = await page.evaluate(() => {
      const form = document.querySelector('form') || document.querySelector('.form-container');
      if (!form) return null;
      
      const computedStyle = window.getComputedStyle(form);
      const cards = Array.from(document.querySelectorAll('.card, [class*="card"], .bg-card'));
      
      return {
        formWidth: form.offsetWidth,
        formPadding: computedStyle.padding,
        cardCount: cards.length,
        cardWidths: cards.map(card => card.offsetWidth),
        overflowIssues: cards.some(card => card.scrollWidth > card.offsetWidth)
      };
    });
    
    if (layoutInfo) {
      console.log(`  ${viewport.name} (${viewport.width}px):`);
      console.log(`    Form Width: ${layoutInfo.formWidth}px`);
      console.log(`    Cards: ${layoutInfo.cardCount}`);
      console.log(`    Overflow Issues: ${layoutInfo.overflowIssues ? 'Yes ⚠️' : 'No ✅'}`);
    }
  }
}

async function main() {
  console.log('🎨 Starting visual form review...\n');
  
  const browser = await puppeteer.launch({
    headless: false, // Show browser for visual inspection
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    defaultViewport: { width: 1400, height: 900 }
  });
  
  try {
    const page = await browser.newPage();
    
    const loginSuccess = await performLogin(page);
    if (!loginSuccess) {
      console.log('❌ Cannot proceed without login');
      return;
    }
    
    await captureFormScreenshots(page);
    await analyzeFormStructure(page);
    await checkResponsiveLayout(page);
    
    console.log('\n🎯 Visual review complete!');
    console.log('📸 Screenshots saved in: ./screenshots/form-review/');
    console.log('🌐 Browser window left open for manual inspection');
    
    // Keep browser open for manual inspection
    console.log('\n⏳ Keeping browser open for 30 seconds for manual review...');
    await new Promise(resolve => setTimeout(resolve, 30000));
    
  } catch (error) {
    console.error('❌ Visual review failed:', error);
  } finally {
    await browser.close();
  }
}

main().catch(console.error);