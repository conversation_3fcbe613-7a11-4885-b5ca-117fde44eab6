"use client";

import { useCallback, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";
import { Form, useZodForm } from "@/components/ui/form";
import { authProtectedPaths } from "@/constants/paths";

import { useCreateProperty, useUpdateProperty } from "../../hooks/useProperties";
import type { Property } from "../../types";
import {
  createPropertySchema,
  updatePropertySchema,
  type CreatePropertyFormValues,
  type UpdatePropertyFormValues,
} from "../../utils/validators/property";
import { AddressSection } from "./AddressSection";
import { BasicInformationSection } from "./BasicInformationSection";
import { ImageUploadSection } from "./ImageUploadSection";
import { OwnerInformationSection } from "./OwnerInformationSection";
import { PurchaseInformationSection } from "./PurchaseInformationSection";

interface PropertyFormProps {
  initialData?: Property;
  isEditing?: boolean;
}

export function PropertyForm({ initialData, isEditing = false }: PropertyFormProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<{ name: string; image: string }[]>([]);

  // Mutations
  const createPropertyMutation = useCreateProperty();
  const updatePropertyMutation = useUpdateProperty();

  // Form setup
  const form = useZodForm({
    schema: isEditing ? updatePropertySchema : createPropertySchema,
    defaultValues:
      isEditing && initialData
        ? {
            id: initialData.id,
            name: initialData.name,
            address: initialData.address,
            property_type: initialData.property_type,
            description: initialData.description,
            owner_name: initialData.owner_name,
            owner_email: initialData.owner_email,
            owner_phone: initialData.owner_phone,
            purchase_price: initialData.purchase_price,
            purchase_date: initialData.purchase_date,
            images: initialData.images?.map((img) => ({ name: img.name, image: img.url })) || [],
          }
        : {
            name: "",
            address: {
              street: "",
              city: "",
              state: "",
              zip_code: "",
              country: "",
            },
            property_type: "residential" as const,
            description: "",
            owner_name: "",
            owner_email: "",
            owner_phone: "",
            purchase_price: undefined,
            purchase_date: "",
            images: [],
          },
  });

  // Image upload handler
  const handleImageUpload = useCallback(
    (files: File[]) => {
      const newImages = files.map((file) => ({
        name: file.name,
        image: URL.createObjectURL(file), // In real implementation, this would be uploaded to server
      }));

      setUploadedImages((prev) => [...prev, ...newImages]);
      form.setValue("images", [...(form.getValues("images") || []), ...newImages]);
    },
    [form]
  );

  // Remove image handler
  const handleRemoveImage = useCallback(
    (index: number) => {
      setUploadedImages((prev) => prev.filter((_, i) => i !== index));
      const currentImages = form.getValues("images") || [];
      form.setValue(
        "images",
        currentImages.filter((image: any, i: number) => i !== index)
      );
    },
    [form]
  );

  // Form submission
  const onSubmit = async (values: CreatePropertyFormValues | UpdatePropertyFormValues) => {
    setIsSubmitting(true);

    try {
      if (isEditing && "id" in values) {
        await updatePropertyMutation.mutateAsync({ id: values.id, data: values } as any);
        toast.success(t("pages.properties.updateSuccess"));
      } else {
        await createPropertyMutation.mutateAsync(values as CreatePropertyFormValues);
        toast.success(t("pages.properties.createSuccess"));
      }

      router.push(authProtectedPaths.PROPERTIES as any);
    } catch (error) {
      toast.error(
        isEditing ? t("pages.properties.updateError") : t("pages.properties.createError")
      );
      // TODO: Implement proper error logging
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex min-h-screen flex-col bg-background">
      {/* Header */}
      <div className="flex-none border-b bg-card">
        <div className="px-6 py-4">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              {isEditing
                ? t("pages.properties.editProperty")
                : t("pages.properties.createProperty")}
            </h1>
            <p className="mt-1 text-sm text-muted-foreground">
              {isEditing
                ? t("pages.properties.editDescription")
                : t("pages.properties.createDescription")}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 p-6">
        <Form {...form}>
          <form id="property-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <BasicInformationSection form={form} />
            <AddressSection form={form} />
            <OwnerInformationSection form={form} />
            <PurchaseInformationSection form={form} />
            <ImageUploadSection
              uploadedImages={uploadedImages}
              onImageUpload={handleImageUpload}
              onRemoveImage={handleRemoveImage}
            />
          </form>
        </Form>
      </div>

      {/* Sticky Footer - Following OneX ERP Standard - Full Width */}
      <div className="sticky bottom-0 z-50 w-full flex-none border-t bg-card shadow-lg">
        <div className="mx-auto max-w-7xl px-6 py-4">
          <div className="flex justify-end">
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.push(authProtectedPaths.PROPERTIES as any)}
                className="px-6">
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={isSubmitting} form="property-form" className="px-6">
                {isSubmitting && <Loader2 className="mr-2 size-4 animate-spin" />}
                {isEditing ? t("common.update") : t("common.add")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
