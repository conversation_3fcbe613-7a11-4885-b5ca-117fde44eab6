import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
import type { ApiResponse, Contract, CreateContract, PaginatedResponse } from "../types";

// API functions (connecting to Vietnamese contracts API endpoints)
const contractsApi = {
  // Get all contracts
  getContracts: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: Contract["status"];
    contract_type?: Contract["contract_type"];
    property_id?: string;
    unit_id?: string;
    tenant_id?: string;
  }): Promise<PaginatedResponse<Contract>> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.limit) searchParams.append("limit", params.limit.toString());
    if (params?.search) searchParams.append("search", params.search);
    if (params?.status) searchParams.append("status", params.status);
    if (params?.contract_type) searchParams.append("contract_type", params.contract_type);
    if (params?.property_id) searchParams.append("property_id", params.property_id);
    if (params?.unit_id) searchParams.append("unit_id", params.unit_id);
    if (params?.tenant_id) searchParams.append("tenant_id", params.tenant_id);

    const response = await fetch(`/api/property-assets/contracts?${searchParams.toString()}`);
    if (!response.ok) {
      throw new Error("Failed to fetch contracts");
    }

    return response.json();
  },

  // Get contract by ID
  getContract: async (id: string): Promise<Contract> => {
    const response = await fetch(`/api/property-assets/contracts/${id}`);
    if (!response.ok) {
      throw new Error("Failed to fetch contract");
    }

    const result = await response.json();
    return result.data;
  },

  // Create contract
  createContract: async (data: CreateContract): Promise<Contract> => {
    const response = await fetch("/api/property-assets/contracts", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to create contract");
    }

    const result = await response.json();
    return result.data;
  },

  // Update contract
  updateContract: async (id: string, data: Partial<CreateContract>): Promise<Contract> => {
    const response = await fetch(`/api/property-assets/contracts/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update contract");
    }

    const result = await response.json();
    return result.data;
  },

  // Delete contract
  deleteContract: async (id: string): Promise<void> => {
    const response = await fetch(`/api/property-assets/contracts/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error("Failed to delete contract");
    }
  },
};

// Query keys
export const contractsKeys = {
  all: ["contracts"] as const,
  lists: () => [...contractsKeys.all, "list"] as const,
  list: (params?: any) => [...contractsKeys.lists(), params] as const,
  details: () => [...contractsKeys.all, "detail"] as const,
  detail: (id: string) => [...contractsKeys.details(), id] as const,
};

// Hooks
export const useContracts = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: Contract["status"];
  contract_type?: Contract["contract_type"];
  property_id?: string;
  unit_id?: string;
  tenant_id?: string;
}) => {
  return useQuery({
    queryKey: contractsKeys.list(params),
    queryFn: () => contractsApi.getContracts(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useContract = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: contractsKeys.detail(id),
    queryFn: () => contractsApi.getContract(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: contractsApi.createContract,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: contractsKeys.lists() });
      toast.success("Tạo hợp đồng thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi tạo hợp đồng: ${error.message}`);
    },
  });
};

export const useUpdateContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateContract> }) =>
      contractsApi.updateContract(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: contractsKeys.lists() });
      queryClient.setQueryData(contractsKeys.detail(data.id), data);
      toast.success("Cập nhật hợp đồng thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi cập nhật hợp đồng: ${error.message}`);
    },
  });
};

export const useDeleteContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: contractsApi.deleteContract,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: contractsKeys.lists() });
      toast.success("Xóa hợp đồng thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi xóa hợp đồng: ${error.message}`);
    },
  });
};
