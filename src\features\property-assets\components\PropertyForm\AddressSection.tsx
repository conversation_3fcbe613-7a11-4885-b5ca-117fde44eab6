"use client";

import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface AddressSectionProps {
  form: any;
}

export function AddressSection({ form }: AddressSectionProps) {
  const { t } = useTranslation();

  return (
    <Card className="border-border shadow-sm">
      <CardHeader className="border-b border-border bg-background py-3">
        <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
          <div className="size-2 rounded-full bg-success"></div>
          {t("pages.properties.addressInformation")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 bg-card p-4">
        {/* Street Address */}
        <FormField
          control={form.control}
          name="address.street"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.address.street")} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.properties.placeholders.street")}
                  className="h-9 border-input focus:border-primary focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* City, State, Zip Row */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <FormField
            control={form.control}
            name="address.city"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.address.city")} <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("pages.properties.placeholders.city")}
                    className="h-9 border-input focus:border-primary focus:ring-ring"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.state"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.address.state")} <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("pages.properties.placeholders.state")}
                    className="h-9 border-input focus:border-primary focus:ring-ring"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address.zip_code"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.address.zipCode")}{" "}
                  <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("pages.properties.placeholders.zipCode")}
                    className="h-9 border-input focus:border-primary focus:ring-ring"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />
        </div>

        {/* Country */}
        <FormField
          control={form.control}
          name="address.country"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.address.country")} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.properties.placeholders.country")}
                  className="h-9 border-input focus:border-primary focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
}
