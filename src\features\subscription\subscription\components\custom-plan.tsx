"use client";

import { useState } from "react";
import { useTranslation } from "react-i18next";
import { MessageSquare, Users, HardDrive, Sparkles } from "lucide-react";

import { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader, Input } from "@/components/ui";

interface CustomPlanRequirements {
  messages: number;
  staff: number;
  storage: number;
}

export default function CustomPlan() {
  const { t } = useTranslation();
  const [requirements, setRequirements] = useState<CustomPlanRequirements>({
    messages: 10000,
    staff: 10,
    storage: 500,
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleRequirementChange = (field: keyof CustomPlanRequirements, value: number) => {
    setRequirements(prev => ({
      ...prev,
      [field]: Math.max(0, value)
    }));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // TODO: Implement API call to submit custom plan requirements
      console.log("Custom plan requirements:", requirements);
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat("en-US").format(num);
  };

  return (
    <Card className="relative overflow-hidden border-2 border-dashed border-primary/30 bg-gradient-to-br from-primary/5 to-primary/10">
      {/* Popular Badge */}
      <div className="absolute -right-8 top-4 rotate-45 bg-primary px-8 py-1">
        <span className="text-xs font-medium text-primary-foreground">Custom</span>
      </div>

      <CardHeader className="pb-4">
        <div className="flex items-center gap-2">
          <Sparkles className="size-5 text-primary" />
          <h3 className="text-xl font-semibold text-foreground">
            {t("pages.subscription.customPlan.title", "Custom Plan")}
          </h3>
        </div>
        <p className="text-sm text-muted-foreground">
          {t("pages.subscription.customPlan.description", "Tell us your requirements and we'll create a perfect plan for you")}
        </p>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Requirements Form */}
        <div className="space-y-4">
          <h4 className="font-medium text-foreground">
            {t("pages.subscription.customPlan.requirements", "Your Requirements")}
          </h4>

          {/* Messages */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <MessageSquare className="size-4 text-primary" />
              <label className="text-sm font-medium text-foreground">
                {t("pages.subscription.customPlan.messages", "Messages per month")}
              </label>
            </div>
            <Input
              type="number"
              value={requirements.messages}
              onChange={(e) => handleRequirementChange("messages", parseInt(e.target.value) || 0)}
              className="w-full"
              min="0"
              step="1000"
            />
            <p className="text-xs text-muted-foreground">
              Current: {formatNumber(requirements.messages)} messages
            </p>
          </div>

          {/* Staff */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Users className="size-4 text-primary" />
              <label className="text-sm font-medium text-foreground">
                {t("pages.subscription.customPlan.staff", "Virtual assistants")}
              </label>
            </div>
            <Input
              type="number"
              value={requirements.staff}
              onChange={(e) => handleRequirementChange("staff", parseInt(e.target.value) || 0)}
              className="w-full"
              min="0"
              step="1"
            />
            <p className="text-xs text-muted-foreground">
              Current: {formatNumber(requirements.staff)} assistants
            </p>
          </div>

          {/* Storage */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <HardDrive className="size-4 text-primary" />
              <label className="text-sm font-medium text-foreground">
                {t("pages.subscription.customPlan.storage", "Knowledge capacity (MB)")}
              </label>
            </div>
            <Input
              type="number"
              value={requirements.storage}
              onChange={(e) => handleRequirementChange("storage", parseInt(e.target.value) || 0)}
              className="w-full"
              min="0"
              step="100"
            />
            <p className="text-xs text-muted-foreground">
              Current: {formatNumber(requirements.storage)} MB
            </p>
          </div>
        </div>

        {/* Features */}
        <div className="space-y-3">
          <h4 className="font-medium text-foreground">
            {t("pages.subscription.customPlan.included", "Always Included")}
          </h4>
          <ul className="space-y-2 text-sm text-muted-foreground">
            <li className="flex items-center gap-2">
              <div className="size-1.5 rounded-full bg-primary" />
              OneXStaff Advance ~GPT-4o
            </li>
            <li className="flex items-center gap-2">
              <div className="size-1.5 rounded-full bg-primary" />
              Multilingual support
            </li>
            <li className="flex items-center gap-2">
              <div className="size-1.5 rounded-full bg-primary" />
              Priority support
            </li>
            <li className="flex items-center gap-2">
              <div className="size-1.5 rounded-full bg-primary" />
              Custom integrations
            </li>
          </ul>
        </div>

        {/* Submit Button */}
        <Button
          onClick={handleSubmit}
          loading={isSubmitting}
          className="w-full bg-primary hover:bg-primary/90"
          size="lg"
        >
          {t("pages.subscription.customPlan.submit", "Get Custom Quote")}
        </Button>

        <p className="text-center text-xs text-muted-foreground">
          {t("pages.subscription.customPlan.contact", "We'll contact you within 24 hours with a personalized quote")}
        </p>
      </CardContent>
    </Card>
  );
}
