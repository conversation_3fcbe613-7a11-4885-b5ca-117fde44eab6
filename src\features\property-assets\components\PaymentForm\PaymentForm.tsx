"use client";

import { useCallback, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// Form and validation
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { authProtectedPaths } from "@/constants/paths";

import { useContracts } from "../../hooks/useContracts";
import { useCreatePayment, useUpdatePayment } from "../../hooks/usePayments";
import type { Payment } from "../../types";
// Payment specific imports
import {
  createPaymentSchema,
  updatePaymentSchema,
  type CreatePaymentFormValues,
  type UpdatePaymentFormValues,
} from "../../utils/validators/payment";

interface PaymentFormProps {
  initialData?: Payment;
  isEditing?: boolean;
  preselectedContractId?: string;
}

export function PaymentForm({
  initialData,
  isEditing = false,
  preselectedContractId,
}: PaymentFormProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Data hooks
  const { data: contractsData } = useContracts({ limit: 1000 });
  const contracts = contractsData?.items || [];

  // Mutations
  const createPaymentMutation = useCreatePayment();
  const updatePaymentMutation = useUpdatePayment();

  // Form setup
  const form = useZodForm({
    schema: isEditing ? updatePaymentSchema : createPaymentSchema,
    defaultValues:
      isEditing && initialData
        ? {
            id: initialData.id,
            contract_id: initialData.contract_id,
            amount: initialData.amount,
            payment_date: initialData.payment_date,
            payment_method: initialData.payment_method,
            payment_type: initialData.payment_type,
            description: initialData.description || "",
            reference_number: initialData.reference_number || "",
            status: initialData.status,
          }
        : {
            contract_id: preselectedContractId || "",
            amount: 0,
            payment_date: new Date().toISOString().split("T")[0],
            payment_method: "bank_transfer" as const,
            payment_type: "rent" as const,
            description: "",
            reference_number: "",
          },
  });

  const watchedContractId = form.watch("contract_id");
  const selectedContract = contracts.find((contract) => contract.id === watchedContractId);

  const onSubmit = useCallback(
    async (values: CreatePaymentFormValues | UpdatePaymentFormValues) => {
      if (isSubmitting) return;

      setIsSubmitting(true);
      try {
        if (isEditing && "id" in values) {
          await updatePaymentMutation.mutateAsync({
            id: values.id,
            data: values,
          } as any);
          toast.success(t("payments.messages.updateSuccess"));
          router.push(authProtectedPaths.PAYMENTS_ID.replace(":id", values.id) as any);
        } else {
          const result = await createPaymentMutation.mutateAsync(values as CreatePaymentFormValues);
          toast.success(t("payments.messages.createSuccess"));
          router.push(authProtectedPaths.PAYMENTS_ID.replace(":id", result.id) as any);
        }
      } catch (error) {
        // TODO: Implement proper error logging
        toast.error(
          isEditing ? t("payments.messages.updateError") : t("payments.messages.createError")
        );
      } finally {
        setIsSubmitting(false);
      }
    },
    [isSubmitting, isEditing, updatePaymentMutation, createPaymentMutation, t, router]
  );

  // Quick fill handlers
  const handleQuickFillRent = useCallback(() => {
    if (selectedContract) {
      form.setValue("amount", selectedContract.rent_amount);
      form.setValue("payment_type", "rent");
      form.setValue(
        "description",
        `Monthly rent payment for ${selectedContract.property?.name || "property"} - ${selectedContract.unit?.unit_number || "unit"}`
      );
    }
  }, [selectedContract, form]);

  const handleQuickFillDeposit = useCallback(() => {
    if (selectedContract) {
      form.setValue("amount", selectedContract.deposit_amount);
      form.setValue("payment_type", "deposit");
      form.setValue(
        "description",
        `Security deposit for ${selectedContract.property?.name || "property"} - ${selectedContract.unit?.unit_number || "unit"}`
      );
    }
  }, [selectedContract, form]);

  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <div className="flex-none border-b bg-card">
        <div className="px-6 py-4">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              {isEditing ? t("payments.editPayment") : t("payments.createPayment")}
            </h1>
            <p className="mt-1 text-sm text-muted-foreground">
              {isEditing ? t("payments.editDescription") : t("payments.createDescription")}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 bg-background p-6">
        <Form {...form}>
          <form id="payment-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Contract Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("payments.sections.contract")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="contract_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("payments.fields.contract")}
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className="border-input bg-background text-foreground">
                            <SelectValue placeholder={t("payments.placeholders.contract")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {contracts.map((contract) => (
                            <SelectItem key={contract.id} value={contract.id}>
                              {contract.property?.name} - {contract.unit?.unit_number}
                              {contract.tenant &&
                                ` (${contract.tenant.first_name} ${contract.tenant.last_name})`}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Contract Info Display */}
                {selectedContract && (
                  <div className="space-y-2 rounded-lg bg-muted/50 p-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        {t("payments.contractInfo.rent")}:
                      </span>
                      <span className="font-medium">
                        ${selectedContract.rent_amount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        {t("payments.contractInfo.deposit")}:
                      </span>
                      <span className="font-medium">
                        ${selectedContract.deposit_amount.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">
                        {t("payments.contractInfo.dueDay")}:
                      </span>
                      <span className="font-medium">{selectedContract.rent_due_day}</span>
                    </div>

                    {/* Quick Fill Buttons */}
                    <div className="mt-3 flex gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleQuickFillRent}
                        className="border-input text-foreground hover:bg-accent">
                        {t("payments.quickFill.rent")}
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={handleQuickFillDeposit}
                        className="border-input text-foreground hover:bg-accent">
                        {t("payments.quickFill.deposit")}
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Payment Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("payments.sections.paymentDetails")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("payments.fields.amount")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={t("payments.placeholders.amount")}
                            className="border-input bg-background text-foreground"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="payment_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("payments.fields.paymentDate")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="payment_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("payments.fields.paymentType")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue placeholder={t("payments.placeholders.paymentType")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="rent">{t("payments.paymentTypes.rent")}</SelectItem>
                            <SelectItem value="deposit">
                              {t("payments.paymentTypes.deposit")}
                            </SelectItem>
                            <SelectItem value="late_fee">
                              {t("payments.paymentTypes.lateFee")}
                            </SelectItem>
                            <SelectItem value="maintenance">
                              {t("payments.paymentTypes.maintenance")}
                            </SelectItem>
                            <SelectItem value="other">
                              {t("payments.paymentTypes.other")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="payment_method"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("payments.fields.paymentMethod")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue placeholder={t("payments.placeholders.paymentMethod")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="cash">
                              {t("payments.paymentMethods.cash")}
                            </SelectItem>
                            <SelectItem value="bank_transfer">
                              {t("payments.paymentMethods.bankTransfer")}
                            </SelectItem>
                            <SelectItem value="credit_card">
                              {t("payments.paymentMethods.creditCard")}
                            </SelectItem>
                            <SelectItem value="check">
                              {t("payments.paymentMethods.check")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="reference_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("payments.fields.referenceNumber")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={t("payments.placeholders.referenceNumber")}
                          className="border-input bg-background text-foreground"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {isEditing && (
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("payments.fields.status")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue placeholder={t("payments.placeholders.status")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="pending">{t("payments.status.pending")}</SelectItem>
                            <SelectItem value="completed">
                              {t("payments.status.completed")}
                            </SelectItem>
                            <SelectItem value="failed">{t("payments.status.failed")}</SelectItem>
                            <SelectItem value="refunded">
                              {t("payments.status.refunded")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("payments.fields.description")}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t("payments.placeholders.description")}
                          className="min-h-[100px] border-input bg-background text-foreground"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>

      {/* Sticky Footer - Following OneX ERP Standard - Full Width */}
      <div className="sticky bottom-0 z-50 w-full flex-none border-t border-border bg-card shadow-lg">
        <div className="mx-auto max-w-7xl px-6 py-4">
          <div className="flex justify-end">
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                className="border-border bg-background px-6 text-foreground hover:bg-muted">
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                form="payment-form"
                className="bg-primary px-6 text-primary-foreground hover:bg-primary-hover disabled:opacity-50">
                {isSubmitting && <Loader2 className="mr-2 size-4 animate-spin" />}
                {isEditing ? t("common.update") : t("common.create")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
