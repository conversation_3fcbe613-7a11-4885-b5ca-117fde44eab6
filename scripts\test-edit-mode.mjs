#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

const TEST_URL = 'http://localhost:3000';

async function testEditMode() {
  console.log('🧪 Testing Edit Mode Toggle...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  const page = await browser.newPage();

  try {
    // Navigate to layout management with authentication
    console.log('🗺️ Navigating to Layout Management...');
    const authSuccess = await navigateWithAuth(page, `${TEST_URL}/property-assets/layout`);
    if (!authSuccess) {
      throw new Error('Authentication failed');
    }
    
    // Wait for content to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    console.log('✅ Layout Management page loaded\n');

    // Click Open Mapping button
    console.log('🏢 Clicking Open Mapping button...');
    const openMappingFound = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const openMappingBtn = buttons.find(btn => btn.textContent.includes('Open Mapping'));
      if (openMappingBtn) {
        openMappingBtn.click();
        return true;
      }
      return false;
    });
    
    if (!openMappingFound) {
      throw new Error('Open Mapping button not found');
    }
    
    console.log('✅ Open Mapping button clicked\n');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Take screenshot before edit mode
    await page.screenshot({ path: 'screenshots/before-edit-mode.png', fullPage: true });
    console.log('📸 Screenshot taken before edit mode\n');

    // Try to enable edit mode
    console.log('✏️ Attempting to enable edit mode...');
    
    const editModeResult = await page.evaluate(() => {
      // Look for dropdown menu trigger (grid icon)
      const dropdownTriggers = Array.from(document.querySelectorAll('button'));
      const gridIconButton = dropdownTriggers.find(btn => {
        const svg = btn.querySelector('svg[data-lucide="grid-3-x-3"]');
        return !!svg;
      });
      
      if (gridIconButton) {
        gridIconButton.click();
        return 'dropdown_opened';
      }
      
      // Alternative: look for direct edit button
      const editButtons = Array.from(document.querySelectorAll('button'));
      const editBtn = editButtons.find(btn => btn.textContent.includes('Edit'));
      if (editBtn) {
        editBtn.click();
        return 'direct_edit_clicked';
      }
      
      return 'no_edit_button_found';
    });
    
    console.log(`   - Edit action result: ${editModeResult}`);
    
    if (editModeResult === 'dropdown_opened') {
      // Wait for dropdown to appear, then click edit option
      await new Promise(resolve => setTimeout(resolve, 500));
      
      const editItemClicked = await page.evaluate(() => {
        const editMenuItems = Array.from(document.querySelectorAll('[role="menuitem"]'));
        const editItem = editMenuItems.find(item => item.textContent.includes('Edit'));
        if (editItem) {
          editItem.click();
          return true;
        }
        return false;
      });
      
      console.log(`   - Edit menu item clicked: ${editItemClicked}`);
    }
    
    // Wait a bit to see if there are errors
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check for runtime errors
    const hasErrors = await page.evaluate(() => {
      const errorElements = document.querySelectorAll('[data-nextjs-dialog-overlay]');
      return errorElements.length > 0;
    });
    
    if (hasErrors) {
      console.log('❌ Runtime error detected in edit mode');
      await page.screenshot({ path: 'screenshots/edit-mode-error.png', fullPage: true });
    } else {
      console.log('✅ Edit mode enabled successfully - no runtime errors detected');
      await page.screenshot({ path: 'screenshots/edit-mode-success.png', fullPage: true });
    }
    
    console.log('📸 Final screenshot saved\n');

  } catch (error) {
    console.error('❌ Error during edit mode test:', error.message);
    await page.screenshot({ path: 'screenshots/edit-mode-test-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

// Run the test
testEditMode().catch(console.error);