import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
import type { ApiResponse, CreateTenant, PaginatedResponse, Tenant } from "../types";

// API functions (connecting to Vietnamese tenants API endpoints)
const tenantsApi = {
  // Get all tenants
  getTenants: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    tenant_type?: string;
    status?: "active" | "inactive";
  }): Promise<PaginatedResponse<Tenant>> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.limit) searchParams.append("limit", params.limit.toString());
    if (params?.search) searchParams.append("search", params.search);
    if (params?.tenant_type) searchParams.append("tenant_type", params.tenant_type);
    if (params?.status) searchParams.append("status", params.status);

    const response = await fetch(`/api/property-assets/tenants?${searchParams.toString()}`);
    if (!response.ok) {
      throw new Error("Failed to fetch tenants");
    }

    return response.json();
  },

  // Get tenant by ID
  getTenant: async (id: string): Promise<Tenant> => {
    const response = await fetch(`/api/property-assets/tenants/${id}`);
    if (!response.ok) {
      throw new Error("Failed to fetch tenant");
    }

    const result = await response.json();
    return result.data;
  },

  // Create tenant
  createTenant: async (data: CreateTenant): Promise<Tenant> => {
    const response = await fetch("/api/property-assets/tenants", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to create tenant");
    }

    const result = await response.json();
    return result.data;
  },

  // Update tenant
  updateTenant: async (id: string, data: Partial<CreateTenant>): Promise<Tenant> => {
    const response = await fetch(`/api/property-assets/tenants/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update tenant");
    }

    const result = await response.json();
    return result.data;
  },

  // Delete tenant
  deleteTenant: async (id: string): Promise<void> => {
    const response = await fetch(`/api/property-assets/tenants/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error("Failed to delete tenant");
    }
  },
};

// Query keys
export const tenantsKeys = {
  all: ["tenants"] as const,
  lists: () => [...tenantsKeys.all, "list"] as const,
  list: (params?: any) => [...tenantsKeys.lists(), params] as const,
  details: () => [...tenantsKeys.all, "detail"] as const,
  detail: (id: string) => [...tenantsKeys.details(), id] as const,
};

// Hooks
export const useTenants = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  tenant_type?: string;
  status?: "active" | "inactive";
}) => {
  return useQuery({
    queryKey: tenantsKeys.list(params),
    queryFn: () => tenantsApi.getTenants(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useTenant = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: tenantsKeys.detail(id),
    queryFn: () => tenantsApi.getTenant(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateTenant = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: tenantsApi.createTenant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: tenantsKeys.lists() });
      toast.success("Tạo người thuê thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi tạo người thuê: ${error.message}`);
    },
  });
};

export const useUpdateTenant = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateTenant> }) =>
      tenantsApi.updateTenant(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: tenantsKeys.lists() });
      queryClient.setQueryData(tenantsKeys.detail(data.id), data);
      toast.success("Cập nhật người thuê thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi cập nhật người thuê: ${error.message}`);
    },
  });
};

export const useDeleteTenant = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: tenantsApi.deleteTenant,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: tenantsKeys.lists() });
      toast.success("Xóa người thuê thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi xóa người thuê: ${error.message}`);
    },
  });
};
