import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { dashboardApi, financialApi, paymentApi } from "@/lib/apis/property-assets";

import type { CreatePayment, FinancialSummary, Payment, ProfitLossReport } from "../types";
import { contractKeys, financialKeys } from "./keys";

// Financial Summary Hook
export const useFinancialSummary = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: financialKeys.dashboard(),
    queryFn: () => financialApi.getSummary(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Profit & Loss Report Hook
export const useProfitLossReport = (params?: {
  start_date?: string;
  end_date?: string;
  property_id?: string;
  time_range?: string;
}) => {
  return useQuery({
    queryKey: financialKeys.profitLoss(params || {}),
    queryFn: () => financialApi.getProfitLossReport(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Revenue Analytics Hook
export const useRevenueAnalytics = (params?: {
  start_date?: string;
  end_date?: string;
  property_id?: string;
  time_range?: string;
}) => {
  return useQuery({
    queryKey: financialKeys.revenue(params || {}),
    queryFn: () => financialApi.getRevenueAnalytics(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Transactions List Hook
export const useTransactions = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: financialKeys.transactions(),
    queryFn: () => financialApi.getTransactions(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Dashboard Stats Hook
export const useDashboardStats = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: financialKeys.dashboard(),
    queryFn: () => dashboardApi.getStats(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Payment Hooks
export const usePayments = (params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: ["payments", params],
    queryFn: () => paymentApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const usePayment = (id: string) => {
  return useQuery({
    queryKey: ["payments", id],
    queryFn: () => paymentApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useContractPayments = (contractId: string, params?: Record<string, unknown>) => {
  return useQuery({
    queryKey: contractKeys.payments(contractId),
    queryFn: () => paymentApi.getByContract(contractId, params),
    enabled: !!contractId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create Payment Hook
export const useCreatePayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreatePayment) => paymentApi.create(data),
    onSuccess: (data) => {
      // Invalidate and refetch payments list
      queryClient.invalidateQueries({ queryKey: ["payments"] });

      // Invalidate contract payments if contract_id is available
      if (data.data.data.contract_id) {
        queryClient.invalidateQueries({
          queryKey: contractKeys.payments(data.data.data.contract_id),
        });
      }

      // Invalidate financial data
      queryClient.invalidateQueries({ queryKey: financialKeys.all() });

      // Add the new payment to the cache
      queryClient.setQueryData(["payments", data.data.data.id], data);

      toast.success("Payment recorded successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to record payment");
    },
  });
};

// Update Payment Hook
export const useUpdatePayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreatePayment> }) =>
      paymentApi.update(id, data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch payments list
      queryClient.invalidateQueries({ queryKey: ["payments"] });

      // Update the specific payment in cache
      queryClient.setQueryData(["payments", variables.id], data);

      // Invalidate contract payments if contract_id is available
      if (data.data.data.contract_id) {
        queryClient.invalidateQueries({
          queryKey: contractKeys.payments(data.data.data.contract_id),
        });
      }

      // Invalidate financial data
      queryClient.invalidateQueries({ queryKey: financialKeys.all() });

      toast.success("Payment updated successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to update payment");
    },
  });
};

// Delete Payment Hook
export const useDeletePayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => paymentApi.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: ["payments", id] });

      // Invalidate all related lists
      queryClient.invalidateQueries({ queryKey: ["payments"] });
      queryClient.invalidateQueries({ queryKey: contractKeys.all() });
      queryClient.invalidateQueries({ queryKey: financialKeys.all() });

      toast.success("Payment deleted successfully");
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to delete payment");
    },
  });
};
