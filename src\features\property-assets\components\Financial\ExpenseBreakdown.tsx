"use client";

import { useState } from "react";
import {
  <PERSON>ert<PERSON><PERSON>gle,
  BarChart3,
  DollarSign,
  Download,
  FileText,
  Filter,
  Home,
  <PERSON><PERSON>hart,
  Shield,
  TrendingDown,
  TrendingUp,
  <PERSON>ch,
  Zap,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  <PERSON>hart as RechartsPieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";

interface ExpenseBreakdownProps {
  propertyId?: string;
  className?: string;
}

export function ExpenseBreakdown({ propertyId, className }: ExpenseBreakdownProps) {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState<"month" | "quarter" | "year">("month");
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");
  const [viewMode, setViewMode] = useState<"chart" | "table">("chart");

  // Mock expense data - replace with real API data
  const expenseCategories = [
    {
      name: "Maintenance & Repairs",
      value: 35,
      amount: 15400,
      budget: 14000,
      color: "#8884d8",
      icon: Wrench,
      trend: "up",
      trendValue: 10.2,
      description: "HVAC, plumbing, general repairs",
    },
    {
      name: "Utilities",
      value: 25,
      amount: 11000,
      budget: 11500,
      color: "#82ca9d",
      icon: Zap,
      trend: "down",
      trendValue: -4.5,
      description: "Electricity, gas, water, internet",
    },
    {
      name: "Insurance",
      value: 20,
      amount: 8800,
      budget: 9000,
      color: "#ffc658",
      icon: Shield,
      trend: "up",
      trendValue: 2.1,
      description: "Property, liability insurance",
    },
    {
      name: "Property Tax",
      value: 15,
      amount: 6600,
      budget: 6600,
      color: "#ff7300",
      icon: FileText,
      trend: "flat",
      trendValue: 0,
      description: "Municipal and county taxes",
    },
    {
      name: "Management Fees",
      value: 3,
      amount: 1320,
      budget: 1400,
      color: "#8dd1e1",
      icon: Home,
      trend: "down",
      trendValue: -5.7,
      description: "Property management services",
    },
    {
      name: "Other",
      value: 2,
      amount: 880,
      budget: 1000,
      color: "#d084d0",
      icon: DollarSign,
      trend: "down",
      trendValue: -12,
      description: "Miscellaneous expenses",
    },
  ];

  const monthlyExpenseData = [
    { month: "Jan", maintenance: 14200, utilities: 10500, insurance: 8800, tax: 6600, other: 2200 },
    { month: "Feb", maintenance: 13800, utilities: 11200, insurance: 8800, tax: 6600, other: 1900 },
    { month: "Mar", maintenance: 16500, utilities: 10800, insurance: 8800, tax: 6600, other: 2100 },
    { month: "Apr", maintenance: 12200, utilities: 11500, insurance: 8800, tax: 6600, other: 1800 },
    { month: "May", maintenance: 15800, utilities: 10900, insurance: 8800, tax: 6600, other: 2000 },
    { month: "Jun", maintenance: 15400, utilities: 11000, insurance: 8800, tax: 6600, other: 1880 },
  ];

  const expenseByProperty = [
    { property: "Sunset Apartments", amount: 18500, percentage: 42, units: 24 },
    { property: "Downtown Lofts", amount: 12800, percentage: 29, units: 18 },
    { property: "Garden View Complex", amount: 8200, percentage: 19, units: 12 },
    { property: "Riverside Towers", amount: 4500, percentage: 10, units: 8 },
  ];

  const totalExpenses = expenseCategories.reduce((sum, cat) => sum + cat.amount, 0);
  const totalBudget = expenseCategories.reduce((sum, cat) => sum + cat.budget, 0);
  const budgetVariance = ((totalExpenses - totalBudget) / totalBudget) * 100;

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up":
        return <TrendingUp className="size-3 text-destructive" />;
      case "down":
        return <TrendingDown className="size-3 text-success" />;
      default:
        return <div className="size-3 rounded-full bg-muted" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up":
        return "text-destructive";
      case "down":
        return "text-success";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Expense Analysis Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">
            {t("financial.expenseAnalysis.title")}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t("financial.expenseAnalysis.description")}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">{t("financial.timeRange.month")}</SelectItem>
              <SelectItem value="quarter">{t("financial.timeRange.quarter")}</SelectItem>
              <SelectItem value="year">{t("financial.timeRange.year")}</SelectItem>
            </SelectContent>
          </Select>
          <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
            <SelectTrigger className="w-24">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="chart">Chart</SelectItem>
              <SelectItem value="table">Table</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Download className="mr-2 size-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Expense Summary Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Expenses</p>
                <p className="text-2xl font-bold">${totalExpenses.toLocaleString()}</p>
                <div className="mt-1 flex items-center space-x-1">
                  {budgetVariance > 0 ? (
                    <TrendingUp className="size-3 text-destructive" />
                  ) : (
                    <TrendingDown className="size-3 text-success" />
                  )}
                  <span
                    className={`text-xs ${budgetVariance > 0 ? "text-destructive" : "text-success"}`}>
                    {budgetVariance > 0 ? "+" : ""}
                    {budgetVariance.toFixed(1)}% vs budget
                  </span>
                </div>
              </div>
              <DollarSign className="size-8 text-destructive" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Budget Remaining</p>
                <p className="text-2xl font-bold">
                  ${(totalBudget - totalExpenses).toLocaleString()}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <Progress
                    value={((totalBudget - totalExpenses) / totalBudget) * 100}
                    className="h-2 w-12"
                  />
                  <span className="text-xs text-muted-foreground">
                    {(((totalBudget - totalExpenses) / totalBudget) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
              <PieChart className="size-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Expense per Unit</p>
                <p className="text-2xl font-bold">$710</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-warning" />
                  <span className="text-xs text-warning">+2.8%</span>
                </div>
              </div>
              <Home className="size-8 text-warning" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Highest Category</p>
                <p className="text-2xl font-bold">Maintenance</p>
                <div className="mt-1 flex items-center space-x-1">
                  <AlertTriangle className="size-3 text-destructive" />
                  <span className="text-xs text-destructive">35% of total</span>
                </div>
              </div>
              <Wrench className="size-8 text-primary" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Expense Breakdown Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChart className="size-5" />
              <span>Expense Distribution</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {viewMode === "chart" ? (
              <ResponsiveContainer width="100%" height={300}>
                <RechartsPieChart>
                  <Pie
                    data={expenseCategories}
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    dataKey="value"
                    label={({ name, value }) => `${name}: ${value}%`}>
                    {expenseCategories.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value, name) => [
                      `$${expenseCategories.find((d) => d.name === name)?.amount.toLocaleString()}`,
                      name,
                    ]}
                  />
                </RechartsPieChart>
              </ResponsiveContainer>
            ) : (
              <div className="space-y-3">
                {expenseCategories.map((category, index) => (
                  <div key={index} className="flex items-center justify-between rounded border p-2">
                    <div className="flex items-center space-x-2">
                      <category.icon className="size-4" style={{ color: category.color }} />
                      <span className="text-sm font-medium">{category.name}</span>
                    </div>
                    <span className="text-sm font-semibold">
                      ${category.amount.toLocaleString()}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Category Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="size-5" />
              <span>Category Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {expenseCategories.map((category, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <category.icon className="size-4" style={{ color: category.color }} />
                      <span className="text-sm font-medium">{category.name}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-semibold">
                        ${category.amount.toLocaleString()}
                      </span>
                      <div className="flex items-center space-x-1">
                        {getTrendIcon(category.trend)}
                        <span className={`text-xs ${getTrendColor(category.trend)}`}>
                          {category.trend !== "flat" && (category.trendValue > 0 ? "+" : "")}
                          {category.trendValue}%
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-muted-foreground">{category.description}</span>
                    <span
                      className={`${category.amount > category.budget ? "text-destructive" : "text-success"}`}>
                      Budget: ${category.budget.toLocaleString()}
                    </span>
                  </div>
                  <Progress value={(category.amount / category.budget) * 100} className="h-2" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Expense Trends */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="trends">Monthly Trends</TabsTrigger>
          <TabsTrigger value="properties">By Property</TabsTrigger>
          <TabsTrigger value="analysis">Cost Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Monthly Expense Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={monthlyExpenseData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, ""]} />
                  <Legend />
                  <Area
                    type="monotone"
                    dataKey="maintenance"
                    stackId="1"
                    stroke="#8884d8"
                    fill="#8884d8"
                    name="Maintenance"
                  />
                  <Area
                    type="monotone"
                    dataKey="utilities"
                    stackId="1"
                    stroke="#82ca9d"
                    fill="#82ca9d"
                    name="Utilities"
                  />
                  <Area
                    type="monotone"
                    dataKey="insurance"
                    stackId="1"
                    stroke="#ffc658"
                    fill="#ffc658"
                    name="Insurance"
                  />
                  <Area
                    type="monotone"
                    dataKey="tax"
                    stackId="1"
                    stroke="#ff7300"
                    fill="#ff7300"
                    name="Tax"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="properties" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Expenses by Property</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {expenseByProperty.map((property, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                      <h4 className="font-medium">{property.property}</h4>
                      <p className="text-sm text-muted-foreground">{property.units} units</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">${property.amount.toLocaleString()}</p>
                      <p className="text-sm text-muted-foreground">
                        {property.percentage}% of total
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Cost Optimization Opportunities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="rounded-lg border border-destructive/20 bg-destructive/10 p-3">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="size-4 text-destructive" />
                      <span className="font-medium text-destructive">High Priority</span>
                    </div>
                    <p className="mt-1 text-sm text-destructive/90">
                      Maintenance costs are 10.2% above budget. Review vendor contracts.
                    </p>
                  </div>
                  <div className="rounded-lg border border-warning/20 bg-warning/10 p-3">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="size-4 text-warning" />
                      <span className="font-medium text-warning">Medium Priority</span>
                    </div>
                    <p className="mt-1 text-sm text-warning/90">
                      Insurance premiums increased by 2.1%. Shop for better rates.
                    </p>
                  </div>
                  <div className="rounded-lg border border-success/20 bg-success/10 p-3">
                    <div className="flex items-center space-x-2">
                      <TrendingDown className="size-4 text-success" />
                      <span className="font-medium text-success">Good Progress</span>
                    </div>
                    <p className="mt-1 text-sm text-success/90">
                      Utility costs reduced by 4.5% through energy efficiency improvements.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Expense Forecasting</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="rounded-lg bg-primary/10 p-3 text-center">
                    <h4 className="font-semibold text-primary">Next Month</h4>
                    <p className="text-xl font-bold text-primary">$45,200</p>
                    <p className="text-sm text-primary/90">+2.8% projected</p>
                  </div>
                  <div className="rounded-lg bg-secondary/10 p-3 text-center">
                    <h4 className="font-semibold text-secondary-foreground">Next Quarter</h4>
                    <p className="text-xl font-bold text-secondary-foreground">$138,500</p>
                    <p className="text-sm text-secondary-foreground/90">+4.1% projected</p>
                  </div>
                  <div className="rounded-lg bg-warning/10 p-3 text-center">
                    <h4 className="font-semibold text-warning">Annual Budget</h4>
                    <p className="text-xl font-bold text-warning">$535,000</p>
                    <p className="text-sm text-warning/90">82% utilized</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
