import { z } from "zod";

export const createUnitSchema = z.object({
  property_id: z.string().min(1, "validation.propertyRequired"),
  unit_number: z.string().min(1, "validation.unitNumberRequired"),
  unit_type: z.enum(["studio", "1br", "2br", "3br", "commercial", "office", "retail"], {
    errorMap: () => ({ message: "validation.unitTypeRequired" }),
  }),
  floor: z.number().int("validation.floorMustBeInteger"),
  square_footage: z.number().positive("validation.squareFootageMustBePositive"),
  bedrooms: z.number().int("validation.bedroomsMustBeInteger").min(0).optional(),
  bathrooms: z.number().min(0, "validation.bathroomsMustBePositive").optional(),
  description: z.string().optional(),
  rent_amount: z.number().positive("validation.rentAmountMustBePositive"),
  deposit_amount: z.number().min(0, "validation.depositAmountMustBePositive"),
  amenities: z.array(z.string()).optional(),
  images: z
    .array(
      z.object({
        name: z.string(),
        image: z.string(),
      })
    )
    .optional(),
});

export const updateUnitSchema = createUnitSchema.partial().extend({
  id: z.string().min(1, "validation.idRequired"),
});

export const unitFilterSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  query: z.string().optional(),
  property_id: z.string().optional(),
  unit_type: z.enum(["studio", "1br", "2br", "3br", "commercial", "office", "retail"]).optional(),
  status: z.enum(["available", "occupied", "maintenance", "inactive"]).optional(),
});

export type CreateUnitFormValues = z.infer<typeof createUnitSchema>;
export type UpdateUnitFormValues = z.infer<typeof updateUnitSchema>;
export type UnitFilterValues = z.infer<typeof unitFilterSchema>;
