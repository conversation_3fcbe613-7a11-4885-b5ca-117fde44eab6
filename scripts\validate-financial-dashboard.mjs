import puppeteer from 'puppeteer';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs';

// Get current file path
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import auth utility
const authUtilPath = join(__dirname, 'utils', 'auth.mjs');
const { navigateWithAuth } = await import(authUtilPath);

async function validateFinancialDashboard() {
  let browser;
  
  try {
    console.log('🚀 Starting Financial Dashboard UI validation...');
    
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: null,
      args: ['--start-maximized', '--no-sandbox']
    });

    const page = await browser.newPage();
    
    // Navigate to Financial Dashboard with authentication
    console.log('📊 Navigating to Financial Dashboard with authentication...');
    const authSuccess = await navigateWithAuth(page, 'http://localhost:3000/property-assets/financial');
    
    if (!authSuccess) {
      throw new Error('Failed to authenticate and navigate to Financial Dashboard');
    }
    
    // Wait for the dashboard to load
    await page.waitForSelector('h1', { timeout: 10000 });
    
    // Take screenshot of main dashboard
    console.log('📸 Taking screenshot of Financial Dashboard...');
    await page.screenshot({ 
      path: 'screenshots/financial-dashboard-main.png', 
      fullPage: true 
    });
    
    // Validate Financial Dashboard elements
    console.log('✅ Validating Financial Dashboard elements...');
    
    const validationResults = [];
    
    // Check main title
    try {
      const title = await page.waitForSelector('h1', { timeout: 5000 });
      const titleText = await page.evaluate(el => el.textContent, title);
      validationResults.push({
        test: 'Main title exists',
        passed: titleText?.includes('Financial') || titleText?.includes('Dashboard'),
        details: `Title text: "${titleText}"`
      });
    } catch (error) {
      validationResults.push({
        test: 'Main title exists', 
        passed: false,
        details: `Error: ${error.message}`
      });
    }
    
    // Check for financial metric cards
    try {
      const metricCards = await page.$$('[data-testid="metric-card"], .card, [class*="card"]');
      validationResults.push({
        test: 'Financial metric cards exist',
        passed: metricCards.length >= 3,
        details: `Found ${metricCards.length} metric cards`
      });
    } catch (error) {
      validationResults.push({
        test: 'Financial metric cards exist',
        passed: false,
        details: `Error: ${error.message}`
      });
    }
    
    // Check for charts/visualizations
    try {
      const charts = await page.$$('svg, canvas, .recharts-wrapper, [class*="chart"]');
      validationResults.push({
        test: 'Financial charts exist',
        passed: charts.length >= 1,
        details: `Found ${charts.length} chart elements`
      });
    } catch (error) {
      validationResults.push({
        test: 'Financial charts exist',
        passed: false,
        details: `Error: ${error.message}`
      });
    }
    
    // Check for time range selector
    try {
      const selectors = await page.$$('select, [role="combobox"], .select-trigger');
      validationResults.push({
        test: 'Time range selector exists',
        passed: selectors.length >= 1,
        details: `Found ${selectors.length} selector elements`
      });
    } catch (error) {
      validationResults.push({
        test: 'Time range selector exists',
        passed: false,
        details: `Error: ${error.message}`
      });
    }
    
    // Check for export functionality
    try {
      const exportButtons = await page.$$('button');
      let exportButton = null;
      
      for (const button of exportButtons) {
        const buttonText = await page.evaluate(el => el.textContent, button);
        if (buttonText && (buttonText.includes('Export') || buttonText.includes('Download'))) {
          exportButton = button;
          break;
        }
      }
      
      validationResults.push({
        test: 'Export functionality exists',
        passed: !!exportButton,
        details: exportButton ? 'Export button found' : 'No export button found'
      });
    } catch (error) {
      validationResults.push({
        test: 'Export functionality exists',
        passed: false,
        details: `Error: ${error.message}`
      });
    }
    
    // Test individual component navigation if tabs exist
    try {
      const tabs = await page.$$('[role="tab"], .tab, [class*="tab"]');
      if (tabs.length > 0) {
        console.log('🔄 Testing tab navigation...');
        
        for (let i = 0; i < Math.min(tabs.length, 3); i++) {
          try {
            await tabs[i].click();
            await new Promise(resolve => setTimeout(resolve, 1000));
            await page.screenshot({ 
              path: `screenshots/financial-dashboard-tab-${i + 1}.png`, 
              fullPage: true 
            });
            
            validationResults.push({
              test: `Tab ${i + 1} navigation`,
              passed: true,
              details: 'Tab clicked and screenshot taken'
            });
          } catch (error) {
            validationResults.push({
              test: `Tab ${i + 1} navigation`,
              passed: false,
              details: `Error: ${error.message}`
            });
          }
        }
      }
    } catch (error) {
      validationResults.push({
        test: 'Tab navigation test',
        passed: false,
        details: `Error: ${error.message}`
      });
    }
    
    // Test responsiveness
    console.log('📱 Testing responsive design...');
    
    // Mobile view
    await page.setViewport({ width: 375, height: 667 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: 'screenshots/financial-dashboard-mobile.png', 
      fullPage: true 
    });
    
    // Tablet view
    await page.setViewport({ width: 768, height: 1024 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: 'screenshots/financial-dashboard-tablet.png', 
      fullPage: true 
    });
    
    // Desktop view
    await page.setViewport({ width: 1920, height: 1080 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: 'screenshots/financial-dashboard-desktop.png', 
      fullPage: true 
    });
    
    validationResults.push({
      test: 'Responsive design test',
      passed: true,
      details: 'Screenshots taken for mobile, tablet, and desktop views'
    });
    
    // Generate validation report
    const report = {
      timestamp: new Date().toISOString(),
      testSuite: 'Financial Dashboard UI Validation',
      totalTests: validationResults.length,
      passedTests: validationResults.filter(r => r.passed).length,
      failedTests: validationResults.filter(r => !r.passed).length,
      results: validationResults
    };
    
    const reportPath = 'test-reports/financial-dashboard-validation.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📋 Financial Dashboard Validation Report:');
    console.log(`✅ Passed: ${report.passedTests}/${report.totalTests} tests`);
    console.log(`❌ Failed: ${report.failedTests}/${report.totalTests} tests`);
    
    if (report.failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      validationResults.filter(r => !r.passed).forEach(result => {
        console.log(`  - ${result.test}: ${result.details}`);
      });
    }
    
    console.log(`\n📁 Report saved to: ${reportPath}`);
    console.log('📁 Screenshots saved to: screenshots/');
    
    return report;
    
  } catch (error) {
    console.error('❌ Error during validation:', error);
    throw error;
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run validation
validateFinancialDashboard()
  .then(report => {
    console.log('\n🎉 Financial Dashboard validation completed!');
    process.exit(report.failedTests > 0 ? 1 : 0);
  })
  .catch(error => {
    console.error('💥 Validation failed:', error);
    process.exit(1);
  });