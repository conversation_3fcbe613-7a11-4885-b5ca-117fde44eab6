"use client";

import { use<PERSON>allback, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { AlertCircle, CheckCircle, FileImage, Info, Loader2, Upload, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { z } from "zod";

import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Textarea } from "@/components/ui/textarea";

// Hooks
import { useUploadLayoutImage } from "../../hooks";
// Types
import type { Property } from "../../types";

const createLayoutSchema = z.object({
  name: z.string().min(1, "Layout name is required").max(100, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  floorNumber: z.number().min(0).max(100).optional(),
  imageFile: z.instanceof(File).optional(),
});

type CreateLayoutFormData = z.infer<typeof createLayoutSchema>;

interface CreateLayoutFormProps {
  property: Property;
  onSubmit: (
    data: CreateLayoutFormData & { imageUrl?: string; imageWidth: number; imageHeight: number }
  ) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
  className?: string;
}

export function CreateLayoutForm({
  property,
  onSubmit,
  onCancel,
  isLoading = false,
  className = "",
}: CreateLayoutFormProps) {
  const { t } = useTranslation();

  // Hooks
  const uploadImageMutation = useUploadLayoutImage();

  // State
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageDimensions, setImageDimensions] = useState<{ width: number; height: number } | null>(
    null
  );
  const [uploadProgress, setUploadProgress] = useState(0);
  const [dragActive, setDragActive] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  // Form
  const form = useForm<CreateLayoutFormData>({
    resolver: zodResolver(createLayoutSchema),
    defaultValues: {
      name: "",
      description: "",
      floorNumber: 1,
    },
  });

  // Handle image upload
  const handleImageUpload = useCallback(
    async (file: File) => {
      setUploadError(null);

      // Validate file type
      if (!file.type.startsWith("image/")) {
        setUploadError(t("pages.layouts.invalidFileType"));
        return;
      }

      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        setUploadError(t("pages.layouts.fileTooLarge"));
        return;
      }

      // Update form
      form.setValue("imageFile", file);

      // Start upload progress simulation
      setUploadProgress(0);
      const progressInterval = setInterval(() => {
        setUploadProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90; // Keep at 90% until upload completes
          }
          return prev + 10;
        });
      }, 100);

      try {
        // Upload image using the hook
        const result = await uploadImageMutation.mutateAsync(file);

        setImagePreview(result.url);
        setImageDimensions({ width: result.width, height: result.height });
        setUploadProgress(100);
      } catch (error) {
        clearInterval(progressInterval);
        setUploadProgress(0);
        const errorMessage =
          error instanceof Error ? error.message : t("pages.layouts.uploadFailed");
        setUploadError(errorMessage);
      }
    },
    [form, t, uploadImageMutation]
  );

  // Handle drag and drop
  const handleDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault();
      setDragActive(false);

      const files = Array.from(e.dataTransfer.files);
      if (files.length > 0) {
        handleImageUpload(files[0]);
      }
    },
    [handleImageUpload]
  );

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  // Remove image
  const handleRemoveImage = useCallback(() => {
    setImagePreview(null);
    setImageDimensions(null);
    setUploadProgress(0);
    setUploadError(null);
    form.setValue("imageFile", undefined);
  }, [form]);

  // Form submission
  const handleSubmit = useCallback(
    async (data: CreateLayoutFormData) => {
      try {
        await onSubmit({
          ...data,
          imageUrl: imagePreview || undefined,
          imageWidth: imageDimensions?.width || 800,
          imageHeight: imageDimensions?.height || 600,
        });
      } catch (error) {
        // TODO: Implement proper error logging
      }
    },
    [onSubmit, imagePreview, imageDimensions]
  );

  return (
    <div className={`flex min-h-screen flex-col ${className}`}>
      {/* Header */}
      <div className="p-6">
        <div className="flex items-center gap-2">
          <FileImage className="size-5" />
          <h1 className="text-2xl font-semibold text-foreground">
            {t("pages.layouts.createLayout")}
          </h1>
        </div>
        <p className="mt-1 text-sm text-muted-foreground">
          {t("pages.layouts.createLayoutDescription")} - {property.name}
        </p>
      </div>

      {/* Form Content */}
      <div className="flex-1 px-6 pb-6">
        <Card>
          <CardContent className="p-6">
            <Form {...form}>
              <form
                id="layout-form"
                onSubmit={form.handleSubmit(handleSubmit)}
                className="space-y-6">
                {/* Basic Information */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("common.name")} *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("pages.layouts.layoutNamePlaceholder")}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="floorNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>{t("pages.layouts.floorNumber")}</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            placeholder="1"
                            {...field}
                            onChange={(e) =>
                              field.onChange(e.target.value ? Number(e.target.value) : undefined)
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          {t("pages.layouts.floorNumberDescription")}
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>{t("common.description")}</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t("pages.layouts.layoutDescriptionPlaceholder")}
                          className="resize-none"
                          rows={3}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Image Upload */}
                <div className="space-y-4">
                  <Label className="text-sm font-medium">{t("pages.layouts.uploadImage")}</Label>

                  {!imagePreview ? (
                    <div
                      className={`rounded-lg border-2 border-dashed p-8 text-center transition-colors ${
                        dragActive
                          ? "border-primary bg-primary/5"
                          : "border-muted-foreground/25 hover:border-muted-foreground/50"
                      }`}
                      onDrop={handleDrop}
                      onDragOver={handleDragOver}
                      onDragLeave={handleDragLeave}>
                      <Upload className="mx-auto mb-4 size-12 text-muted-foreground" />
                      <div className="space-y-2">
                        <p className="text-sm font-medium">{t("pages.layouts.dropImageHere")}</p>
                        <p className="text-xs text-muted-foreground">
                          {t("pages.layouts.supportedFormats")}
                        </p>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const input = document.createElement("input");
                            input.type = "file";
                            input.accept = "image/*";
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0];
                              if (file) handleImageUpload(file);
                            };
                            input.click();
                          }}>
                          {t("pages.layouts.chooseFile")}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Card>
                      <CardContent className="p-4">
                        <div className="flex items-start gap-4">
                          <div className="relative">
                            <img
                              src={imagePreview}
                              alt="Layout preview"
                              className="h-24 w-32 rounded-lg border object-cover"
                            />
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="absolute -right-2 -top-2 size-6 rounded-full p-0"
                              onClick={handleRemoveImage}>
                              <X className="size-3" />
                            </Button>
                          </div>

                          <div className="flex-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <CheckCircle className="size-4 text-success" />
                              <span className="text-sm font-medium">
                                {t("pages.layouts.imageUploaded")}
                              </span>
                            </div>

                            {imageDimensions && (
                              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                                <Badge variant="outline">
                                  {imageDimensions.width} × {imageDimensions.height}px
                                </Badge>
                                <span>{form.watch("imageFile")?.name}</span>
                              </div>
                            )}

                            {uploadProgress < 100 && (
                              <div className="space-y-1">
                                <Progress value={uploadProgress} className="h-2" />
                                <p className="text-xs text-muted-foreground">
                                  {t("pages.layouts.uploading")} {uploadProgress}%
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}

                  {uploadError && (
                    <Alert variant="destructive">
                      <AlertCircle className="size-4" />
                      <AlertDescription>{uploadError}</AlertDescription>
                    </Alert>
                  )}

                  <Alert>
                    <Info className="size-4" />
                    <AlertDescription>{t("pages.layouts.uploadImageDescription")}</AlertDescription>
                  </Alert>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>

      {/* Sticky Footer - Following OneX ERP Standard - Full Width */}
      <div className="sticky bottom-0 z-50 w-full flex-none border-t bg-card shadow-lg">
        <div className="mx-auto max-w-7xl px-6 py-4">
          <div className="flex justify-end">
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading}
                className="px-6">
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                form="layout-form"
                disabled={isLoading || !form.watch("name") || uploadProgress < 100}
                className="px-6">
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 size-4 animate-spin" />
                    {t("common.creating")}
                  </>
                ) : (
                  t("common.create")
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
