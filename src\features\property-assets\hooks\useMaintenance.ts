import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
import type {
  ApiResponse,
  CreateMaintenanceRequest,
  MaintenanceRequest,
  PaginatedResponse,
} from "../types";

// API functions (connecting to Vietnamese maintenance API endpoints)
const maintenanceApi = {
  // Get all maintenance requests
  getMaintenanceRequests: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: MaintenanceRequest["status"];
    priority?: MaintenanceRequest["priority"];
    property_id?: string;
    unit_id?: string;
  }): Promise<PaginatedResponse<MaintenanceRequest>> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.limit) searchParams.append("limit", params.limit.toString());
    if (params?.search) searchParams.append("search", params.search);
    if (params?.status) searchParams.append("status", params.status);
    if (params?.priority) searchParams.append("priority", params.priority);
    if (params?.property_id) searchParams.append("property_id", params.property_id);
    if (params?.unit_id) searchParams.append("unit_id", params.unit_id);

    const response = await fetch(`/api/property-assets/maintenance?${searchParams.toString()}`);
    if (!response.ok) {
      throw new Error("Failed to fetch maintenance requests");
    }

    return response.json();
  },

  // Get maintenance request by ID
  getMaintenanceRequest: async (id: string): Promise<MaintenanceRequest> => {
    const response = await fetch(`/api/property-assets/maintenance/${id}`);
    if (!response.ok) {
      throw new Error("Failed to fetch maintenance request");
    }

    const result = await response.json();
    return result.data;
  },

  // Create maintenance request
  createMaintenanceRequest: async (data: CreateMaintenanceRequest): Promise<MaintenanceRequest> => {
    const response = await fetch("/api/property-assets/maintenance", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to create maintenance request");
    }

    const result = await response.json();
    return result.data;
  },

  // Update maintenance request
  updateMaintenanceRequest: async (
    id: string,
    data: Partial<CreateMaintenanceRequest>
  ): Promise<MaintenanceRequest> => {
    const response = await fetch(`/api/property-assets/maintenance/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update maintenance request");
    }

    const result = await response.json();
    return result.data;
  },

  // Delete maintenance request
  deleteMaintenanceRequest: async (id: string): Promise<void> => {
    const response = await fetch(`/api/property-assets/maintenance/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error("Failed to delete maintenance request");
    }
  },
};

// Query keys
export const maintenanceKeys = {
  all: ["maintenance"] as const,
  lists: () => [...maintenanceKeys.all, "list"] as const,
  list: (params?: any) => [...maintenanceKeys.lists(), params] as const,
  details: () => [...maintenanceKeys.all, "detail"] as const,
  detail: (id: string) => [...maintenanceKeys.details(), id] as const,
};

// Hooks
export const useMaintenanceRequests = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: MaintenanceRequest["status"];
  priority?: MaintenanceRequest["priority"];
  property_id?: string;
  unit_id?: string;
}) => {
  return useQuery({
    queryKey: maintenanceKeys.list(params),
    queryFn: () => maintenanceApi.getMaintenanceRequests(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useMaintenanceRequest = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: maintenanceKeys.detail(id),
    queryFn: () => maintenanceApi.getMaintenanceRequest(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateMaintenanceRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: maintenanceApi.createMaintenanceRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() });
      toast.success("Tạo yêu cầu bảo trì thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi tạo yêu cầu bảo trì: ${error.message}`);
    },
  });
};

export const useUpdateMaintenanceRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateMaintenanceRequest> }) =>
      maintenanceApi.updateMaintenanceRequest(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() });
      queryClient.setQueryData(maintenanceKeys.detail(data.id), data);
      toast.success("Cập nhật yêu cầu bảo trì thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi cập nhật yêu cầu bảo trì: ${error.message}`);
    },
  });
};

export const useDeleteMaintenanceRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: maintenanceApi.deleteMaintenanceRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() });
      toast.success("Xóa yêu cầu bảo trì thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi xóa yêu cầu bảo trì: ${error.message}`);
    },
  });
};
