"use client";

import { useState } from "react";
import {
  AlertCircle,
  Bath,
  Bed,
  Camera,
  Car,
  Check,
  Copy,
  Download,
  Droplets,
  Edit,
  Eye,
  FileText,
  Filter,
  Home,
  Lightbulb,
  MoreHorizontal,
  Palette,
  Plus,
  Ruler,
  Save,
  Search,
  Shield,
  Square,
  Thermometer,
  Upload,
  Wifi,
  Wind,
  X,
  Zap,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";

interface UnitSpecificationsProps {
  className?: string;
}

export function UnitSpecifications({ className }: UnitSpecificationsProps) {
  const { t } = useTranslation();
  const [selectedProperty, setSelectedProperty] = useState<string>("all");
  const [selectedUnit, setSelectedUnit] = useState<any>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isAddSpecOpen, setIsAddSpecOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  // Mock unit specifications data
  const units = [
    {
      id: "1",
      unitNumber: "101",
      property: "Sunset Apartments",
      type: "1-Bedroom",
      status: "Occupied",
      specifications: {
        basic: {
          bedrooms: 1,
          bathrooms: 1,
          totalArea: 750,
          livingArea: 600,
          balconyArea: 150,
          ceilingHeight: 9,
          floorLevel: 1,
          orientation: "South-East",
          buildYear: 2018,
        },
        layout: {
          entryway: true,
          livingRoom: { area: 300, features: ["Bay Window", "Hardwood Floors"] },
          kitchen: { area: 120, features: ["Granite Counters", "Stainless Appliances"] },
          bedroom: { area: 180, features: ["Walk-in Closet", "En-suite"] },
          bathroom: { area: 60, features: ["Tile Shower", "Vanity"] },
          balcony: { area: 150, features: ["City View", "Privacy Screen"] },
        },
        amenities: [
          "Air Conditioning",
          "Heating",
          "Dishwasher",
          "Washer/Dryer",
          "High-Speed Internet",
          "Cable Ready",
          "Smoke Detector",
          "Security System",
        ],
        fixtures: {
          flooring: {
            livingRoom: "Hardwood",
            bedroom: "Carpet",
            kitchen: "Tile",
            bathroom: "Tile",
          },
          lighting: {
            livingRoom: "Recessed LED",
            bedroom: "Ceiling Fan",
            kitchen: "Under-cabinet",
          },
          appliances: ["Refrigerator", "Stove", "Microwave", "Dishwasher", "Washer", "Dryer"],
          windows: { type: "Double-pane", count: 4, treatment: "Blinds included" },
        },
        condition: {
          overall: "Excellent",
          lastInspected: "2024-01-15",
          lastUpdated: "2023-11-20",
          issues: [],
          maintenanceSchedule: "Quarterly",
          nextInspection: "2024-04-15",
        },
      },
      monthlyRent: 2200,
      marketValue: 285000,
      lastUpdated: "2024-01-20",
    },
    {
      id: "2",
      unitNumber: "205",
      property: "Downtown Lofts",
      type: "2-Bedroom",
      status: "Vacant",
      specifications: {
        basic: {
          bedrooms: 2,
          bathrooms: 2,
          totalArea: 1200,
          livingArea: 950,
          balconyArea: 250,
          ceilingHeight: 12,
          floorLevel: 2,
          orientation: "North-West",
          buildYear: 2020,
        },
        layout: {
          entryway: true,
          livingRoom: { area: 450, features: ["Floor-to-ceiling Windows", "Exposed Brick"] },
          kitchen: { area: 180, features: ["Island", "Premium Appliances"] },
          bedroom1: { area: 200, features: ["Master Suite", "Walk-in Closet"] },
          bedroom2: { area: 150, features: ["Built-in Desk", "Murphy Bed"] },
          bathroom1: { area: 80, features: ["Soaking Tub", "Double Vanity"] },
          bathroom2: { area: 40, features: ["Glass Shower", "Modern Fixtures"] },
          balcony: { area: 250, features: ["Skyline View", "Gas Line"] },
        },
        amenities: [
          "Central Air",
          "Floor Heating",
          "Smart Home System",
          "In-unit Laundry",
          "Fiber Internet",
          "Surround Sound Pre-wire",
          "Security Camera",
          "Smart Locks",
        ],
        fixtures: {
          flooring: {
            livingRoom: "Polished Concrete",
            bedroom: "Luxury Vinyl",
            kitchen: "Quartz",
            bathroom: "Porcelain",
          },
          lighting: {
            livingRoom: "Track Lighting",
            bedroom: "Smart Switches",
            kitchen: "Pendant Lights",
          },
          appliances: ["Premium Fridge", "Gas Range", "Wine Cooler", "Washer/Dryer Combo"],
          windows: { type: "Floor-to-ceiling", count: 8, treatment: "Motorized Blinds" },
        },
        condition: {
          overall: "New",
          lastInspected: "2024-01-10",
          lastUpdated: "2024-01-10",
          issues: [],
          maintenanceSchedule: "Semi-annual",
          nextInspection: "2024-07-10",
        },
      },
      monthlyRent: 3500,
      marketValue: 485000,
      lastUpdated: "2024-01-18",
    },
  ];

  const amenityCategories = {
    Essential: ["Air Conditioning", "Heating", "Security System", "Smoke Detector"],
    Appliances: ["Dishwasher", "Washer/Dryer", "Refrigerator", "Stove", "Microwave"],
    Technology: ["High-Speed Internet", "Cable Ready", "Smart Home System", "Fiber Internet"],
    Luxury: ["Wine Cooler", "Soaking Tub", "Premium Appliances", "Smart Locks"],
  };

  const conditionOptions = ["Excellent", "Good", "Fair", "Needs Attention", "Under Renovation"];
  const flooringOptions = ["Hardwood", "Carpet", "Tile", "Vinyl", "Laminate", "Concrete"];
  const orientationOptions = [
    "North",
    "North-East",
    "East",
    "South-East",
    "South",
    "South-West",
    "West",
    "North-West",
  ];

  const filteredUnits = units.filter(
    (unit) =>
      (selectedProperty === "all" || unit.property === selectedProperty) &&
      (searchTerm === "" ||
        unit.unitNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        unit.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        unit.property.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Occupied":
        return "bg-success/10 text-success border-success/20";
      case "Vacant":
        return "bg-warning/10 text-warning border-warning/20";
      case "Maintenance":
        return "bg-destructive/10 text-destructive border-destructive/20";
      default:
        return "bg-muted text-muted-foreground border-muted-foreground/20";
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case "Excellent":
        return "text-success";
      case "Good":
        return "text-primary";
      case "Fair":
        return "text-warning";
      case "Needs Attention":
        return "text-warning";
      case "Under Renovation":
        return "text-destructive";
      default:
        return "text-muted-foreground";
    }
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Unit Specifications Management</h2>
          <p className="text-sm text-muted-foreground">
            Detailed unit specifications, amenities, and condition tracking
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isAddSpecOpen} onOpenChange={setIsAddSpecOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 size-4" />
                Add Specifications
              </Button>
            </DialogTrigger>
            <DialogContent className="max-h-[80vh] max-w-2xl overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Add Unit Specifications</DialogTitle>
                <DialogDescription>Create detailed specifications for a new unit</DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Property</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select property" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sunset">Sunset Apartments</SelectItem>
                        <SelectItem value="downtown">Downtown Lofts</SelectItem>
                        <SelectItem value="garden">Garden View Complex</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Unit Number</Label>
                    <Input placeholder="e.g., 101" />
                  </div>
                </div>

                <Separator />

                <div>
                  <h4 className="mb-3 font-medium">Basic Information</h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label>Bedrooms</Label>
                      <Input type="number" placeholder="1" />
                    </div>
                    <div>
                      <Label>Bathrooms</Label>
                      <Input type="number" step="0.5" placeholder="1" />
                    </div>
                    <div>
                      <Label>Total Area (sq ft)</Label>
                      <Input type="number" placeholder="750" />
                    </div>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddSpecOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsAddSpecOpen(false)}>Create Specifications</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col gap-4 sm:flex-row">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Search units..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedProperty} onValueChange={setSelectedProperty}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Properties" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              <SelectItem value="Sunset Apartments">Sunset Apartments</SelectItem>
              <SelectItem value="Downtown Lofts">Downtown Lofts</SelectItem>
              <SelectItem value="Garden View Complex">Garden View Complex</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Units Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 xl:grid-cols-3">
        {filteredUnits.map((unit) => (
          <Card key={unit.id} className="transition-shadow hover:shadow-lg">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">Unit {unit.unitNumber}</CardTitle>
                  <p className="text-sm text-muted-foreground">{unit.property}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusColor(unit.status)}>{unit.status}</Badge>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="size-8 p-0">
                        <MoreHorizontal className="size-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => setSelectedUnit(unit)}>
                        <Eye className="mr-2 size-4" />
                        View Details
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Edit className="mr-2 size-4" />
                        Edit Specifications
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Copy className="mr-2 size-4" />
                        Copy Specifications
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>
                        <Download className="mr-2 size-4" />
                        Export Report
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Basic Info */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="flex items-center space-x-2">
                  <Bed className="size-4 text-muted-foreground" />
                  <span>{unit.specifications.basic.bedrooms} Bed</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Bath className="size-4 text-muted-foreground" />
                  <span>{unit.specifications.basic.bathrooms} Bath</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Square className="size-4 text-muted-foreground" />
                  <span>{unit.specifications.basic.totalArea} sq ft</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Home className="size-4 text-muted-foreground" />
                  <span>Floor {unit.specifications.basic.floorLevel}</span>
                </div>
              </div>

              <Separator />

              {/* Key Features */}
              <div>
                <h4 className="mb-2 text-sm font-medium">Key Features</h4>
                <div className="flex flex-wrap gap-1">
                  {unit.specifications.amenities.slice(0, 4).map((amenity, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {amenity}
                    </Badge>
                  ))}
                  {unit.specifications.amenities.length > 4 && (
                    <Badge variant="outline" className="text-xs">
                      +{unit.specifications.amenities.length - 4} more
                    </Badge>
                  )}
                </div>
              </div>

              {/* Condition */}
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-sm text-muted-foreground">Condition: </span>
                  <span
                    className={`text-sm font-medium ${getConditionColor(unit.specifications.condition.overall)}`}>
                    {unit.specifications.condition.overall}
                  </span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-semibold">
                    ${unit.monthlyRent.toLocaleString()}/mo
                  </div>
                  <div className="text-xs text-muted-foreground">
                    Value: ${unit.marketValue.toLocaleString()}
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2 pt-2">
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1"
                  onClick={() => setSelectedUnit(unit)}>
                  <Eye className="mr-1 size-3" />
                  Details
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Edit className="mr-1 size-3" />
                  Edit
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Unit Details Modal */}
      <Dialog open={!!selectedUnit} onOpenChange={() => setSelectedUnit(null)}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          {selectedUnit && (
            <div>
              <DialogHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <DialogTitle>Unit {selectedUnit.unitNumber} Specifications</DialogTitle>
                    <DialogDescription>{selectedUnit.property}</DialogDescription>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getStatusColor(selectedUnit.status)}>
                      {selectedUnit.status}
                    </Badge>
                    <Button variant="outline" size="sm">
                      <Edit className="mr-2 size-4" />
                      Edit
                    </Button>
                  </div>
                </div>
              </DialogHeader>

              <Tabs defaultValue="basic" className="mt-6">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="layout">Layout</TabsTrigger>
                  <TabsTrigger value="amenities">Amenities</TabsTrigger>
                  <TabsTrigger value="fixtures">Fixtures</TabsTrigger>
                  <TabsTrigger value="condition">Condition</TabsTrigger>
                </TabsList>

                <TabsContent value="basic" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Bedrooms</Label>
                      <div className="flex items-center space-x-2">
                        <Bed className="size-4 text-muted-foreground" />
                        <span>{selectedUnit.specifications.basic.bedrooms}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Bathrooms</Label>
                      <div className="flex items-center space-x-2">
                        <Bath className="size-4 text-muted-foreground" />
                        <span>{selectedUnit.specifications.basic.bathrooms}</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Total Area</Label>
                      <div className="flex items-center space-x-2">
                        <Square className="size-4 text-muted-foreground" />
                        <span>{selectedUnit.specifications.basic.totalArea} sq ft</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Living Area</Label>
                      <span>{selectedUnit.specifications.basic.livingArea} sq ft</span>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Balcony Area</Label>
                      <span>{selectedUnit.specifications.basic.balconyArea} sq ft</span>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Ceiling Height</Label>
                      <div className="flex items-center space-x-2">
                        <Ruler className="size-4 text-muted-foreground" />
                        <span>{selectedUnit.specifications.basic.ceilingHeight} ft</span>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Floor Level</Label>
                      <span>Floor {selectedUnit.specifications.basic.floorLevel}</span>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Orientation</Label>
                      <span>{selectedUnit.specifications.basic.orientation}</span>
                    </div>
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Build Year</Label>
                      <span>{selectedUnit.specifications.basic.buildYear}</span>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="layout" className="space-y-4">
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    {Object.entries(selectedUnit.specifications.layout).map(([room, details]) => {
                      if (typeof details === "boolean") return null;
                      return (
                        <Card key={room}>
                          <CardContent className="p-4">
                            <h4 className="mb-2 font-medium capitalize">
                              {room.replace(/([A-Z])/g, " $1")}
                            </h4>
                            <div className="space-y-2 text-sm">
                              <div>
                                <span className="text-muted-foreground">Area: </span>
                                <span>{(details as any).area} sq ft</span>
                              </div>
                              {(details as any).features && (
                                <div>
                                  <span className="text-muted-foreground">Features: </span>
                                  <div className="mt-1 flex flex-wrap gap-1">
                                    {(details as any).features.map(
                                      (feature: string, index: number) => (
                                        <Badge key={index} variant="outline" className="text-xs">
                                          {feature}
                                        </Badge>
                                      )
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </TabsContent>

                <TabsContent value="amenities" className="space-y-4">
                  {Object.entries(amenityCategories).map(([category, categoryAmenities]) => (
                    <div key={category}>
                      <h4 className="mb-3 font-medium">{category}</h4>
                      <div className="grid grid-cols-2 gap-2 md:grid-cols-3">
                        {categoryAmenities.map((amenity) => (
                          <div key={amenity} className="flex items-center space-x-2">
                            <div className="shrink-0">
                              {selectedUnit.specifications.amenities.includes(amenity) ? (
                                <Check className="size-4 text-success" />
                              ) : (
                                <X className="size-4 text-muted-foreground" />
                              )}
                            </div>
                            <span
                              className={`text-sm ${
                                selectedUnit.specifications.amenities.includes(amenity)
                                  ? "text-foreground"
                                  : "text-muted-foreground"
                              }`}>
                              {amenity}
                            </span>
                          </div>
                        ))}
                      </div>
                      <Separator className="mt-4" />
                    </div>
                  ))}
                </TabsContent>

                <TabsContent value="fixtures" className="space-y-4">
                  <div className="space-y-6">
                    <div>
                      <h4 className="mb-3 flex items-center font-medium">
                        <Palette className="mr-2 size-4" />
                        Flooring
                      </h4>
                      <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                        {Object.entries(selectedUnit.specifications.fixtures.flooring).map(
                          ([room, type]) => (
                            <div key={room} className="text-sm">
                              <span className="capitalize text-muted-foreground">{room}: </span>
                              <span className="font-medium">{String(type)}</span>
                            </div>
                          )
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="mb-3 flex items-center font-medium">
                        <Lightbulb className="mr-2 size-4" />
                        Lighting
                      </h4>
                      <div className="grid grid-cols-2 gap-4 md:grid-cols-3">
                        {Object.entries(selectedUnit.specifications.fixtures.lighting).map(
                          ([room, type]) => (
                            <div key={room} className="text-sm">
                              <span className="capitalize text-muted-foreground">{room}: </span>
                              <span className="font-medium">{String(type)}</span>
                            </div>
                          )
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="mb-3 flex items-center font-medium">
                        <Zap className="mr-2 size-4" />
                        Appliances
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {selectedUnit.specifications.fixtures.appliances.map(
                          (appliance: any, index: number) => (
                            <Badge key={index} variant="outline">
                              {appliance}
                            </Badge>
                          )
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="mb-3 font-medium">Windows</h4>
                      <div className="grid grid-cols-2 gap-4 text-sm md:grid-cols-3">
                        <div>
                          <span className="text-muted-foreground">Type: </span>
                          <span className="font-medium">
                            {selectedUnit.specifications.fixtures.windows.type}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Count: </span>
                          <span className="font-medium">
                            {selectedUnit.specifications.fixtures.windows.count}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Treatment: </span>
                          <span className="font-medium">
                            {selectedUnit.specifications.fixtures.windows.treatment}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="condition" className="space-y-4">
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium">Overall Condition</Label>
                        <div
                          className={`text-lg font-semibold ${getConditionColor(selectedUnit.specifications.condition.overall)}`}>
                          {selectedUnit.specifications.condition.overall}
                        </div>
                      </div>

                      <div>
                        <Label className="text-sm font-medium">Last Inspected</Label>
                        <div>
                          {new Date(
                            selectedUnit.specifications.condition.lastInspected
                          ).toLocaleDateString()}
                        </div>
                      </div>

                      <div>
                        <Label className="text-sm font-medium">Next Inspection</Label>
                        <div>
                          {new Date(
                            selectedUnit.specifications.condition.nextInspection
                          ).toLocaleDateString()}
                        </div>
                      </div>

                      <div>
                        <Label className="text-sm font-medium">Maintenance Schedule</Label>
                        <div>{selectedUnit.specifications.condition.maintenanceSchedule}</div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium">Current Issues</Label>
                        {selectedUnit.specifications.condition.issues.length === 0 ? (
                          <div className="flex items-center space-x-2 text-success">
                            <Check className="size-4" />
                            <span>No current issues</span>
                          </div>
                        ) : (
                          <div className="space-y-2">
                            {selectedUnit.specifications.condition.issues.map(
                              (issue: string, index: number) => (
                                <div
                                  key={index}
                                  className="flex items-center space-x-2 text-warning">
                                  <AlertCircle className="size-4" />
                                  <span>{issue}</span>
                                </div>
                              )
                            )}
                          </div>
                        )}
                      </div>

                      <div>
                        <Label className="text-sm font-medium">Last Updated</Label>
                        <div>
                          {new Date(
                            selectedUnit.specifications.condition.lastUpdated
                          ).toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
