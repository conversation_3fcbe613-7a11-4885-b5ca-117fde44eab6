import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
import type { FinancialSummary, PaginatedResponse, ProfitLossReport, Transaction } from "../types";

// API functions (connecting to Vietnamese financial API endpoints)
const financialApi = {
  // Get financial summary
  getFinancialSummary: async (params?: {
    property_id?: string;
    time_range?: string;
  }): Promise<FinancialSummary> => {
    const searchParams = new URLSearchParams();

    if (params?.property_id) searchParams.append("property_id", params.property_id);
    if (params?.time_range) searchParams.append("time_range", params.time_range);

    const response = await fetch(
      `/api/property-assets/financial/summary?${searchParams.toString()}`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch financial summary");
    }

    const result = await response.json();
    return result.data;
  },

  // Get profit/loss report
  getProfitLossReport: async (params?: {
    start_date?: string;
    end_date?: string;
    property_id?: string;
    time_range?: string;
  }): Promise<ProfitLossReport> => {
    const searchParams = new URLSearchParams();

    if (params?.start_date) searchParams.append("start_date", params.start_date);
    if (params?.end_date) searchParams.append("end_date", params.end_date);
    if (params?.property_id) searchParams.append("property_id", params.property_id);
    if (params?.time_range) searchParams.append("time_range", params.time_range);

    const response = await fetch(
      `/api/property-assets/financial/profit-loss?${searchParams.toString()}`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch profit/loss report");
    }

    const result = await response.json();
    return result.data;
  },

  // Get transactions
  getTransactions: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    transaction_type?: string;
    property_id?: string;
  }): Promise<PaginatedResponse<Transaction>> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.limit) searchParams.append("limit", params.limit.toString());
    if (params?.search) searchParams.append("search", params.search);
    if (params?.transaction_type) searchParams.append("transaction_type", params.transaction_type);
    if (params?.property_id) searchParams.append("property_id", params.property_id);

    const response = await fetch(
      `/api/property-assets/financial/transactions?${searchParams.toString()}`
    );
    if (!response.ok) {
      throw new Error("Failed to fetch transactions");
    }

    return response.json();
  },
};

// Query keys
export const financialKeys = {
  all: ["financial"] as const,
  summary: (params?: any) => [...financialKeys.all, "summary", params] as const,
  profitLoss: (params?: any) => [...financialKeys.all, "profit-loss", params] as const,
  transactions: (params?: any) => [...financialKeys.all, "transactions", params] as const,
};

// Hooks
export const useFinancialSummary = (params?: { property_id?: string; time_range?: string }) => {
  return useQuery({
    queryKey: financialKeys.summary(params),
    queryFn: () => financialApi.getFinancialSummary(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProfitLossReport = (params?: {
  start_date?: string;
  end_date?: string;
  property_id?: string;
  time_range?: string;
}) => {
  return useQuery({
    queryKey: financialKeys.profitLoss(params),
    queryFn: () => financialApi.getProfitLossReport(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useTransactions = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  transaction_type?: string;
  property_id?: string;
}) => {
  return useQuery({
    queryKey: financialKeys.transactions(params),
    queryFn: () => financialApi.getTransactions(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useRevenueAnalytics = (params?: {
  start_date?: string;
  end_date?: string;
  property_id?: string;
  time_range?: string;
}) => {
  return useQuery({
    queryKey: [...financialKeys.all, "revenue", params] as const,
    queryFn: async () => {
      // Mock revenue analytics data for now
      return {
        total_revenue: 285000000,
        average_monthly_revenue: 47500000,
        revenue_growth: 12.5,
        revenue_by_month: [
          { month: "Jan", revenue: 45000000 },
          { month: "Feb", revenue: 47000000 },
          { month: "Mar", revenue: 46000000 },
          { month: "Apr", revenue: 48000000 },
          { month: "May", revenue: 50000000 },
          { month: "Jun", revenue: 52000000 },
        ],
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
