"use client";

import { use<PERSON><PERSON>back, useState } from "react";
import { useRouter } from "next/navigation";
import {
  AlertCircle,
  Calendar,
  Clock,
  DollarSign,
  Edit,
  FileText,
  Home,
  RefreshCw,
  Shield,
  Trash2,
  TrendingUp,
  User,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { authProtectedPaths } from "@/constants/paths";

import { useContract, useDeleteContract } from "../../hooks/useContracts";
import { usePayments } from "../../hooks/usePayments";
import type { Contract } from "../../types";

interface ContractDetailsComponentProps {
  contractId: string;
}

export function ContractDetailsComponent({ contractId }: ContractDetailsComponentProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const { data: contract, isLoading, error } = useContract(contractId);
  const { data: paymentsData } = usePayments({ contract_id: contractId, limit: 1000 });
  const payments = paymentsData?.items || [];
  const deleteContractMutation = useDeleteContract();

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.CONTRACTS_ID_EDIT.replace(":id", contractId) as any);
  }, [router, contractId]);

  const handleDelete = useCallback(async () => {
    try {
      await deleteContractMutation.mutateAsync(contractId);
      toast.success(t("contracts.messages.deleteSuccess"));
      router.push(authProtectedPaths.CONTRACTS as any);
    } catch (error) {
      // TODO: Implement proper error logging
      toast.error(t("contracts.messages.deleteError"));
    }
    setShowDeleteDialog(false);
  }, [deleteContractMutation, contractId, t, router]);

  const handleViewProperty = useCallback(() => {
    if (contract?.property_id) {
      router.push(authProtectedPaths.PROPERTIES_ID.replace(":id", contract.property_id) as any);
    }
  }, [contract, router]);

  const handleViewUnit = useCallback(() => {
    if (contract?.unit_id) {
      router.push(authProtectedPaths.UNITS_ID.replace(":id", contract.unit_id) as any);
    }
  }, [contract, router]);

  const handleViewTenant = useCallback(() => {
    if (contract?.tenant_id) {
      router.push(authProtectedPaths.TENANTS_ID.replace(":id", contract.tenant_id) as any);
    }
  }, [contract, router]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-muted-foreground">{t("common.loading")}</div>
      </div>
    );
  }

  if (error || !contract) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 size-12 text-muted-foreground" />
          <h3 className="mb-2 text-lg font-medium text-foreground">
            {t("contracts.errors.notFound")}
          </h3>
          <p className="mb-4 text-muted-foreground">{t("contracts.errors.notFoundDescription")}</p>
          <Button onClick={() => router.push(authProtectedPaths.CONTRACTS as any)}>
            {t("common.goBack")}
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: Contract["status"]) => {
    switch (status) {
      case "active":
        return "bg-success/10 text-success border-success/20";
      case "terminated":
        return "bg-destructive/10 text-destructive border-destructive/20";
      case "expired":
        return "bg-muted text-muted-foreground border-muted";
      case "pending":
        return "bg-warning/10 text-warning border-warning/20";
      default:
        return "bg-muted text-muted-foreground border-muted";
    }
  };

  const getContractTypeColor = (type: Contract["contract_type"]) => {
    switch (type) {
      case "monthly":
        return "bg-primary/10 text-primary border-primary/20";
      case "annual":
        return "bg-secondary/10 text-secondary-foreground border-secondary/20";
      case "profit_sharing":
        return "bg-accent/10 text-accent-foreground border-accent/20";
      case "revenue_sharing":
        return "bg-muted/50 text-foreground border-border";
      default:
        return "bg-muted text-muted-foreground border-muted";
    }
  };

  // Calculate payment statistics
  const totalPayments = payments.reduce(
    (sum, payment) => (payment.status === "completed" ? sum + payment.amount : sum),
    0
  );
  const pendingPayments = payments.filter((p) => p.status === "pending").length;
  const completedPayments = payments.filter((p) => p.status === "completed").length;

  // Calculate contract duration
  const startDate = new Date(contract.start_date);
  const endDate = contract.end_date ? new Date(contract.end_date) : null;
  const today = new Date();
  const isActive = contract.status === "active";

  let durationProgress = 0;
  let daysRemaining = 0;
  let totalDays = 0;

  if (endDate) {
    totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    const daysPassed = Math.ceil((today.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
    durationProgress = Math.min(100, Math.max(0, (daysPassed / totalDays) * 100));
    daysRemaining = Math.max(
      0,
      Math.ceil((endDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
    );
  }

  // Calculate expected revenue
  const monthlyRent = contract.rent_amount;
  const expectedAnnualRevenue = monthlyRent * 12;

  return (
    <div className="mx-auto max-w-6xl space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-foreground">
            {t("contracts.contractDetails")}
          </h1>
          <p className="text-sm text-muted-foreground">
            {t("contracts.contractId")}: {contract.id}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={handleEdit} variant="outline">
            <Edit className="mr-2 size-4" />
            {t("common.edit")}
          </Button>
          <Button
            onClick={() => setShowDeleteDialog(true)}
            variant="destructive"
            disabled={contract.status === "active"}>
            <Trash2 className="mr-2 size-4" />
            {t("common.delete")}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Info */}
        <div className="space-y-6 lg:col-span-2">
          {/* Contract Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center space-x-2">
                  <FileText className="size-5" />
                  <span>{t("contracts.overview")}</span>
                </span>
                <div className="flex items-center space-x-2">
                  <Badge className={getStatusColor(contract.status)}>
                    {t(`contracts.status.${contract.status}`)}
                  </Badge>
                  <Badge className={getContractTypeColor(contract.contract_type)}>
                    {t(`contracts.contractTypes.${contract.contract_type}`)}
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Property & Unit */}
              <div className="space-y-3">
                <div
                  className="flex cursor-pointer items-center justify-between rounded-lg bg-muted/50 p-3 hover:bg-muted"
                  onClick={handleViewProperty}>
                  <div className="flex items-center space-x-3">
                    <Home className="size-5 text-muted-foreground" />
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">
                        {t("contracts.fields.property")}
                      </div>
                      <div className="font-medium text-foreground">
                        {contract.property?.name || "N/A"}
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    {t("common.view")}
                  </Button>
                </div>

                <div
                  className="flex cursor-pointer items-center justify-between rounded-lg bg-muted/50 p-3 hover:bg-muted"
                  onClick={handleViewUnit}>
                  <div className="flex items-center space-x-3">
                    <Home className="size-5 text-muted-foreground" />
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">
                        {t("contracts.fields.unit")}
                      </div>
                      <div className="font-medium text-foreground">
                        {contract.unit?.unit_number || "N/A"}
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    {t("common.view")}
                  </Button>
                </div>

                <div
                  className="flex cursor-pointer items-center justify-between rounded-lg bg-muted/50 p-3 hover:bg-muted"
                  onClick={handleViewTenant}>
                  <div className="flex items-center space-x-3">
                    <User className="size-5 text-muted-foreground" />
                    <div>
                      <div className="text-sm font-medium text-muted-foreground">
                        {t("contracts.fields.tenant")}
                      </div>
                      <div className="font-medium text-foreground">
                        {contract.tenant
                          ? `${contract.tenant.first_name} ${contract.tenant.last_name}`
                          : "N/A"}
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    {t("common.view")}
                  </Button>
                </div>
              </div>

              <Separator />

              {/* Contract Duration */}
              <div className="space-y-3">
                <h4 className="flex items-center space-x-2 font-medium">
                  <Calendar className="size-4" />
                  <span>{t("contracts.duration")}</span>
                </h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">
                      {t("contracts.fields.startDate")}
                    </dt>
                    <dd className="text-foreground">
                      {new Date(contract.start_date).toLocaleDateString()}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">
                      {t("contracts.fields.endDate")}
                    </dt>
                    <dd className="text-foreground">
                      {contract.end_date
                        ? new Date(contract.end_date).toLocaleDateString()
                        : t("contracts.openEnded")}
                    </dd>
                  </div>
                </div>

                {endDate && isActive && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-muted-foreground">{t("contracts.progress")}</span>
                      <span className="font-medium">{Math.round(durationProgress)}%</span>
                    </div>
                    <Progress value={durationProgress} className="h-2" />
                    <div className="text-sm text-muted-foreground">
                      {daysRemaining} {t("contracts.daysRemaining")}
                    </div>
                  </div>
                )}
              </div>

              {contract.auto_renewal && (
                <div className="flex items-center space-x-2 rounded-lg border border-primary/20 bg-primary/5 p-3">
                  <RefreshCw className="size-4 text-primary" />
                  <span className="text-sm text-primary">{t("contracts.autoRenewalEnabled")}</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Financial Terms */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="size-5" />
                <span>{t("contracts.sections.financialTerms")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("contracts.fields.rentAmount")}
                  </dt>
                  <dd className="text-2xl font-semibold text-foreground">
                    ${contract.rent_amount.toLocaleString()}
                  </dd>
                  <span className="text-sm text-muted-foreground">{t("contracts.perMonth")}</span>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("contracts.fields.depositAmount")}
                  </dt>
                  <dd className="text-2xl font-semibold text-foreground">
                    ${contract.deposit_amount.toLocaleString()}
                  </dd>
                </div>
              </div>

              {contract.late_fee_amount && (
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("contracts.fields.lateFeeAmount")}
                  </dt>
                  <dd className="text-foreground">${contract.late_fee_amount.toLocaleString()}</dd>
                </div>
              )}

              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  {t("contracts.fields.rentDueDay")}
                </dt>
                <dd className="text-foreground">
                  {contract.rent_due_day}
                  {t("contracts.dayOfMonth")}
                </dd>
              </div>

              {(contract.profit_sharing_percentage || contract.revenue_sharing_percentage) && (
                <>
                  <Separator />
                  <div className="space-y-3">
                    <h4 className="flex items-center space-x-2 font-medium">
                      <TrendingUp className="size-4" />
                      <span>{t("contracts.sharingTerms")}</span>
                    </h4>
                    {contract.profit_sharing_percentage && (
                      <div>
                        <dt className="text-sm font-medium text-muted-foreground">
                          {t("contracts.fields.profitSharingPercentage")}
                        </dt>
                        <dd className="text-lg font-semibold text-foreground">
                          {contract.profit_sharing_percentage}%
                        </dd>
                      </div>
                    )}
                    {contract.revenue_sharing_percentage && (
                      <div>
                        <dt className="text-sm font-medium text-muted-foreground">
                          {t("contracts.fields.revenueSharingPercentage")}
                        </dt>
                        <dd className="text-lg font-semibold text-foreground">
                          {contract.revenue_sharing_percentage}%
                        </dd>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>

          {/* Terms and Conditions */}
          {contract.terms_and_conditions && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="size-5" />
                  <span>{t("contracts.sections.termsConditions")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose prose-sm max-w-none text-foreground">
                  {contract.terms_and_conditions.split("\n").map((paragraph, index) => (
                    <p key={index} className="mb-2">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t("contracts.paymentSummary")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t("contracts.stats.totalPayments")}</span>
                <span className="font-semibold text-foreground">{payments.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {t("contracts.stats.completedPayments")}
                </span>
                <span className="font-semibold text-foreground">{completedPayments}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">
                  {t("contracts.stats.pendingPayments")}
                </span>
                <span className="font-semibold text-foreground">{pendingPayments}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t("contracts.stats.totalCollected")}</span>
                <span className="text-lg font-semibold text-foreground">
                  ${totalPayments.toLocaleString()}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Revenue Projections */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="size-5" />
                <span>{t("contracts.revenueProjections")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  {t("contracts.projections.expectedMonthly")}
                </dt>
                <dd className="text-lg font-semibold text-foreground">
                  ${monthlyRent.toLocaleString()}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  {t("contracts.projections.expectedAnnual")}
                </dt>
                <dd className="text-lg font-semibold text-foreground">
                  ${expectedAnnualRevenue.toLocaleString()}
                </dd>
              </div>
            </CardContent>
          </Card>

          {/* Contract Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Clock className="size-5" />
                <span>{t("contracts.info")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  {t("contracts.fields.noticePeriod")}
                </dt>
                <dd className="text-foreground">
                  {contract.notice_period_days} {t("contracts.days")}
                </dd>
              </div>
              <Separator />
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  {t("common.createdAt")}
                </dt>
                <dd className="text-foreground">
                  {new Date(contract.created_at).toLocaleString()}
                </dd>
              </div>
              <div>
                <dt className="text-sm font-medium text-muted-foreground">
                  {t("common.updatedAt")}
                </dt>
                <dd className="text-foreground">
                  {new Date(contract.updated_at).toLocaleString()}
                </dd>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t("contracts.quickActions")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button
                className="w-full justify-start"
                variant="outline"
                onClick={() => router.push(authProtectedPaths.PAYMENTS_NEW as any)}>
                <DollarSign className="mr-2 size-4" />
                {t("contracts.actions.recordPayment")}
              </Button>
              <Button
                className="w-full justify-start"
                variant="outline"
                onClick={() => router.push(authProtectedPaths.PAYMENTS as any)}>
                <FileText className="mr-2 size-4" />
                {t("contracts.actions.viewPayments")}
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("contracts.deleteConfirmation.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {contract.status === "active"
                ? t("contracts.deleteConfirmation.activeContract")
                : t("contracts.deleteConfirmation.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={contract.status === "active" || deleteContractMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {deleteContractMutation.isPending ? t("common.deleting") : t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
