import { NextRequest, NextResponse } from "next/server";

import type {
  CreateMaintenanceRequest,
  MaintenanceRequest,
} from "@/features/property-assets/types";

import { maintenanceRequestsMockData } from "../mock-data";

// GET /api/property-assets/maintenance
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") as MaintenanceRequest["status"] | null;
    const priority = searchParams.get("priority") as MaintenanceRequest["priority"] | null;
    const category = searchParams.get("category") as MaintenanceRequest["category"] | null;
    const property_id = searchParams.get("property_id") || "";
    const unit_id = searchParams.get("unit_id") || "";

    let filteredRequests = [...maintenanceRequestsMockData];

    // Apply property filter
    if (property_id) {
      filteredRequests = filteredRequests.filter((request) => request.property_id === property_id);
    }

    // Apply unit filter
    if (unit_id) {
      filteredRequests = filteredRequests.filter((request) => request.unit_id === unit_id);
    }

    // Apply search filter
    if (search) {
      filteredRequests = filteredRequests.filter(
        (request) =>
          request.title.toLowerCase().includes(search.toLowerCase()) ||
          request.description.toLowerCase().includes(search.toLowerCase()) ||
          (request.contractor_name &&
            request.contractor_name.toLowerCase().includes(search.toLowerCase())) ||
          (request.notes && request.notes.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply status filter
    if (status) {
      filteredRequests = filteredRequests.filter((request) => request.status === status);
    }

    // Apply priority filter
    if (priority) {
      filteredRequests = filteredRequests.filter((request) => request.priority === priority);
    }

    // Apply category filter
    if (category) {
      filteredRequests = filteredRequests.filter((request) => request.category === category);
    }

    // Sort by priority (high -> medium -> low) then by reported date (newest first)
    const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
    filteredRequests.sort((a, b) => {
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return new Date(b.reported_date).getTime() - new Date(a.reported_date).getTime();
    });

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedRequests = filteredRequests.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 360));

    return NextResponse.json({
      items: paginatedRequests,
      total: filteredRequests.length,
      page,
      limit,
      totalPages: Math.ceil(filteredRequests.length / limit),
    });
  } catch (error) {
    console.error("Error fetching maintenance requests:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/property-assets/maintenance
export async function POST(request: NextRequest) {
  try {
    const body: CreateMaintenanceRequest = await request.json();

    // Validate required fields
    if (!body.property_id || !body.title || !body.description || !body.priority || !body.category) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Validate estimated cost is non-negative if provided
    if (body.estimated_cost !== undefined && body.estimated_cost < 0) {
      return NextResponse.json({ error: "Estimated cost cannot be negative" }, { status: 400 });
    }

    // Validate dates if provided
    if (body.scheduled_date) {
      const scheduledDate = new Date(body.scheduled_date);
      const reportedDate = new Date();

      if (scheduledDate < reportedDate) {
        return NextResponse.json(
          { error: "Scheduled date cannot be before reported date" },
          { status: 400 }
        );
      }
    }

    // Transform images to match MaintenanceRequest format
    const transformedImages = body.images?.map((img, index) => ({
      id: `img_${Date.now()}_${index}`,
      name: img.name,
      url: img.image,
      uploaded_at: new Date().toISOString(),
    }));

    // Create new maintenance request
    const newRequest: MaintenanceRequest = {
      id: `maint_${Date.now()}`,
      ...body,
      images: transformedImages,
      status: "open", // Default status for new requests
      reported_date: new Date().toISOString().split("T")[0],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company1", // Mock company ID
    };

    // Add to mock data
    maintenanceRequestsMockData.push(newRequest);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 520));

    return NextResponse.json({
      data: newRequest,
      success: true,
      message: "Maintenance request created successfully",
    });
  } catch (error) {
    console.error("Error creating maintenance request:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
