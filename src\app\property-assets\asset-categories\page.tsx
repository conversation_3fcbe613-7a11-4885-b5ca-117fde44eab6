"use client";

import { useTranslation } from "react-i18next";

import { AssetCategories } from "@/features/property-assets/components/AssetCategories/AssetCategories";

import { CustomBreadcrumb } from "@/components/Layout/CustomBreadCrumb/custom-breadcrumb";

export default function AssetCategoriesPage() {
  const { t } = useTranslation();

  return (
    <div className="p-6">
      {/* Header with Breadcrumb */}
      <div className="mb-6">
        <CustomBreadcrumb />
      </div>

      {/* AssetCategories Component */}
      <AssetCategories />
    </div>
  );
}
