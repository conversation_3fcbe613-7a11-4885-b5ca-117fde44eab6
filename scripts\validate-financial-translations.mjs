#!/usr/bin/env node

/**
 * Validate Financial Translation Keys
 * Ensures all financial translation keys used in FinancialDashboard are available in locale files
 */

import fs from 'fs';
import path from 'path';

const COMPONENT_PATH = 'src/features/property-assets/components/Financial/FinancialDashboard.tsx';
const EN_LOCALE_PATH = 'src/i18n/locales/en.json';
const VI_LOCALE_PATH = 'src/i18n/locales/vi.json';

function extractTranslationKeys(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const keyPattern = /t\("(financial\.[^"]+)"\)/g;
  const keys = [];
  let match;
  
  while ((match = keyPattern.exec(content)) !== null) {
    keys.push(match[1]);
  }
  
  return [...new Set(keys)].sort();
}

function checkKeyExists(obj, keyPath) {
  const keys = keyPath.split('.');
  let current = obj;
  
  for (const key of keys) {
    if (current === null || current === undefined || !(key in current)) {
      return false;
    }
    current = current[key];
  }
  
  return true;
}

function validateTranslations() {
  console.log('🔍 Validating Financial Translation Keys');
  console.log('=====================================\n');
  
  // Extract keys from component
  const requiredKeys = extractTranslationKeys(COMPONENT_PATH);
  console.log(`📋 Found ${requiredKeys.length} translation keys in component:\n`);
  requiredKeys.forEach(key => console.log(`   - ${key}`));
  
  // Load locale files
  const enLocale = JSON.parse(fs.readFileSync(EN_LOCALE_PATH, 'utf8'));
  const viLocale = JSON.parse(fs.readFileSync(VI_LOCALE_PATH, 'utf8'));
  
  console.log('\n🔍 Checking English translations:');
  console.log('─'.repeat(40));
  let enMissing = 0;
  requiredKeys.forEach(key => {
    const exists = checkKeyExists(enLocale, key);
    console.log(`   ${exists ? '✅' : '❌'} ${key}`);
    if (!exists) enMissing++;
  });
  
  console.log('\n🔍 Checking Vietnamese translations:');
  console.log('─'.repeat(40));
  let viMissing = 0;
  requiredKeys.forEach(key => {
    const exists = checkKeyExists(viLocale, key);
    console.log(`   ${exists ? '✅' : '❌'} ${key}`);
    if (!exists) viMissing++;
  });
  
  console.log('\n📊 Summary:');
  console.log('─'.repeat(20));
  console.log(`   Total keys: ${requiredKeys.length}`);
  console.log(`   English: ${requiredKeys.length - enMissing}/${requiredKeys.length} (${enMissing} missing)`);
  console.log(`   Vietnamese: ${requiredKeys.length - viMissing}/${requiredKeys.length} (${viMissing} missing)`);
  
  if (enMissing === 0 && viMissing === 0) {
    console.log('\n🎉 All financial translation keys are properly implemented!');
    return true;
  } else {
    console.log('\n❌ Some translation keys are missing. Please add them to the locale files.');
    return false;
  }
}

// Run validation
process.exit(validateTranslations() ? 0 : 1);