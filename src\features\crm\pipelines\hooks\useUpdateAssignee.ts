import { useCallback, useState } from "react";
import { useQueryClient } from "@tanstack/react-query";

import { Assignee } from "@/lib/apis/types/crm";

interface UseUpdateAssigneeOptions {
  queryKey: any;
  updateMutation: any;
  onSuccess?: () => void;
  onError?: (error: any) => void;
}

export function useUpdateAssignee({
  queryKey,
  updateMutation,
  onSuccess,
  onError,
}: UseUpdateAssigneeOptions) {
  const queryClient = useQueryClient();
  const [optimisticUpdates, setOptimisticUpdates] = useState<Record<string, any>>({});

  const handleAssigneeUpdate = useCallback(
    async (itemId: string, assignee: Assignee | null, items: any[]) => {
      // Find the item in the current items array
      const item = items.find((item) => item.id === itemId);
      if (!item) return;

      // Store the original item for rollback
      const originalItem = item;

      // Optimistically update the UI immediately
      const optimisticItem = {
        ...originalItem,
        assignee: assignee,
      };

      setOptimisticUpdates((prev) => ({
        ...prev,
        [itemId]: optimisticItem,
      }));

      try {
        // Make the API call
        await updateMutation.mutateAsync({
          id: itemId,
          data: {
            assignee_id: assignee?.id || null,
          } as any, // Cast to any since the API expects assignee_id but type might not have it
        });

        // API call successful, clear optimistic update and update the cache directly
        setOptimisticUpdates((prev) => {
          const newUpdates = { ...prev };
          delete newUpdates[itemId];
          return newUpdates;
        });

        // Update the cache directly instead of refetching
        queryClient.setQueriesData<any>({ queryKey }, (oldData: any) => {
          if (!oldData) return oldData;
          return {
            ...oldData,
            pages: oldData.pages.map((page: any) => ({
              ...page,
              items: page.items.map((item: any) =>
                item.id === itemId ? { ...item, assignee: assignee } : item
              ),
            })),
          };
        });

        // Call success callback if provided
        if (onSuccess) {
          onSuccess();
        }
      } catch (error) {
        console.error("Error updating assignee:", error);

        // Rollback the optimistic update on error
        setOptimisticUpdates((prev) => {
          const newUpdates = { ...prev };
          delete newUpdates[itemId];
          return newUpdates;
        });

        // Call error callback if provided
        if (onError) {
          onError(error);
        }
      }
    },
    [queryClient, updateMutation, onSuccess, onError]
  );

  // Merge optimistic updates with actual data
  const getItemsWithOptimisticUpdates = useCallback(
    (items: any[]) => {
      return items.map((item) => {
        const optimisticUpdate = optimisticUpdates[item.id];
        return optimisticUpdate || item;
      });
    },
    [optimisticUpdates]
  );

  return {
    handleAssigneeUpdate,
    getItemsWithOptimisticUpdates,
    optimisticUpdates,
  };
}
