import { Suspense } from "react";
import { Metadata } from "next";

import { TenantDetailsComponent } from "@/features/property-assets/components/TenantDetails";

export const metadata: Metadata = {
  title: "Tenant Details | OneX ERP",
  description: "View detailed tenant information, rental history, and portal access",
};

interface TenantDetailPageProps {
  params: {
    id: string;
  };
}

export default function TenantDetailPage({ params }: TenantDetailPageProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <TenantDetailsComponent tenantId={params.id} />
    </Suspense>
  );
}
