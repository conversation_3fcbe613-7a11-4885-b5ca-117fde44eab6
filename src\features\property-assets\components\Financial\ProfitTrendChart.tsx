"use client";

import { useState } from "react";
import {
  AlertCircle,
  BarChart3,
  Calendar,
  DollarSign,
  Download,
  Filter,
  Info,
  LineChart as LineChartIcon,
  Target,
  TrendingDown,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  LineChart,
  Tooltip as RechartsTooltip,
  ReferenceArea,
  ReferenceLine,
  ResponsiveContainer,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Too<PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { useProfitLossReport } from "../../hooks/useFinancial";

interface ProfitTrendChartProps {
  propertyId?: string;
  className?: string;
}

export function ProfitTrendChart({ propertyId, className }: ProfitTrendChartProps) {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState<"month" | "quarter" | "year">("month");
  const [chartType, setChartType] = useState<"line" | "area" | "bar" | "combined">("line");
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");
  const [selectedMetric, setSelectedMetric] = useState<"profit" | "margin" | "roi">("profit");

  const { data: profitLossData, isLoading } = useProfitLossReport({
    property_id: selectedProperty === "all" ? undefined : selectedProperty,
    time_range: timeRange,
  });

  // Enhanced mock data for profit analysis
  const profitTrendData = [
    {
      period: "Jan 2023",
      revenue: 45000,
      expenses: 12000,
      grossProfit: 33000,
      netProfit: 31500,
      profitMargin: 70,
      roi: 8.5,
      cashFlow: 29000,
      target: 30000,
    },
    {
      period: "Feb 2023",
      revenue: 47000,
      expenses: 13000,
      grossProfit: 34000,
      netProfit: 32000,
      profitMargin: 68,
      roi: 8.8,
      cashFlow: 30200,
      target: 30000,
    },
    {
      period: "Mar 2023",
      revenue: 46000,
      expenses: 15000,
      grossProfit: 31000,
      netProfit: 29500,
      profitMargin: 64,
      roi: 7.9,
      cashFlow: 27800,
      target: 30000,
    },
    {
      period: "Apr 2023",
      revenue: 48000,
      expenses: 11000,
      grossProfit: 37000,
      netProfit: 35000,
      profitMargin: 73,
      roi: 9.5,
      cashFlow: 33500,
      target: 30000,
    },
    {
      period: "May 2023",
      revenue: 50000,
      expenses: 14000,
      grossProfit: 36000,
      netProfit: 34200,
      profitMargin: 68,
      roi: 9.2,
      cashFlow: 32800,
      target: 30000,
    },
    {
      period: "Jun 2023",
      revenue: 52000,
      expenses: 16000,
      grossProfit: 36000,
      netProfit: 34500,
      profitMargin: 66,
      roi: 9.1,
      cashFlow: 33200,
      target: 30000,
    },
  ];

  const quarterlyComparisonData = [
    { quarter: "2022 Q3", profit: 89000, margin: 65, roi: 7.8 },
    { quarter: "2022 Q4", profit: 92000, margin: 67, roi: 8.1 },
    { quarter: "2023 Q1", profit: 94500, margin: 67, roi: 8.4 },
    { quarter: "2023 Q2", profit: 103700, margin: 69, roi: 9.3 },
  ];

  const propertyProfitData = [
    {
      property: "Sunset Apartments",
      profit: 42000,
      margin: 72,
      roi: 9.8,
      trend: "up",
      trendValue: 12.5,
    },
    {
      property: "Downtown Lofts",
      profit: 35000,
      margin: 68,
      roi: 8.9,
      trend: "up",
      trendValue: 8.2,
    },
    {
      property: "Garden View Complex",
      profit: 28000,
      margin: 65,
      roi: 8.1,
      trend: "up",
      trendValue: 6.8,
    },
    {
      property: "Riverside Towers",
      profit: 15000,
      margin: 62,
      roi: 7.2,
      trend: "down",
      trendValue: -2.1,
    },
  ];

  // Calculate key metrics
  const currentProfit = profitTrendData[profitTrendData.length - 1]?.netProfit || 0;
  const previousProfit = profitTrendData[profitTrendData.length - 2]?.netProfit || 0;
  const profitGrowth = previousProfit
    ? ((currentProfit - previousProfit) / previousProfit) * 100
    : 0;

  const avgMargin =
    profitTrendData.reduce((sum, item) => sum + item.profitMargin, 0) / profitTrendData.length;
  const avgROI = profitTrendData.reduce((sum, item) => sum + item.roi, 0) / profitTrendData.length;

  const totalProfit = profitTrendData.reduce((sum, item) => sum + item.netProfit, 0);
  const targetAchievement =
    (profitTrendData.filter((item) => item.netProfit >= item.target).length /
      profitTrendData.length) *
    100;

  const renderChart = () => {
    const dataKey =
      selectedMetric === "profit"
        ? "netProfit"
        : selectedMetric === "margin"
          ? "profitMargin"
          : "roi";

    switch (chartType) {
      case "area":
        return (
          <ResponsiveContainer width="100%" height={350}>
            <AreaChart data={profitTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <RechartsTooltip
                formatter={(value, name) => [
                  selectedMetric === "profit"
                    ? `$${value.toLocaleString()}`
                    : selectedMetric === "margin"
                      ? `${value}%`
                      : `${value}%`,
                  name,
                ]}
              />
              <Legend />
              <Area
                type="monotone"
                dataKey={dataKey}
                stroke="#8884d8"
                fill="#8884d8"
                fillOpacity={0.6}
                name={
                  selectedMetric === "profit"
                    ? "Net Profit"
                    : selectedMetric === "margin"
                      ? "Profit Margin"
                      : "ROI"
                }
              />
              {selectedMetric === "profit" && (
                <ReferenceLine y={30000} stroke="red" strokeDasharray="5 5" label="Target" />
              )}
            </AreaChart>
          </ResponsiveContainer>
        );

      case "bar":
        return (
          <ResponsiveContainer width="100%" height={350}>
            <BarChart data={profitTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <RechartsTooltip
                formatter={(value) => [
                  selectedMetric === "profit" ? `$${value.toLocaleString()}` : `${value}%`,
                ]}
              />
              <Legend />
              <Bar
                dataKey={dataKey}
                fill="#8884d8"
                name={
                  selectedMetric === "profit"
                    ? "Net Profit"
                    : selectedMetric === "margin"
                      ? "Profit Margin"
                      : "ROI"
                }
              />
              {selectedMetric === "profit" && <Bar dataKey="target" fill="#82ca9d" name="Target" />}
            </BarChart>
          </ResponsiveContainer>
        );

      case "combined":
        return (
          <ResponsiveContainer width="100%" height={350}>
            <ComposedChart data={profitTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <RechartsTooltip />
              <Legend />
              <Bar yAxisId="left" dataKey="netProfit" fill="#8884d8" name="Net Profit" />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="profitMargin"
                stroke="#82ca9d"
                strokeWidth={3}
                name="Profit Margin (%)"
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="roi"
                stroke="#ff7300"
                strokeWidth={3}
                name="ROI (%)"
              />
            </ComposedChart>
          </ResponsiveContainer>
        );

      default: // line
        return (
          <ResponsiveContainer width="100%" height={350}>
            <LineChart data={profitTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="period" />
              <YAxis />
              <RechartsTooltip
                formatter={(value) => [
                  selectedMetric === "profit" ? `$${value.toLocaleString()}` : `${value}%`,
                ]}
              />
              <Legend />
              <Line
                type="monotone"
                dataKey={dataKey}
                stroke="#8884d8"
                strokeWidth={3}
                dot={{ fill: "#8884d8", strokeWidth: 2, r: 5 }}
                name={
                  selectedMetric === "profit"
                    ? "Net Profit"
                    : selectedMetric === "margin"
                      ? "Profit Margin"
                      : "ROI"
                }
              />
              {selectedMetric === "profit" && (
                <>
                  <ReferenceLine y={30000} stroke="red" strokeDasharray="5 5" label="Target" />
                  <ReferenceArea y1={28000} y2={32000} fill="#82ca9d" fillOpacity={0.2} />
                </>
              )}
            </LineChart>
          </ResponsiveContainer>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="size-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Profit Analysis Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">
            {t("financial.profitAnalysis.title")}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t("financial.profitAnalysis.description")}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedMetric} onValueChange={(value: any) => setSelectedMetric(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="profit">Profit</SelectItem>
              <SelectItem value="margin">Margin</SelectItem>
              <SelectItem value="roi">ROI</SelectItem>
            </SelectContent>
          </Select>
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">{t("financial.timeRange.month")}</SelectItem>
              <SelectItem value="quarter">{t("financial.timeRange.quarter")}</SelectItem>
              <SelectItem value="year">{t("financial.timeRange.year")}</SelectItem>
            </SelectContent>
          </Select>
          <Select value={chartType} onValueChange={(value: any) => setChartType(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="line">Line</SelectItem>
              <SelectItem value="area">Area</SelectItem>
              <SelectItem value="bar">Bar</SelectItem>
              <SelectItem value="combined">Combined</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Download className="mr-2 size-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Profit KPI Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Net Profit</p>
                <p className="text-2xl font-bold">${currentProfit.toLocaleString()}</p>
                <div className="mt-1 flex items-center space-x-1">
                  {profitGrowth > 0 ? (
                    <TrendingUp className="size-3 text-success" />
                  ) : (
                    <TrendingDown className="size-3 text-destructive" />
                  )}
                  <span
                    className={`text-xs ${profitGrowth > 0 ? "text-success" : "text-destructive"}`}>
                    {profitGrowth > 0 ? "+" : ""}
                    {profitGrowth.toFixed(1)}%
                  </span>
                </div>
              </div>
              <DollarSign className="size-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Margin</p>
                <p className="text-2xl font-bold">{avgMargin.toFixed(1)}%</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-primary" />
                  <span className="text-xs text-primary">+2.1%</span>
                </div>
              </div>
              <BarChart3 className="size-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg ROI</p>
                <p className="text-2xl font-bold">{avgROI.toFixed(1)}%</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-secondary-foreground" />
                  <span className="text-xs text-secondary-foreground">+0.8%</span>
                </div>
              </div>
              <Target className="size-8 text-secondary-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Target Achievement</p>
                <p className="text-2xl font-bold">{targetAchievement.toFixed(0)}%</p>
                <div className="mt-1 flex items-center space-x-1">
                  <Progress value={targetAchievement} className="h-2 w-12" />
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="size-3 text-muted-foreground" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Months hitting profit targets</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
              <LineChartIcon className="size-8 text-warning" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Profit Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <LineChartIcon className="size-5" />
            <span>Profit Trend Analysis</span>
          </CardTitle>
        </CardHeader>
        <CardContent>{renderChart()}</CardContent>
      </Card>

      {/* Profit Analysis Tabs */}
      <Tabs defaultValue="comparison" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="comparison">Quarterly</TabsTrigger>
          <TabsTrigger value="properties">By Property</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
          <TabsTrigger value="projections">Projections</TabsTrigger>
        </TabsList>

        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Quarterly Profit Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={quarterlyComparisonData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="quarter" />
                  <YAxis />
                  <RechartsTooltip
                    formatter={(value, name) => [
                      name === "profit" ? `$${value.toLocaleString()}` : `${value}%`,
                      name === "profit" ? "Profit" : name === "margin" ? "Margin" : "ROI",
                    ]}
                  />
                  <Legend />
                  <Bar dataKey="profit" fill="#8884d8" name="Profit" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="properties" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Profitability by Property</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {propertyProfitData.map((property, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-4">
                    <div>
                      <h4 className="font-medium">{property.property}</h4>
                      <div className="mt-1 flex items-center space-x-4">
                        <span className="text-sm text-muted-foreground">
                          Margin: {property.margin}%
                        </span>
                        <span className="text-sm text-muted-foreground">ROI: {property.roi}%</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">${property.profit.toLocaleString()}</p>
                      <div className="mt-1 flex items-center space-x-1">
                        {property.trend === "up" ? (
                          <TrendingUp className="size-3 text-success" />
                        ) : (
                          <TrendingDown className="size-3 text-destructive" />
                        )}
                        <span
                          className={`text-xs ${property.trend === "up" ? "text-success" : "text-destructive"}`}>
                          {property.trend === "up" ? "+" : ""}
                          {property.trendValue}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Performance Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="rounded-lg border border-success/20 bg-success/10 p-3">
                    <div className="flex items-center space-x-2">
                      <TrendingUp className="size-4 text-success" />
                      <span className="font-medium text-success">Strong Performance</span>
                    </div>
                    <p className="mt-1 text-sm text-success/90">
                      Net profit increased by {profitGrowth.toFixed(1)}% from last month.
                    </p>
                  </div>
                  <div className="rounded-lg border border-primary/20 bg-primary/10 p-3">
                    <div className="flex items-center space-x-2">
                      <Target className="size-4 text-primary" />
                      <span className="font-medium text-primary">Target Achievement</span>
                    </div>
                    <p className="mt-1 text-sm text-primary/90">
                      {targetAchievement.toFixed(0)}% of months hit profit targets this period.
                    </p>
                  </div>
                  <div className="rounded-lg border border-warning/20 bg-warning/10 p-3">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="size-4 text-warning" />
                      <span className="font-medium text-warning">Opportunity</span>
                    </div>
                    <p className="mt-1 text-sm text-warning/90">
                      Riverside Towers showing declining profitability trend.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Profitability Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="mb-2 flex justify-between text-sm">
                      <span className="text-muted-foreground">Profit Margin</span>
                      <span className="font-semibold">{avgMargin.toFixed(1)}%</span>
                    </div>
                    <Progress value={avgMargin} className="h-2" />
                  </div>
                  <Separator />
                  <div>
                    <div className="mb-2 flex justify-between text-sm">
                      <span className="text-muted-foreground">ROI</span>
                      <span className="font-semibold">{avgROI.toFixed(1)}%</span>
                    </div>
                    <Progress value={avgROI * 10} className="h-2" />
                  </div>
                  <Separator />
                  <div className="text-center">
                    <p className="mb-2 text-sm text-muted-foreground">Total Profit (6 months)</p>
                    <div className="text-2xl font-bold text-foreground">
                      ${totalProfit.toLocaleString()}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Average: ${(totalProfit / 6).toLocaleString()}/month
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="projections" className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Next Month</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-2xl font-bold text-success">$36,200</p>
                  <p className="text-sm text-muted-foreground">Projected Profit</p>
                  <div className="mt-2 flex items-center justify-center space-x-1">
                    <TrendingUp className="size-3 text-success" />
                    <span className="text-xs text-success">+4.9% growth</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Next Quarter</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-2xl font-bold text-primary">$108,500</p>
                  <p className="text-sm text-muted-foreground">Projected Profit</p>
                  <div className="mt-2 flex items-center justify-center space-x-1">
                    <TrendingUp className="size-3 text-primary" />
                    <span className="text-xs text-primary">+6.2% growth</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Annual Target</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <p className="text-2xl font-bold text-secondary-foreground">$420,000</p>
                  <p className="text-sm text-muted-foreground">Annual Target</p>
                  <div className="mt-2 flex items-center justify-center space-x-1">
                    <Progress value={75} className="h-2 w-16" />
                    <span className="text-xs text-secondary-foreground">75% achieved</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
