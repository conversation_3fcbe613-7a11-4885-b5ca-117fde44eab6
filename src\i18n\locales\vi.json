{"nav": {"overview": "<PERSON><PERSON><PERSON> quan", "patientManagement": "<PERSON><PERSON><PERSON><PERSON> lý b<PERSON>nh nhân", "doctorManagement": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> b<PERSON>", "medicalSupplies": "<PERSON><PERSON><PERSON> tư y tế", "invoicesPayments": "Hóa đơn & Thanh toán", "report": "Báo cáo", "administration": "<PERSON><PERSON><PERSON><PERSON> trị", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "productList": "<PERSON><PERSON> s<PERSON>ch sản ph<PERSON>m", "newProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "editProduct": "Chỉnh s<PERSON>a sản phẩm", "variantsList": "<PERSON><PERSON> s<PERSON>ch biến thể", "brandList": "<PERSON><PERSON> s<PERSON>ch th<PERSON><PERSON><PERSON> hi<PERSON>u", "categoryList": "<PERSON><PERSON> s<PERSON>ch danh mục", "order": "<PERSON><PERSON><PERSON> hàng", "orderList": "<PERSON><PERSON> s<PERSON>ch đơn hàng", "orderDetail": "<PERSON> tiết đơn hàng", "orderEdit": "Chỉnh sửa đơn hàng", "orderProcess": "<PERSON><PERSON> lý đơn hàng", "returnOrderList": "<PERSON><PERSON> s<PERSON>ch đơn hàng trả lại", "packageList": "<PERSON><PERSON> s<PERSON>ch gói hàng", "integration": "<PERSON><PERSON><PERSON>", "fetchEvent": "<PERSON><PERSON><PERSON> s<PERSON> kiện", "syncRecords": "<PERSON>ồng bộ dữ liệu", "channel": "<PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "logistics": "<PERSON><PERSON><PERSON> ch<PERSON>", "shippingProviderList": "<PERSON><PERSON> s<PERSON>ch nhà vận chuy<PERSON>n", "purchaseOrder": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> hàng", "purchaseOrderList": "<PERSON><PERSON> s<PERSON>ch đơn nh<PERSON><PERSON> hàng", "supplierList": "<PERSON><PERSON> s<PERSON>ch nhà cung cấp", "customers": "<PERSON><PERSON><PERSON><PERSON>", "customerDashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "customerList": "<PERSON><PERSON> s<PERSON>ch kh<PERSON>ch hàng", "customerDetail": "<PERSON> tiết kh<PERSON>ch hàng", "customerGroupList": "<PERSON><PERSON> s<PERSON>ch nhóm khách hàng", "loyaltyProgram": "<PERSON><PERSON><PERSON><PERSON> trình thành viên", "rewardProgram": "<PERSON><PERSON><PERSON><PERSON> trình tích điểm", "finance": "<PERSON><PERSON><PERSON>", "account": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "transaction": "<PERSON><PERSON><PERSON>", "inventory": "<PERSON><PERSON>", "locationList": "<PERSON><PERSON> s<PERSON>ch đị<PERSON> điểm", "inventoryList": "<PERSON><PERSON> s<PERSON>ch tồn kho", "stockAdjustmentList": "<PERSON><PERSON> s<PERSON>ch điều chỉnh kho", "stockRelocateList": "<PERSON><PERSON> s<PERSON>ch chuy<PERSON>n kho", "promotion": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "discountList": "<PERSON><PERSON> s<PERSON>ch gi<PERSON>m giá", "voucherList": "<PERSON><PERSON> s<PERSON>ch voucher", "import": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "importList": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON><PERSON> li<PERSON>u", "recordList": "<PERSON><PERSON> s<PERSON> bản ghi", "website": "Website", "blogCategory": "<PERSON><PERSON> m<PERSON><PERSON> b<PERSON>i viết", "blogList": "<PERSON><PERSON> s<PERSON>ch b<PERSON>i vi<PERSON>t", "notification": "<PERSON><PERSON><PERSON><PERSON> báo", "notificationList": "<PERSON><PERSON> s<PERSON>ch thông báo", "loyaltyApp": "Ứng dụng thành viên", "pos": "<PERSON><PERSON>", "detailFetchEvent": "<PERSON> tiết sự kiện", "supportedChannels": "<PERSON><PERSON><PERSON> hỗ trợ", "installChannel": "Cài đặt kênh mới", "terminalList": "<PERSON><PERSON> s<PERSON>ch thi<PERSON>t bị", "shiftList": "<PERSON><PERSON> s<PERSON>ch ca làm việc", "posFnB": "Bán hàng F&B", "settings": "Cài đặt", "dashboard": "<PERSON><PERSON><PERSON> đi<PERSON> k<PERSON>n", "productReport": "<PERSON><PERSON><PERSON> c<PERSON>o sản ph<PERSON>m", "productDetail": "<PERSON> tiết sản phẩm", "orderManual": "<PERSON><PERSON><PERSON><PERSON> đơn hàng", "productMapping": "<PERSON><PERSON>ng bộ sản phẩm", "productMappingDetail": "<PERSON> ti<PERSON>t Mapping", "productMappingAttribute": "Mapping th<PERSON><PERSON><PERSON> t<PERSON> sản phẩm", "staff": "Nhân viên", "staffList": "<PERSON><PERSON> s<PERSON>ch nhân viên", "department": "Phòng ban", "conversation": "<PERSON><PERSON><PERSON> tho<PERSON>i", "interact": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "knowledge": "<PERSON><PERSON><PERSON> th<PERSON>", "task": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "editStaff": "Chỉnh sửa nhân viên", "activities": "<PERSON><PERSON><PERSON> đ<PERSON>", "crm": "CRM", "opportunityDetail": "<PERSON> ti<PERSON>t c<PERSON> hội", "pipelines": "<PERSON><PERSON> tr<PERSON>nh", "subscription": "<PERSON><PERSON><PERSON> ký", "checkout": "<PERSON><PERSON> toán", "propertyAssets": "<PERSON><PERSON><PERSON> sản bất động sản", "properties": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "layoutManagement": "<PERSON><PERSON><PERSON><PERSON> lý b<PERSON> cục", "units": "Đơn vị cho thuê", "contracts": "<PERSON><PERSON><PERSON>", "tenants": "<PERSON><PERSON><PERSON><PERSON> thuê", "financial": "<PERSON><PERSON><PERSON>", "maintenance": "<PERSON><PERSON><PERSON> trì", "reports": "Báo cáo", "propertyAssetsDashboard": "<PERSON>ảng điều khiển tài sản bất động sản", "propertyDetail": "<PERSON> tiết bất động sản", "newProperty": "<PERSON><PERSON><PERSON> động sản mới", "editProperty": "Chỉnh sửa bất động sản", "unitDetail": "<PERSON> tiết đơn vị", "newUnit": "Đơn vị mới", "editUnit": "Chỉnh sửa đơn vị", "contractDetail": "<PERSON> tiết hợp đồng", "newContract": "<PERSON><PERSON><PERSON> đồng mới", "editContract": "Chỉnh sửa hợp đồng", "tenantDetail": "<PERSON> tiết ngư<PERSON>i thuê", "newTenant": "<PERSON><PERSON><PERSON><PERSON> thuê mới", "editTenant": "Chỉnh sửa người thuê", "payments": "<PERSON><PERSON> toán", "paymentDetail": "<PERSON> tiết thanh toán", "newPayment": "<PERSON><PERSON> to<PERSON> mới", "editPayment": "Chỉnh sửa thanh toán", "maintenanceDetail": "<PERSON> tiết bảo trì", "newMaintenance": "<PERSON><PERSON>o trì mới", "editMaintenance": "Chỉnh sửa bảo trì", "assetCategories": "<PERSON><PERSON> m<PERSON><PERSON> tài sản", "assetCategoryDetail": "<PERSON> tiết danh mục tài sản", "newAssetCategory": "<PERSON><PERSON> mục tài sản mới", "editAssetCategory": "Chỉnh sửa danh mục tài sản", "documentManagement": "<PERSON><PERSON><PERSON><PERSON> lý tài liệu", "documentDetail": "<PERSON> tiết tài liệu", "newDocument": "<PERSON><PERSON><PERSON> li<PERSON>u mới", "editDocument": "Chỉnh sửa tài liệu", "propertyGallery": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON>", "propertyGalleryDetail": "<PERSON> tiết thư viện", "newPropertyGallery": "<PERSON><PERSON><PERSON><PERSON> m<PERSON>i", "editPropertyGallery": "Chỉnh sửa ảnh", "propertyValuation": "<PERSON><PERSON><PERSON> giá tài sản", "propertyValuationDetail": "<PERSON> tiết định giá", "newPropertyValuation": "<PERSON><PERSON>nh giá mới", "editPropertyValuation": "Chỉnh sửa định giá"}, "product": {"image": "<PERSON><PERSON><PERSON> sản phẩm", "title": "<PERSON><PERSON><PERSON> s<PERSON>n p<PERSON>m", "description": "<PERSON><PERSON>", "price": "Giá", "sku": "Mã SKU", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "category": "<PERSON><PERSON>", "inventory": "<PERSON><PERSON><PERSON> kho", "notMapped": "<PERSON><PERSON><PERSON><PERSON> map đến đích"}, "productMapping": {"lastSynced": "<PERSON><PERSON><PERSON> bộ lần cu<PERSON>i", "errorLoading": "Lỗi tải chi tiết đồng bộ sản phẩm", "manualRetry": "<PERSON><PERSON><PERSON> lại thủ công", "cancelledMessage": "<PERSON><PERSON> hủy đồng bộ sản phẩm", "mappingStatus": "<PERSON>r<PERSON><PERSON> thái đồng bộ", "variant": "<PERSON><PERSON><PERSON><PERSON> thể"}, "groups": {"crm": "CRM", "operations": "<PERSON><PERSON><PERSON> h<PERSON>nh", "virtual_staff": "<PERSON><PERSON><PERSON> viên <PERSON>o", "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "property_assets": "<PERSON><PERSON><PERSON> sản bất động sản"}, "branch": {"Ho Chi Minh": "TP.HCM", "Ha Noi": "<PERSON><PERSON>", "Da Nang": "Đà Nẵng", "Hai Phong": "<PERSON><PERSON><PERSON>", "Can Tho": "<PERSON><PERSON><PERSON>", "Binh Duong": "<PERSON><PERSON><PERSON>", "Binh Phuoc": "Bình Phước", "All": "<PERSON><PERSON><PERSON> c<PERSON>", "title": "<PERSON> n<PERSON>h", "all": "<PERSON><PERSON><PERSON> cả chi nh<PERSON>h", "addBranch": "<PERSON><PERSON><PERSON><PERSON> chi nh<PERSON>h mới", "branch": "<PERSON> n<PERSON>h", "shortcuts": {"alt": "Alt", "plus": "+"}, "daily": "<PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON> t<PERSON>", "monthly": "<PERSON><PERSON><PERSON>g", "yearly": "<PERSON><PERSON><PERSON>", "annually": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>"}, "profile": {"free": "<PERSON><PERSON><PERSON> phí", "profile": "<PERSON><PERSON> sơ", "settings": "Cài đặt", "darkMode": "<PERSON><PERSON> độ tối", "on": "<PERSON><PERSON><PERSON>", "off": "Tắt", "language": "<PERSON><PERSON><PERSON>", "english": "<PERSON><PERSON><PERSON><PERSON>", "vietnamese": "Tiếng <PERSON>", "logout": "<PERSON><PERSON><PERSON> xu<PERSON>", "founder": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>g lập", "usedSpace": "<PERSON>ng lượng đã dùng", "upgrade": "<PERSON><PERSON><PERSON> c<PERSON>p", "message": "<PERSON>", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "staff": "Nhân viên", "storage": "<PERSON><PERSON><PERSON> tr<PERSON>"}, "auth": {"brandSection": {"title": "<PERSON><PERSON><PERSON> thử miễn phí 14 ngày"}, "logoutSuccess": "<PERSON><PERSON><PERSON> xuất thành công", "logoutError": "Lỗi đăng xuất", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "genderPlaceholder": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> t<PERSON>h", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>", "preferNotToSay": "<PERSON><PERSON><PERSON><PERSON> muốn nói", "dob": "<PERSON><PERSON><PERSON>", "dobPlaceholder": "<PERSON><PERSON><PERSON> ng<PERSON> sinh", "username": "<PERSON><PERSON><PERSON> đ<PERSON>p", "usernamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập", "login": "<PERSON><PERSON><PERSON>", "register": "<PERSON><PERSON><PERSON> ký", "forgotPasswordDescription": "Nhập email và hướng dẫn sẽ được gửi đến bạn!", "forgotPasswordTitle": "<PERSON>u<PERSON><PERSON>?", "forgotPasswordSubtitle": "Nhập email của bạn để nhận hướng dẫn đặt lại mật khẩu", "resetPassword": "Đặt lại mật khẩu", "resetPasswordTitle": "Đặt lại mật khẩu", "resetPasswordDescription": "<PERSON><PERSON><PERSON><PERSON> mã xác thực và mật khẩu mới", "resetPasswordSubtitle": "<PERSON><PERSON><PERSON><PERSON> mã xác thực và mật khẩu mới", "resetPasswordButton": "Đặt lại mật khẩu", "resetPasswordSuccess": "Đặt lại mật khẩu thành công", "resetPasswordSuccessDescription": "<PERSON><PERSON>y giờ bạn có thể đăng nhập với mật khẩu mới của bạn", "resetPasswordError": "<PERSON><PERSON><PERSON><PERSON> thể đặt lại mật khẩu", "resetPasswordLoading": "<PERSON>ang đặt lại...", "confirmPassword": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "confirmPasswordPlaceholder": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "backToLogin": "Quay lại đăng nh<PERSON>p", "backToForgotPassword": "Quay lại quên mật khẩu", "loginTitle": "<PERSON><PERSON><PERSON>", "loginSubtitle": "<PERSON>hập tên đăng nhập hoặc email để đăng nhập vào tài khoản của bạn", "email": "Email", "emailPlaceholder": "m@example", "emailPlaceholderSignUp": "Nhập email", "verifyEmail": "<PERSON><PERSON><PERSON> thực email", "verifyEmailButton": "<PERSON><PERSON><PERSON> thực email", "verifyEmailSuccess": "<PERSON><PERSON> đã đư<PERSON><PERSON> xác thực thành công", "verifyEmailError": "<PERSON><PERSON><PERSON><PERSON> thể xác thực email", "verifyEmailLoading": "<PERSON><PERSON> xác thực...", "verifyEmailCode": "<PERSON><PERSON>ậ<PERSON> mã đã đư<PERSON><PERSON> g<PERSON><PERSON> đến email củ<PERSON> bạn", "verifyEmailCodePlaceholder": "<PERSON>h<PERSON><PERSON> mã", "verifyEmailCodeButton": "<PERSON><PERSON><PERSON> thực mã", "verifyEmailCodeSuccess": "<PERSON>ã đã đư<PERSON><PERSON> xác thực thành công", "verifyEmailCodeError": "<PERSON><PERSON><PERSON><PERSON> thể xác thực mã", "verifyEmailCodeLoading": "<PERSON><PERSON> xác thực mã...", "newPassword": "<PERSON><PERSON><PERSON> mới", "newPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u mới", "verificationCode": "<PERSON><PERSON> xác thực", "verificationCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã xác thực", "verificationCodeButton": "<PERSON><PERSON><PERSON> th<PERSON>c", "verificationCodeSuccess": "<PERSON><PERSON><PERSON> thực thành công", "verificationCodeError": "<PERSON><PERSON><PERSON> thực thất bại", "verificationCodeDescription": "<PERSON><PERSON>g tôi đã gửi mã đến {{username}}. <PERSON><PERSON><PERSON><PERSON> nó bên dưới.", "sendInstructions": "<PERSON><PERSON><PERSON> h<PERSON>ng dẫn", "sending": "<PERSON><PERSON> g<PERSON>...", "resetting": "<PERSON>ang đặt lại...", "password": "<PERSON><PERSON><PERSON>", "passwordPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "rememberMe": "<PERSON><PERSON> nhớ đăng nhập", "loginButton": "<PERSON><PERSON><PERSON>", "loginWithGoogle": "Đ<PERSON>ng nhập với Google", "loginWithGithub": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>", "noAccount": "Chưa có tài k<PERSON>n?", "signUp": "<PERSON><PERSON><PERSON> ký", "signUpTitle": "<PERSON><PERSON><PERSON> ký", "signUpSubtitle": "<PERSON><PERSON><PERSON> ký để đăng nhập vào trang quản trị", "signUpButton": "<PERSON><PERSON><PERSON> ký", "signUpSuccess": "Đăng ký thành công! Vui lòng xác thực email của bạn", "signUpError": "<PERSON><PERSON><PERSON> ký thất bại", "signUpLoading": "<PERSON><PERSON> đăng ký...", "alreadyHaveAccount": "Đã có tài k<PERSON>n?", "sendNewCode": "<PERSON><PERSON><PERSON> l<PERSON>i", "resendCodeSuccess": "Đ<PERSON> gửi lại mã xác thực", "resendCodeError": "<PERSON><PERSON><PERSON><PERSON> thể gửi lại mã xác thực", "usernameOrEmail": "T<PERSON><PERSON> đ<PERSON>ng nhập hoặc email", "usernameOrEmailPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên đăng nhập hoặc email", "forgot": "<PERSON>uên?", "or": "Hoặc", "loginSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>p thành công", "loginError": "<PERSON><PERSON><PERSON> nh<PERSON>p thất bại", "loginLoading": "<PERSON><PERSON> đăng nhập...", "usernameRequired": "<PERSON><PERSON> lòng nhập tên đăng nhập", "emailRequired": "<PERSON><PERSON> lòng nhập email", "passwordRequired": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "confirmPasswordRequired": "<PERSON><PERSON> lòng xác nhận mật kh<PERSON>u", "invalidPassword": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "forgotPasswordSuccess": "Hướng dẫn đặt lại mật khẩu đã đượ<PERSON> gửi đến email của bạn", "forgotPasswordError": "<PERSON><PERSON><PERSON><PERSON> thể gửi hướng dẫn đặt lại mật khẩu", "newPasswordRequired": "<PERSON><PERSON><PERSON> kh<PERSON>u mới là bắt buộc", "passwordsDoNotMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "passwordMustBeAtLeast8Characters": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "codeRequired": "<PERSON><PERSON> lòng x<PERSON>c nhận mã", "resendCodeCountdown": "G<PERSON><PERSON> lại trong {{seconds}}s"}, "onboarding": {"step1": {"title": "Bạn biết đến OnexBots từ đâu?", "options": {"facebook": "Facebook", "zalo": "<PERSON><PERSON>", "youtube": "Youtube", "instagram": "Instagram", "tiktok": "TikTok", "google": "Google", "linkedin": "LinkedIn", "referral": "<PERSON>ua mối quan hệ", "other": "K<PERSON><PERSON><PERSON>"}, "otherPlaceholder": "<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ..."}, "step2": {"title": "Bạn đã từng sử dụng phần mềm chat <PERSON> chưa?", "options": {"never": "<PERSON><PERSON><PERSON> từng sử dụng", "tried": "Từng sử dụng", "regularly": "S<PERSON> dụng thư<PERSON><PERSON> x<PERSON>n"}}, "step3": {"title": "Bạn làm việc trong ngành nào?", "options": {"ecommerce": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại điện tử", "travel": "<PERSON>", "real_estate": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "health": "Y tế & làm đẹp", "education": "<PERSON><PERSON><PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "otherPlaceholder": "Công nghệ thông tin, Logistics, ..."}, "step3_part2": {"title": "Bạn có bao nhiêu nhân viên?", "options": {"just_me": "Chỉ có tôi", "2-9": "2-9", "10-49": "10-49", "50-199": "50-199", "200-499": "200-499", "500+": "500+"}, "inputLabel": "URL c<PERSON><PERSON> b<PERSON>n (<PERSON><PERSON><PERSON>)", "inputPlaceholder": "www.example.com"}, "step4": {"title": "Bạn có loại công ty nào?", "options": {"specialty_clinic": "Bệnh viện đa khoa", "aesthetic_clinic": "Bệnh viện thẩm mỹ/ Spa", "cosmetic_surgery": "Phẫu thuật thẩm mỹ", "nutrition_clinic": "<PERSON><PERSON>nh viện dinh dưỡng & thể thao", "telemedicine": "Y tế trực tuy<PERSON> & công nghệ y tế", "pharma": "<PERSON><PERSON><PERSON> ty dư<PERSON><PERSON> & c<PERSON><PERSON> hàng bán lẻ thẩm mỹ", "other": "K<PERSON><PERSON><PERSON>"}, "otherPlaceholder": "Trung tâm chăm sóc sức khỏe, <PERSON><PERSON><PERSON> ty công nghệ sinh học, ..."}, "step5": {"title": "Bạn cần hỗ trợ từ chuyên gia nào?", "options": {"consulting": "<PERSON><PERSON> vấn", "customer_care": "<PERSON><PERSON><PERSON> s<PERSON> k<PERSON>ch hàng", "accounting": "<PERSON><PERSON> toán", "marketing": "Marketing", "other": "K<PERSON><PERSON><PERSON>"}, "otherPlaceholder": "<PERSON><PERSON> vấn p<PERSON><PERSON>ý, Hỗ trợ <PERSON>, ..."}, "step6": {"title": "<PERSON><PERSON><PERSON> tiêu của bạn khi sử dụng OnexBots?", "options": {"feedback": "<PERSON>hu thập & ph<PERSON> tích phản hồi", "pressure": "<PERSON><PERSON><PERSON><PERSON> lực giờ cao điểm", "channels": "<PERSON>ử lý nhiều kênh cùng lúc", "quality": "<PERSON><PERSON><PERSON> thi<PERSON>n chất lư<PERSON> tương tác", "responses": "Tự động phản hồ<PERSON> n<PERSON>h", "monitor": "Huấn luy<PERSON> & gi<PERSON><PERSON> sát nhân viên", "other": "K<PERSON><PERSON><PERSON>"}, "otherPlaceholder": "<PERSON><PERSON><PERSON> lead, <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, ..."}, "buttons": {"back": "Trở về", "skip": "Bỏ qua", "continue": "<PERSON><PERSON><PERSON><PERSON>", "done": "<PERSON><PERSON><PERSON> th<PERSON>"}, "otherInput": {"pleaseSpecify": "<PERSON><PERSON> h<PERSON>y chi tiết", "optional": "(<PERSON><PERSON><PERSON>)"}}, "common": {"pickADateRange": "<PERSON><PERSON><PERSON> k<PERSON> thời gian", "other": "K<PERSON><PERSON><PERSON>", "escTo": "<PERSON><PERSON><PERSON><PERSON> để", "at": "ở", "areYouAbsolutelySure": "Bạn có chắc chắn không?", "areYouAbsolutelySureDescription": "Hành động này không thể hoàn tác. Bạn có chắc chắn muốn tiếp tục không?", "canNotDeleteStage": "<PERSON><PERSON><PERSON><PERSON> thể xóa cột này", "canNotDeleteStageDescription": "Bạn phải di chuyển tất cả cơ hội ra khỏi cột này trước khi có thể xóa.", "selectCountry": "<PERSON><PERSON><PERSON> quốc gia", "displayCustomizer": "Hi<PERSON>n thị tùy chỉnh", "customizeTheDisplayOfContentColumnsAccordingToYourPreferences": "<PERSON><PERSON><PERSON> chỉnh hiển thị cột nội dung theo sở thích của bạn.", "noDataAvailable": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "start": "<PERSON><PERSON><PERSON> đ<PERSON>u", "back": "Quay lại", "words": "từ", "confirm": "<PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON>", "totalSize": "Tổng dung lượng", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "reset": "Đặt lại", "setAsDefault": "Đặt lại mặc định", "saveFilters": "<PERSON><PERSON><PERSON> b<PERSON> lọc", "sort": "<PERSON><PERSON><PERSON>p", "view": "Xem", "add": "<PERSON><PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "saving": "<PERSON><PERSON> l<PERSON>...", "close": "Đ<PERSON><PERSON>", "clear": "Xóa", "loading": "<PERSON><PERSON> tả<PERSON>...", "loadingMore": "<PERSON><PERSON> tải thêm...", "deleting": "Đang xóa...", "toggleGrid": "Bật/tắt lưới", "snapToGrid": "Dính vào lưới", "alignHorizontally": "<PERSON><PERSON>n chỉnh ngang", "alignVertically": "<PERSON><PERSON><PERSON> chỉnh dọc", "noData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu", "success": "<PERSON><PERSON><PERSON><PERSON> công", "uploadImage": "<PERSON><PERSON><PERSON> và thả ảnh hoặc", "upload": "<PERSON><PERSON><PERSON>", "uploading": "<PERSON><PERSON> tả<PERSON>...", "fileSizeError": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> tệp phải nhỏ hơn 5MB", "uploadError": "<PERSON><PERSON><PERSON><PERSON> thể tải <PERSON>nh lên", "imageUploadError": "<PERSON><PERSON><PERSON><PERSON> thể tải ảnh nhân viên lên", "areYouSure": "Bạn có chắc chắn không?", "leaveDesc": "<PERSON><PERSON><PERSON> thay đổi chưa đ<PERSON><PERSON><PERSON> lưu sẽ bị xóa.", "deleteTaskConfirmation": "<PERSON><PERSON>nh động này không thể hoàn tác. <PERSON>ông việc này sẽ bị xóa vĩnh viễn.", "deleteProductConfirmation": "<PERSON>ành động này không thể hoàn tác. Sản phẩm này sẽ bị xóa vĩnh viễn.", "deleteListProductConfirmation": "<PERSON><PERSON><PERSON> động này không thể hoàn tác. {{count}} sản phẩm  sẽ bị xóa vĩnh viễn.", "deleteOpportunityConfirmation": "<PERSON><PERSON>nh động này không thể hoàn tác. <PERSON><PERSON> hội này sẽ bị xóa vĩnh viễn.", "install": "Cài đặt", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "deleteSuccess": "<PERSON><PERSON> x<PERSON>a sản phẩm thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa sản phẩm", "deleteSuccessDescription": "<PERSON><PERSON><PERSON> phẩm đã được xóa thành công", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>", "bulkActions": "<PERSON><PERSON><PERSON> động hàng lo<PERSON>t", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "select": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON>ã<PERSON>", "issues": "<PERSON><PERSON><PERSON> đề", "status": {"available": "<PERSON><PERSON> sẵn", "occupied": "<PERSON><PERSON> thuê", "maintenance": "<PERSON><PERSON><PERSON> trì", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "healthy": "<PERSON><PERSON><PERSON>", "issues": "<PERSON><PERSON> vấn đề", "issue": "<PERSON><PERSON><PERSON> đề", "complete": "<PERSON><PERSON><PERSON> th<PERSON>", "inProgress": "<PERSON><PERSON> ti<PERSON>n hành", "processing": "<PERSON><PERSON> lý", "error": "Lỗi", "ready": "Sẵn sàng", "pending": "Chờ xử lý", "success": "<PERSON><PERSON><PERSON><PERSON> công", "online": "<PERSON><PERSON><PERSON><PERSON>", "offline": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>"}, "error": {"title": "Lỗi", "unknown": "<PERSON><PERSON> xảy ra lỗi không xác định"}, "saveChanges": "<PERSON><PERSON><PERSON> thay đổi", "unsavedChanges": "<PERSON><PERSON> đ<PERSON>i chưa đ<PERSON><PERSON><PERSON> l<PERSON>u", "unsavedChangesDescription": "Bạn có thay đổi chưa đ<PERSON><PERSON><PERSON> lư<PERSON>. Bạn có chắc chắn muốn đóng không?", "discard": "Bỏ qua", "keepEditing": "<PERSON><PERSON><PERSON><PERSON> tục chỉnh sửa", "week": "<PERSON><PERSON><PERSON>", "month": "<PERSON><PERSON><PERSON><PERSON>", "quarter": "<PERSON><PERSON><PERSON>", "year": "Năm", "units": "đơn vị", "leaveWithoutSavingDescription": "Bạn có thay đổi chưa đ<PERSON><PERSON><PERSON> l<PERSON>. Bạn có chắc chắn muốn thoát không?", "leaveWithoutSaving": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> l<PERSON>u", "leave": "<PERSON><PERSON><PERSON><PERSON>", "stay": "Ở lại", "knowledgeUpdated": "<PERSON><PERSON><PERSON> thức đã đư<PERSON><PERSON> cập nhật thành công", "knowledgeDeleted": "<PERSON><PERSON><PERSON> thức đã được xóa thành công", "areYouSureDescription": "Bạn có chắc chắn muốn xóa kiến thức này không?", "staffUpdated": "<PERSON><PERSON><PERSON> viên đã đư<PERSON><PERSON> cập nhật thành công", "updateStaffError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật nhân viên", "areYouSureConfirm": "<PERSON><PERSON><PERSON>", "areYouSureCancel": "Hủy bỏ", "updateAttribute": "<PERSON><PERSON><PERSON> n<PERSON><PERSON><PERSON> th<PERSON><PERSON>h", "time": {"month": "<PERSON><PERSON><PERSON><PERSON>", "timeAgo": {"seconds": "{{count}} g<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON>", "seconds_plural": "{{count}} g<PERSON><PERSON><PERSON> tr<PERSON><PERSON><PERSON>", "minutes": "{{count}} ph<PERSON><PERSON> tr<PERSON><PERSON>c", "minutes_plural": "{{count}} ph<PERSON><PERSON> tr<PERSON><PERSON>c", "hours": "{{count}} g<PERSON><PERSON> trước", "hours_plural": "{{count}} g<PERSON><PERSON> trước", "days": "{{count}} ng<PERSON><PERSON> tr<PERSON>", "days_plural": "{{count}} ng<PERSON><PERSON> tr<PERSON>", "months": "{{count}} th<PERSON>g tr<PERSON>", "months_plural": "{{count}} th<PERSON>g tr<PERSON>", "years": "{{count}} năm tr<PERSON>c", "years_plural": "{{count}} năm tr<PERSON>c", "invalidDate": "<PERSON><PERSON><PERSON> h<PERSON> l<PERSON>"}}, "empty": {"title": "<PERSON><PERSON><PERSON>ng có gì ở đây!", "description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả phù hợp."}, "create": "Tạo", "noFileSelected": "<PERSON><PERSON><PERSON> ch<PERSON> file", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công", "fileTooLarge": "File vượt quá dung lượng tối đa {{max}}MB", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "name": "<PERSON><PERSON><PERSON>", "loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>", "markAsDone": "<PERSON><PERSON><PERSON> th<PERSON>", "zoomIn": "<PERSON><PERSON><PERSON> to", "zoomOut": "Phóng nhỏ", "fitToScreen": "<PERSON><PERSON><PERSON> màn hình", "backToOverview": "Quay lại tổng quan", "progress": "<PERSON><PERSON><PERSON><PERSON> độ", "opacity": "<PERSON><PERSON> mờ", "visible": "<PERSON><PERSON><PERSON> thị", "properties": "<PERSON><PERSON><PERSON><PERSON>", "duplicate": "<PERSON><PERSON><PERSON> b<PERSON>n", "quickActions": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "imageLoadError": "<PERSON><PERSON><PERSON><PERSON> thể tải hình <PERSON>nh", "uploadNew": "<PERSON><PERSON><PERSON> lên mới", "viewMode": "<PERSON><PERSON> độ xem", "editMode": "<PERSON><PERSON> độ chỉnh sửa", "total": "Tổng"}, "tenants": {"title": "<PERSON><PERSON><PERSON><PERSON> thuê", "addTenant": "<PERSON><PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "editTenant": "Chỉnh sửa người thuê", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "tenantDetail": "<PERSON> tiết ngư<PERSON>i thuê", "tenantDetails": "<PERSON> tiết ngư<PERSON>i thuê", "tenantId": "<PERSON>ã người thuê", "tenantName": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "sections": {"personalInfo": "Thông tin cá nhân", "contactInfo": "<PERSON>h<PERSON>ng tin liên hệ", "emergencyContact": "<PERSON><PERSON><PERSON> h<PERSON> khẩn cấp", "leaseInfo": "Thông tin thuê", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "paymentInfo": "Thông tin thanh toán", "notes": "<PERSON><PERSON><PERSON>", "identification": "<PERSON><PERSON><PERSON><PERSON> tờ tùy thân", "employment": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "fields": {"firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "fullName": "<PERSON><PERSON> tên", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "address": "Địa chỉ", "dateOfBirth": "<PERSON><PERSON><PERSON>", "occupation": "<PERSON><PERSON><PERSON>", "emergencyContactName": "<PERSON><PERSON><PERSON> liên hệ khẩn cấp", "emergencyContactPhone": "SĐT li<PERSON>n hệ khẩn cấp", "relationship": "<PERSON><PERSON><PERSON> quan hệ", "leaseStart": "<PERSON><PERSON><PERSON> b<PERSON>t đầu thuê", "leaseEnd": "<PERSON><PERSON><PERSON> kết thúc thuê", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> thuê", "securityDeposit": "Tiền đặt cọc", "unit": "Đơn vị", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "notes": "<PERSON><PERSON><PERSON>", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "identificationType": "<PERSON><PERSON><PERSON> gi<PERSON>y tờ", "identificationNumber": "Số gi<PERSON>y tờ", "employmentStatus": "<PERSON><PERSON><PERSON> trạng công việc", "employerName": "<PERSON><PERSON>n công ty", "monthlyIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> hàng tháng"}, "placeholders": {"enterFirstName": "<PERSON><PERSON><PERSON><PERSON> tên", "enterLastName": "<PERSON><PERSON><PERSON><PERSON>", "enterEmail": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email", "enterPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enterOccupation": "<PERSON><PERSON><PERSON><PERSON> ngh<PERSON> nghiệp", "selectUnit": "<PERSON><PERSON>n đơn vị", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "enterNotes": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "firstName": "<PERSON><PERSON><PERSON><PERSON> tên", "lastName": "<PERSON><PERSON><PERSON><PERSON>", "email": "Enter email address", "phone": "Enter phone number", "identificationType": "Select identification type", "identificationNumber": "Enter identification number", "employmentStatus": "Select employment status", "employerName": "Enter employer name", "monthlyIncome": "Enter monthly income", "emergencyContactName": "Enter emergency contact name", "emergencyContactPhone": "Enter emergency contact phone"}, "status": {"active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "pending": "Chờ xử lý", "terminated": "<PERSON><PERSON> chấm d<PERSON>"}, "actions": {"save": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "Chỉnh sửa", "delete": "Xóa", "view": "<PERSON>em chi tiết"}, "messages": {"saveSuccess": "<PERSON><PERSON><PERSON> ng<PERSON>i thuê thành công", "saveError": "Lỗi khi lưu người thuê", "deleteSuccess": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê thành công", "deleteError": "Lỗi khi xóa người thuê", "deleteConfirm": "Bạn có chắc chắn muốn xóa người thuê này?", "required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "createSuccess": "Tenant created successfully", "updateSuccess": "Tenant updated successfully", "createError": "Failed to create tenant", "updateError": "Failed to update tenant"}, "contracts": {"title": "<PERSON><PERSON><PERSON>", "contract": "<PERSON><PERSON><PERSON>", "contractId": "<PERSON><PERSON> hợp đồng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "monthlyRent": "<PERSON><PERSON><PERSON><PERSON> thuê hàng tháng", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "viewContract": "<PERSON><PERSON> đ<PERSON>"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON> kê nhanh", "activeContracts": "<PERSON><PERSON><PERSON> đồng hoạt động", "documents": "<PERSON><PERSON><PERSON> l<PERSON>", "stats": {"totalContracts": "<PERSON><PERSON>ng số hợp đồng", "activeContracts": "<PERSON><PERSON><PERSON> đồng đang hoạt động", "joinDate": "<PERSON><PERSON><PERSON> gia nh<PERSON>p"}, "employmentStatus": {"employed": "<PERSON><PERSON> vi<PERSON><PERSON> làm", "unemployed": "<PERSON><PERSON><PERSON><PERSON>", "self_employed": "Tự kinh doanh", "student": "Sin<PERSON> viên", "retired": "Retired"}, "identificationTypes": {"passport": "<PERSON><PERSON> ch<PERSON>", "national_id": "CMND/CCCD", "driver_license": "Bằng lái xe"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người thuê", "notFoundDescription": "<PERSON>ười thuê mà bạn đang tìm không tồn tại hoặc đã bị xóa."}, "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "description": "Bạn có chắc chắn muốn xóa người thuê này? Hành động này không thể hoàn tác.", "hasActiveContracts": "<PERSON><PERSON><PERSON>i thuê này có hợp đồng đang hoạt động. Bạn có chắc chắn muốn xóa?"}, "createTenant": "<PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON><PERSON> thông tin chi tiết để tạo người thuê mới", "editDescription": "Update the tenant information"}, "maintenance": {"form": {"requestId": "<PERSON><PERSON> yêu c<PERSON>u", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "priority": "<PERSON><PERSON> <PERSON> tiên", "category": "<PERSON><PERSON>", "reportedDate": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "description": "<PERSON><PERSON>", "estimatedCost": "Chi phí <PERSON><PERSON> t<PERSON>h", "contractorName": "<PERSON><PERSON><PERSON> nhà thầu", "contractorPhone": "SĐT nhà thầu", "notes": "<PERSON><PERSON><PERSON>", "actualCost": "<PERSON> phí thực tế"}, "details": {"overview": "<PERSON><PERSON><PERSON> quan", "timeline": "<PERSON><PERSON><PERSON> thời gian", "daysSinceReported": "<PERSON><PERSON><PERSON> kể từ khi báo cáo", "cost": "Chi phí", "related": "<PERSON><PERSON><PERSON> quan", "contractor": "<PERSON><PERSON><PERSON> thầu"}, "status": {"in_progress": "<PERSON><PERSON> ti<PERSON>n hành", "pending": "Chờ xử lý", "completed": "<PERSON><PERSON><PERSON> th<PERSON>", "cancelled": "<PERSON><PERSON> hủy", "open": "Mở"}, "priority": {"low": "<PERSON><PERSON><PERSON><PERSON>", "medium": "<PERSON>rung bình", "high": "<PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>p"}, "category": {"plumbing": "<PERSON><PERSON> thống n<PERSON>", "electrical": "<PERSON><PERSON><PERSON><PERSON>", "hvac": "<PERSON><PERSON><PERSON><PERSON> hòa thông gió", "structural": "<PERSON><PERSON><PERSON> c<PERSON>", "appliance": "<PERSON><PERSON><PERSON><PERSON> bị", "cosmetic": "<PERSON><PERSON><PERSON><PERSON> mỹ", "cleaning": "<PERSON><PERSON>", "security": "An ninh", "general": "<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> y<PERSON>u cầu bảo trì thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa yêu cầu bảo trì", "statusUpdateSuccess": "<PERSON><PERSON><PERSON> nhật trạng thái thành công", "statusUpdateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON><PERSON><PERSON> cầu bảo trì bạn đang tìm không tồn tại hoặc đã bị xóa."}, "dialog": {"deleteTitle": "<PERSON><PERSON><PERSON>", "deleteDescription": "Bạn có chắc chắn muốn xóa yêu cầu bảo trì này? Hành động này không thể hoàn tác."}}, "shapes": {"rectangle": "<PERSON><PERSON><PERSON> chữ nhật", "circle": "<PERSON><PERSON><PERSON> tròn", "polygon": "<PERSON><PERSON>"}, "pages": {"checkout": {"title": "<PERSON><PERSON> toán", "orderInformation": {"title": "<PERSON><PERSON><PERSON><PERSON> tin đơn hàng", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "subscribeTo": "Đăng ký gói", "registrationTime": "<PERSON>h<PERSON><PERSON> gian đ<PERSON>ng ký", "price": "Giá", "creationDate": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "issueInvoice": "<PERSON><PERSON><PERSON> h<PERSON>a đơn", "companyName": "<PERSON><PERSON>n công ty", "taxId": "<PERSON><PERSON> số thuế", "address": "Địa chỉ", "email": "<PERSON>ail công ty", "vat": "VAT (8%)", "total": "<PERSON><PERSON><PERSON> tiền", "pay": "<PERSON><PERSON> toán", "companyNamePlaceholder": "<PERSON>í dụ: <PERSON><PERSON><PERSON> ty <PERSON>", "taxIdPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã số thuế", "addressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "emailPlaceholder": "Ví dụ: <EMAIL>"}, "qrPayment": {"title": "Mã QR để chuyển k<PERSON>n", "accountName": "<PERSON><PERSON><PERSON> tà<PERSON>", "accountNumber": "Số tài <PERSON>n", "bankName": "<PERSON><PERSON> h<PERSON>", "amount": "<PERSON><PERSON> tiền", "content": "<PERSON><PERSON>i dung", "cancelOrder": "<PERSON><PERSON><PERSON> đơn hàng", "success": "<PERSON>h toán thành công !", "redirectingIn": "Chuyển hướng đến trang đăng ký trong {{countdown}} giây..."}}, "opportunities": {"deleteSuccess": "<PERSON><PERSON> x<PERSON>a cơ hội thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa c<PERSON> hội", "title": "<PERSON><PERSON> hội", "add": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON> k<PERSON>...", "addOpportunity": "<PERSON><PERSON> hội mới", "contact": "<PERSON><PERSON><PERSON><PERSON>", "salesperson": "Ngườ<PERSON> phụ trách", "expectedClosing": "<PERSON><PERSON><PERSON> đ<PERSON>g dự kiến", "tags": "Thẻ", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "enterContactName": "<PERSON><PERSON><PERSON><PERSON> tên kh<PERSON>ch hàng...", "enterEmailAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ email...", "enterTags": "<PERSON><PERSON><PERSON>p thẻ...", "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại...", "opportunityDetail": "<PERSON> ti<PERSON>t c<PERSON> hội", "enterOpportunityTitle": "<PERSON><PERSON><PERSON><PERSON> tên c<PERSON> hội...", "enterProbability": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>...", "expectedRevenue": "<PERSON><PERSON><PERSON> thu dự kiến", "probability": "<PERSON><PERSON><PERSON>", "customerInfo": "Thông tin khách hàng", "salesPerson": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> h<PERSON>ng", "notes": "<PERSON><PERSON><PERSON>", "enterRevenue": "<PERSON><PERSON><PERSON><PERSON> doanh thu...", "selectAssignee": "<PERSON><PERSON><PERSON> ng<PERSON> phụ trách", "enterExpectedClosingDate": "<PERSON><PERSON><PERSON> ngày đóng dự kiến...", "status": {"won": "Đạt", "lost": "<PERSON><PERSON><PERSON>", "ongoing": "<PERSON><PERSON> ra"}}, "stages": {"egDiscuss": "Ví dụ: <PERSON><PERSON><PERSON><PERSON>", "createOpportunity": "<PERSON><PERSON><PERSON> c<PERSON> hội", "opportunityNamePlaceholder": "Ví dụ: <PERSON><PERSON><PERSON> cho bệnh viện", "contactPlaceholder": "Ví dụ: <PERSON><PERSON><PERSON><PERSON>", "editColumn": "<PERSON><PERSON><PERSON> c<PERSON>", "columnName": "<PERSON><PERSON><PERSON>", "isWonStage": "<PERSON>à cột thắng?", "deleted": "<PERSON><PERSON><PERSON> cột thành công", "added": "<PERSON><PERSON><PERSON><PERSON> cột thành công", "failedToCreateStage": "<PERSON><PERSON><PERSON><PERSON> thể tạo cột", "yesterday": "<PERSON><PERSON><PERSON> qua", "overdue": "<PERSON><PERSON><PERSON> h<PERSON>n", "today": "<PERSON><PERSON><PERSON> nay", "upcoming": "<PERSON><PERSON><PERSON> t<PERSON>i", "noDueDate": "<PERSON><PERSON><PERSON><PERSON> có hạn", "inDays": "Trong {{count}} ngày", "tomorrow": "<PERSON><PERSON><PERSON> mai", "daysAgo": "{{count}} ng<PERSON><PERSON> tr<PERSON>", "filters": {"createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "outcome": "<PERSON><PERSON><PERSON> qu<PERSON>"}, "newColumn": "<PERSON><PERSON><PERSON> mới", "newColumnPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cột và nhấn Enter", "unassigned": "Chưa phân công", "editActivity": "<PERSON><PERSON><PERSON> ho<PERSON>t động", "fold": "<PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "view": "Xem", "addColumnAfter": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>t sau", "addColumnBefore": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>t trước", "deleteColumn": "<PERSON><PERSON><PERSON> c<PERSON>", "search": "<PERSON><PERSON><PERSON> k<PERSON>...", "title": "<PERSON><PERSON> tr<PERSON>nh", "add": "<PERSON><PERSON><PERSON><PERSON>", "addOpportunity": "<PERSON><PERSON> hội mới", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "opportunity": "<PERSON><PERSON> hội", "contact": "<PERSON><PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "expectedRevenue": "<PERSON><PERSON><PERSON> thu dự kiến", "priority": "<PERSON><PERSON> <PERSON> tiên", "assignee": "Ngườ<PERSON> phụ trách", "selectAssignee": "<PERSON><PERSON><PERSON> ng<PERSON> phụ trách", "noScheduledActivities": "<PERSON><PERSON><PERSON><PERSON> có hoạt động nào", "noActivitiesForStatus": "<PERSON><PERSON><PERSON><PERSON> có hoạt động nào cho {{status}}", "scheduleActivity": "<PERSON><PERSON><PERSON> l<PERSON><PERSON> ho<PERSON> động", "activityType": "<PERSON><PERSON><PERSON> ho<PERSON>t động", "selectActivityType": "<PERSON><PERSON><PERSON> lo<PERSON>i hoạt động", "searchActivityTypes": "T<PERSON>m kiếm loại hoạt động...", "dueDate": "<PERSON><PERSON><PERSON> h<PERSON> hạn", "summary": "<PERSON><PERSON><PERSON>", "notes": "<PERSON><PERSON><PERSON>", "typeSomething": "<PERSON><PERSON><PERSON><PERSON> gì đó...", "searchAssignee": "T<PERSON><PERSON> ng<PERSON> phụ trách...", "noAssigneeFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người phụ trách.", "pickADate": "<PERSON><PERSON><PERSON>", "schedule": "<PERSON><PERSON><PERSON>", "actionName": "<PERSON><PERSON><PERSON> hành động", "newActivityType": "Loại hoạt động mới", "salesperson": "Ngườ<PERSON> phụ trách", "expectedClosing": "<PERSON><PERSON><PERSON> đ<PERSON>g dự kiến", "tags": "Thẻ", "headers": {"contact": "<PERSON><PERSON><PERSON><PERSON>", "email": "Email", "opportunity": "<PERSON><PERSON> hội", "stage": "<PERSON><PERSON><PERSON><PERSON> thái", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "assignee": "Ngườ<PERSON> phụ trách", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "customer": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Số lượng", "closeDate": "<PERSON><PERSON><PERSON>", "probability": "<PERSON><PERSON><PERSON>", "expectedClosingDate": "<PERSON><PERSON><PERSON> đ<PERSON>g dự kiến", "expectedRevenue": "<PERSON><PERSON><PERSON> thu dự kiến", "activities": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "sort": {"title": "<PERSON><PERSON><PERSON> xếp theo", "order": "<PERSON><PERSON><PERSON> tự", "closingDate": "<PERSON><PERSON><PERSON>", "dealValue": "<PERSON><PERSON><PERSON> trị giao dịch", "created": "<PERSON><PERSON><PERSON>", "lastActivity": "<PERSON><PERSON><PERSON> động cu<PERSON>i", "winProbability": "<PERSON><PERSON><PERSON> thắng", "rating": "Đánh giá", "lowestToHighest": "<PERSON><PERSON><PERSON><PERSON> đến cao", "highestToLowest": "<PERSON> đến thấp"}}, "onexbotsDashboard": {"title": "Bảng điều khiển OnexBots", "filters": {"period": "<PERSON><PERSON><PERSON><PERSON> gian", "daily": "<PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON> t<PERSON>", "monthly": "<PERSON><PERSON><PERSON>g", "yearly": "<PERSON><PERSON><PERSON>", "staff": "Nhân viên"}, "stats": {"conversations": "<PERSON><PERSON><PERSON><PERSON> trò ch<PERSON>", "users": "<PERSON><PERSON><PERSON><PERSON> dùng", "accuracyRate": "<PERSON><PERSON> ch<PERSON>h x<PERSON>c", "averageResponseTime": "<PERSON>h<PERSON><PERSON> gian phản hồi trung bình", "viewMore": "<PERSON><PERSON>", "fromLastPeriod": "so v<PERSON><PERSON> kỳ trước", "noComparisonData": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu so sánh"}, "accuracyRateChart": {"title": "Tỷ lệ ch<PERSON>h xác", "description": "<PERSON><PERSON> chính xác của các câu trả lời đượ<PERSON> so sánh với cơ sở kiến thức", "tooltip": {"rate": "<PERSON><PERSON> ch<PERSON>h x<PERSON>c", "resolved": "<PERSON><PERSON> gi<PERSON>i quy<PERSON>", "total": "Tổng số câu hỏi"}}, "responseTimeChart": {"title": "<PERSON>h<PERSON><PERSON> gian phản hồi trung bình", "description": "Thời gian trung bình để một câu trả lời được gửi đi", "fastest": "<PERSON><PERSON><PERSON>", "slowest": "<PERSON><PERSON><PERSON> n<PERSON>", "tooltip": {"average": "<PERSON>rung bình", "min": "<PERSON><PERSON><PERSON> thi<PERSON>u", "max": "<PERSON><PERSON><PERSON> đa"}}}, "onboarding": {"welcomeTo": "<PERSON><PERSON>o mừng đến với", "skip": "Bỏ qua", "done": "<PERSON><PERSON><PERSON> th<PERSON>"}, "overview": {"title": "<PERSON><PERSON><PERSON> quan", "filters": {"period": "<PERSON><PERSON><PERSON><PERSON> gian", "daily": "<PERSON><PERSON><PERSON>", "weekly": "<PERSON><PERSON><PERSON> t<PERSON>", "monthly": "<PERSON><PERSON><PERSON>g", "yearly": "<PERSON><PERSON><PERSON>", "selectLocation": "<PERSON><PERSON><PERSON> cơ sở", "refresh": "<PERSON><PERSON><PERSON>"}, "stats": {"totalFacilities": "Tổng số cơ sở", "totalPatients": "<PERSON><PERSON>ng số bệnh nhân", "averageOccupancy": "Tỷ lệ lấp đầy", "totalRevenue": "<PERSON><PERSON>ng doanh thu", "viewMore": "<PERSON><PERSON>", "fromLastMonth": "so với tháng tr<PERSON>c"}, "patientStats": {"title": "<PERSON><PERSON><PERSON><PERSON> kê b<PERSON>nh nhân", "outpatient": "Ngoại trú", "inpatient": "Nội trú"}, "topTwenty": {"title": "Top 20", "icdDiagnoses": "<PERSON>ẩn đo<PERSON> ICD", "prescribedMedications": "<PERSON><PERSON><PERSON><PERSON> kê đơn"}, "costs": {"averageTreatmentCosts": "<PERSON> phí điều trị trung bình", "insurancePayments": "<PERSON><PERSON> <PERSON><PERSON> b<PERSON>o <PERSON>", "insurance": "<PERSON><PERSON><PERSON>", "service": "<PERSON><PERSON><PERSON> v<PERSON>", "specialCare": "<PERSON><PERSON><PERSON> s<PERSON>c đặc biệt"}, "treatmentOutcomes": {"title": "<PERSON><PERSON><PERSON> quả điều trị", "recovered": "<PERSON><PERSON><PERSON>", "improved": "<PERSON><PERSON><PERSON>", "unchanged": "<PERSON><PERSON><PERSON><PERSON> thay đổi", "deteriorated": "<PERSON><PERSON><PERSON> đi", "deceased": "<PERSON><PERSON> vong", "left": "<PERSON><PERSON><PERSON> đi"}}, "customers": {"title": "<PERSON><PERSON> s<PERSON>ch kh<PERSON>ch hàng", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm theo tên"}, "group": "Nhóm"}, "name": "<PERSON><PERSON><PERSON>", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "address": "Địa chỉ", "group": "Nhóm", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}, "orders": {"filters": {"shift": "<PERSON>a làm việc", "status": "<PERSON><PERSON><PERSON><PERSON> thái đơn hàng", "paymentStatus": "<PERSON>r<PERSON><PERSON> thái thanh toán", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}, "orderHistory": "<PERSON><PERSON><PERSON> sử đơn hàng", "amount": "Số lượng", "redeemPoints": "<PERSON><PERSON><PERSON><PERSON> thưởng", "loyalPoints": "<PERSON><PERSON><PERSON><PERSON> thưởng", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "title": "<PERSON><PERSON> s<PERSON>ch đơn hàng", "searchPriceGroup": "<PERSON><PERSON>m kiếm nhóm giá", "noPriceGroupsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nhóm giá", "searchBranch": "<PERSON><PERSON><PERSON> kiếm chi nh<PERSON>h", "noBranchFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chi nh<PERSON>h", "emptyServiceName": "<PERSON><PERSON> lòng điền đ<PERSON>y đủ tên dịch vụ", "updateOrder": "<PERSON><PERSON><PERSON>", "selectGender": "<PERSON><PERSON><PERSON> g<PERSON><PERSON> t<PERSON>h", "tax": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON> vận chuy<PERSON>n", "addCustomer": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> h<PERSON>ng", "save": "<PERSON><PERSON><PERSON>", "defaultShippingAddress": "Địa chỉ giao hàng mặc định", "defaultBillingAddress": "Đ<PERSON><PERSON> chỉ thanh toán mặc định", "noAddressesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy địa chỉ", "edit": "Chỉnh sửa", "removeRecipientInfo": "<PERSON><PERSON><PERSON> thông tin ng<PERSON>ời nhận", "addRecipientInfo": "<PERSON><PERSON><PERSON><PERSON> thông tin người nhận", "enterName": "<PERSON><PERSON><PERSON><PERSON> tên", "enterAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "enterPhoneNumber": "<PERSON><PERSON><PERSON><PERSON> số điện thoại", "enterCompanyName": "<PERSON><PERSON><PERSON><PERSON> tên công ty", "selectWard": "<PERSON><PERSON><PERSON> xã", "searchWard": "<PERSON><PERSON><PERSON> kiếm xã", "searchDistrict": "<PERSON><PERSON><PERSON> k<PERSON> h<PERSON>", "selectDistrict": "<PERSON><PERSON><PERSON>", "selectProvince": "<PERSON><PERSON><PERSON> tỉnh", "searchProvince": "<PERSON><PERSON><PERSON> kiếm tỉnh", "province": "Tỉnh", "district": "<PERSON><PERSON><PERSON><PERSON>", "ward": "Xã", "addAddress": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "address": "Địa chỉ", "noProvincesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tỉnh", "noDistrictsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy huy<PERSON>n", "noWardsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy xã", "shippingDefault": "Địa chỉ giao hàng mặc định", "billingDefault": "Đ<PERSON><PERSON> chỉ thanh toán mặc định", "editCustomer": "Chỉnh sửa kh<PERSON>ch hàng", "Name": "<PERSON><PERSON><PERSON>", "phoneNumber": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "email": "Email", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "enterEmail": "Nhập email", "birthday": "<PERSON><PERSON><PERSON>", "pickADate": "<PERSON><PERSON><PERSON>", "customerGroup": "<PERSON><PERSON>ó<PERSON> kh<PERSON>ch hàng", "selectCustomerGroup": "<PERSON><PERSON><PERSON> nh<PERSON>m kh<PERSON>ch hàng", "companyName": "<PERSON><PERSON>n công ty", "addresses": "Địa chỉ", "submit": "<PERSON><PERSON><PERSON>", "accumulatedPoints": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch <PERSON>", "groupName": "Nhóm", "placeholder": "<PERSON><PERSON><PERSON> kiếm đơn hàng...", "quantity": "Số lượng", "price": "Giá", "total": "Tổng", "noProductsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy sản phẩm", "addService": "<PERSON><PERSON><PERSON><PERSON> (F9)", "loadingMore": "<PERSON><PERSON> tải thêm...", "addProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "available": "<PERSON><PERSON> sẵn", "onHand": "Trong kho", "note": "<PERSON><PERSON><PERSON>", "maximumAvailableQuantity": "<PERSON><PERSON> lượng tối đa có sẵn", "branch": "<PERSON> n<PERSON>h", "loadingCustomerDetails": "<PERSON><PERSON> tải chi tiết kh<PERSON>ch hàng...", "customer": "<PERSON><PERSON><PERSON><PERSON>", "shippingAddress": "Đ<PERSON>a chỉ giao hàng", "billingAddress": "<PERSON><PERSON><PERSON> chỉ thanh toán", "noCustomersFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy khách hàng", "loading": "<PERSON><PERSON> tả<PERSON>...", "searchCustomer": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>m kh<PERSON>ch hàng", "payment": "<PERSON><PERSON> toán", "addPromotion": "<PERSON><PERSON><PERSON><PERSON> mãi", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "subtotal": "<PERSON><PERSON><PERSON> tiền", "discount": "G<PERSON>ảm giá", "voucher": "Mã giảm giá", "fees": "<PERSON><PERSON> vụ", "promotions": "<PERSON><PERSON><PERSON><PERSON><PERSON> mãi", "notePlaceholder": "<PERSON><PERSON><PERSON><PERSON>hi <PERSON>ú", "tags": "Thẻ", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thẻ", "noProductsInOrder": "<PERSON><PERSON><PERSON><PERSON> có sản phẩm trong đơn hàng.", "cancel": "<PERSON><PERSON><PERSON>", "addOrder": "<PERSON><PERSON><PERSON><PERSON> đơn hàng", "success": "<PERSON><PERSON>n hàng đã đư<PERSON><PERSON> tạo thành công!", "error": "Đ<PERSON><PERSON> hàng không thể đư<PERSON>c xử lý", "adjustPrice": "Điều chỉnh giá", "adjustPriceSuccess": "Giá đã được điều chỉnh thành công!", "adjustPriceError": "<PERSON>h<PERSON>ng thể điều chỉnh giá", "adjustPriceDescription": "Điều chỉnh giá của sản phẩm đã chọn", "adjustPricePlaceholder": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> mới", "adjustPriceButton": "Điều chỉnh giá", "adjustPriceCancel": "<PERSON><PERSON><PERSON>", "setNewPrice": "Đặt giá mới", "value": "<PERSON><PERSON><PERSON> trị", "percent": "<PERSON><PERSON><PERSON> tr<PERSON>m", "addProductToOrderWarning": "<PERSON><PERSON> lòng thêm sản phẩm vào đơn hàng", "selectCustomer": "<PERSON><PERSON> lòng chọn kh<PERSON>ch hàng", "addVoucher": "Thêm mã giảm giá", "voucherCode": "Mã giảm giá", "voucherCodePlaceholder": "<PERSON>hậ<PERSON> mã giảm giá", "voucherCodeButton": "Thêm mã giảm giá", "voucherCodeSuccess": "<PERSON>ã giảm giá đã đư<PERSON><PERSON> thêm thành công", "voucherCodeError": "<PERSON><PERSON><PERSON><PERSON> thể thêm mã giảm giá", "confirm": "Bạn có chắc chắn không?", "confirmCancel": "<PERSON><PERSON><PERSON>", "confirmDelete": "Xóa", "cancelWarning": "Hành động này không thể hoàn tác. Hủy thay đổi?", "cancelDelete": "Hành động này không thể hoàn tác. <PERSON><PERSON><PERSON> mục này?"}, "variants": {"title": "<PERSON><PERSON><PERSON><PERSON> thể", "filters": {"search": {"placeholder": "<PERSON><PERSON>m kiếm theo tên, mã, mã vạch"}}}, "products": {"title": "<PERSON><PERSON><PERSON>", "addBulk": {"title": "<PERSON><PERSON><PERSON><PERSON>", "notice": "<PERSON><PERSON><PERSON> ý:", "templateInstructions": "<PERSON><PERSON> l<PERSON>h<PERSON> file theo mẫu để tránh sai lệch dữ liệu.", "simpleTemplate": "<PERSON><PERSON><PERSON> xuống mẫu đơn giản của chúng tôi", "advancedTemplate": "<PERSON><PERSON><PERSON> xuống mẫu nâng cao của chúng tôi", "here": "tại đây", "supportedFiles": "Định dạng file được hỗ trợ: .xlsx, .xls, .csv", "dragAndDrop": "Kéo và thả file vào đây hoặc", "uploadButton": "<PERSON><PERSON><PERSON>", "fileUpload": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "import": "<PERSON><PERSON><PERSON><PERSON>", "importing": "<PERSON><PERSON> nhập...", "selectFileError": "<PERSON><PERSON> lòng chọn file để tải lên", "importSuccess": "<PERSON><PERSON><PERSON> phẩm đã đư<PERSON>c nhập thành công", "importError": "<PERSON><PERSON> xảy ra lỗi khi nhập sản phẩm"}, "filters": {"search": {"placeholder": "<PERSON><PERSON>m kiếm theo tên, mã, mã vạch", "placeholderBrand": "<PERSON><PERSON><PERSON> kiếm thương hiệu..."}, "product": "<PERSON><PERSON><PERSON> p<PERSON>m", "source": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>", "otherFilters": {"title": "Bộ lọ<PERSON> kh<PERSON>c", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "description": "Hai bộ lọc đầu tiên sẽ được ưu tiên hiển thị trên trang chính. Tùy chỉnh chúng dựa trên nhu cầu của bạn."}, "dateOptions": {"allTime": "<PERSON><PERSON><PERSON> cả thời gian", "today": "<PERSON><PERSON><PERSON> nay", "yesterday": "<PERSON><PERSON><PERSON> qua", "lastWeek": "<PERSON><PERSON><PERSON> tr<PERSON>", "thisWeek": "<PERSON><PERSON><PERSON>", "lastMonth": "<PERSON><PERSON><PERSON><PERSON>", "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "customize": "<PERSON><PERSON><PERSON> chỉnh"}, "deletionFailed": "<PERSON><PERSON><PERSON> biến thể thất bại", "deletedSuccessfully": "<PERSON><PERSON><PERSON> biến thể thành công"}, "headers": {"productInfo": "Thông tin sản phẩm", "category": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "createdAt": "Tạo lúc", "available": "Số lượng", "variant": "<PERSON><PERSON><PERSON><PERSON> thể"}, "actions": {"addProduct": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "addManual": {"title": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m", "onThisPage": "<PERSON><PERSON><PERSON>", "sections": {"basicInfo": "Thông Tin Cơ Bản", "options": "<PERSON><PERSON><PERSON>", "units": "Đơn <PERSON>", "prices": "<PERSON><PERSON><PERSON>", "measurements": "<PERSON><PERSON><PERSON>"}}, "addQuick": "<PERSON><PERSON><PERSON><PERSON> sản ph<PERSON>m n<PERSON>h", "addBulk": "<PERSON><PERSON><PERSON><PERSON> sản phẩm hàng lo<PERSON>t", "refresh": "<PERSON><PERSON><PERSON>", "saveFilters": "<PERSON><PERSON><PERSON> b<PERSON> lọc", "reset": "Đặt lại", "filter": "<PERSON><PERSON><PERSON>"}, "deletionFailed": "<PERSON><PERSON><PERSON> biến thể thất bại", "deletedSuccessfully": "<PERSON><PERSON><PERSON> biến thể thành công", "descriptionDeleteOption": "<PERSON><PERSON><PERSON><PERSON> thể hoàn tác hành động này. <PERSON><PERSON> liệu liên quan đến Đơn vị gói, <PERSON><PERSON><PERSON> sản phẩm và K<PERSON>ch thước sẽ bị xóa vĩnh viễn.", "descriptionDeleteValueOption": "<PERSON><PERSON><PERSON><PERSON> thể hoàn tác hành động này. <PERSON><PERSON> liệu liên quan đến Giá sản phẩm và Kích thước sẽ bị xóa vĩnh viễn.", "name": "<PERSON><PERSON><PERSON>", "sku": "Mã SKU", "barcode": "Mã vạch", "option1": "Tùy chọn 1", "option2": "T<PERSON>y <PERSON> 2", "option3": "Tùy chọ<PERSON> 3", "unit": "Đơn vị", "weight": "Cân nặng", "height": "<PERSON><PERSON><PERSON> cao", "width": "<PERSON><PERSON><PERSON> r<PERSON>", "length": "<PERSON><PERSON><PERSON> dài", "variantDetails": "<PERSON> tiết biến thể", "variants": "<PERSON><PERSON><PERSON><PERSON> thể", "source": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON> hi<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "viewLess": "<PERSON><PERSON>", "viewMore": "<PERSON><PERSON>", "noPricesAvailable": "Không có giá", "prices": "Giá", "tags": "Thẻ", "inventory": {"noMatchResult": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết quả", "title": "<PERSON><PERSON>", "branch": "<PERSON> n<PERSON>h", "history": "<PERSON><PERSON><PERSON> s<PERSON>", "allBranches": "<PERSON><PERSON><PERSON> cả chi nh<PERSON>h", "inventory": "<PERSON><PERSON>", "packing": "<PERSON><PERSON><PERSON>", "shipping": "<PERSON><PERSON><PERSON> ch<PERSON>", "minValue": "<PERSON><PERSON><PERSON> trị tối thiểu", "maxValue": "<PERSON><PERSON><PERSON> trị tối đa", "staff": "Nhân viên", "transactionType": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "change": "<PERSON><PERSON> đ<PERSON>i", "quantity": "Số lượng", "reference": "<PERSON><PERSON>", "available": "<PERSON><PERSON> sẵn", "incoming": "Đang vào", "onHand": "Trong kho"}, "addManual": {"title": "<PERSON><PERSON><PERSON><PERSON>", "onThisPage": "<PERSON><PERSON><PERSON>", "publish": "<PERSON><PERSON><PERSON>", "sections": {"addVariant": "<PERSON><PERSON><PERSON> biến thể khi có nhiều lựa chọn, chẳng hạn như kích cỡ hoặc màu sắc.", "variant": "<PERSON><PERSON><PERSON><PERSON> thể", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "apply": "<PERSON><PERSON>", "variantPlaceholder": "<PERSON><PERSON><PERSON> biến thể", "usedByAllVariants": " <PERSON><PERSON><PERSON><PERSON> sử dụng bởi tất cả các biến thể", "usedByThis": " <PERSON><PERSON><PERSON><PERSON> sử dụng bởi biến thể này", "unitPlaceholder": "<PERSON><PERSON>n đơn vị", "unitSearchPlaceholder": "<PERSON><PERSON><PERSON> ki<PERSON>m đơn vị", "variantSearchPlaceholder": "<PERSON><PERSON><PERSON> kiến biến thể", "unitEmptyText": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đơn vị", "addType": "<PERSON><PERSON><PERSON><PERSON>", "addUnit": "<PERSON><PERSON><PERSON><PERSON> đơn vị", "basicInfo": "Thông Tin Cơ Bản", "options": "<PERSON><PERSON><PERSON>", "option": "t<PERSON><PERSON>n", "units": "Đơn <PERSON>", "prices": "<PERSON><PERSON><PERSON>", "measurements": "<PERSON><PERSON><PERSON>", "selectImages": "<PERSON><PERSON><PERSON>", "submit": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "stay": "Ở lại", "leave": "<PERSON><PERSON><PERSON> đi", "values": "<PERSON><PERSON><PERSON> trị", "valuesPlaceholder": "<PERSON><PERSON><PERSON> tr<PERSON> 1, <PERSON><PERSON><PERSON>r<PERSON> 2, <PERSON><PERSON><PERSON> trị 3", "optionsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên tùy chọn", "addOption": "<PERSON><PERSON><PERSON><PERSON> tù<PERSON> ch<PERSON>n", "optionName": "<PERSON><PERSON><PERSON> t<PERSON> ch<PERSON>n", "addValue": "<PERSON>h<PERSON><PERSON> giá trị", "remove": "Xóa", "valuesPlaceholderInput": "<PERSON><PERSON><PERSON><PERSON> giá trị", "duplicateValue": "<PERSON><PERSON>á trị này đã tồn tại", "createVariant": "<PERSON><PERSON><PERSON> biến thể có nhiều lựa chọn, chẳng hạn như kích cỡ hoặc màu sắc."}, "basicInfo": {"brandPlaceholder": "<PERSON><PERSON><PERSON> th<PERSON> hi<PERSON>u", "brandSearchPlaceholder": "<PERSON><PERSON><PERSON> ki<PERSON>m thư<PERSON> hi<PERSON>u", "brandEmptyText": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thư<PERSON>ng hiệu", "categoryPlaceholder": "<PERSON><PERSON><PERSON> danh mục", "categorySearchPlaceholder": "<PERSON><PERSON><PERSON> ki<PERSON>m danh mục", "categoryEmptyText": "<PERSON><PERSON><PERSON><PERSON> tìm thấy danh mục", "tagsPlaceholder": "<PERSON><PERSON><PERSON><PERSON> thẻ", "images": "<PERSON><PERSON><PERSON> Ảnh", "name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "shortDescription": "<PERSON><PERSON>", "brand": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "sku": "Mã SKU", "tags": "Thẻ", "price": "Giá", "uploadImage": "<PERSON><PERSON><PERSON>n", "optimize": "<PERSON><PERSON><PERSON>", "required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "imageRequired": "<PERSON><PERSON> n<PERSON>t có một <PERSON>nh", "nameRequired": "<PERSON><PERSON><PERSON> sản phẩm là bắt buộc", "nameWarning": "<PERSON><PERSON><PERSON> sản phẩm là bắt buộc để tối ưu hóa", "descriptionWarning": "<PERSON><PERSON> tả là bắt buộc để tối ưu hóa", "skuRequired": "Mã SKU là bắt buộc", "priceRequired": "<PERSON><PERSON><PERSON> là b<PERSON> bu<PERSON>c"}, "options": {"addOption": "<PERSON><PERSON><PERSON><PERSON> tù<PERSON> ch<PERSON>n", "optionName": "<PERSON><PERSON><PERSON> t<PERSON> ch<PERSON>n", "values": "<PERSON><PERSON><PERSON> trị", "addValue": "<PERSON>h<PERSON><PERSON> giá trị", "remove": "Xóa"}, "units": {"title": "Đơn <PERSON>", "addUnit": "<PERSON><PERSON><PERSON><PERSON> đơn vị", "unitName": "<PERSON><PERSON><PERSON> đơn vị", "ratio": "Tỷ lệ", "remove": "Xóa"}, "prices": {"title": "<PERSON><PERSON><PERSON>", "addGroup": "Thêm nhóm giá mới", "groupName": "<PERSON><PERSON><PERSON>", "price": "Giá", "apply": "<PERSON><PERSON>", "applyAll": "<PERSON><PERSON> dụng tất cả"}, "measurements": {"weight": "Cân nặng", "height": "<PERSON><PERSON><PERSON> cao", "width": "<PERSON><PERSON><PERSON> r<PERSON>", "length": "<PERSON><PERSON><PERSON> dài", "apply": "<PERSON><PERSON>", "applyAll": "<PERSON><PERSON> dụng tất cả"}, "buttons": {"cancel": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "stay": "Ở lại", "leave": "<PERSON><PERSON><PERSON> đi"}, "dialogs": {"leaveTitle": "Bạn có chắc muốn rời đi?", "leaveDesc": "<PERSON><PERSON><PERSON> thay đổi chưa lưu sẽ bị mất."}, "validation": {"hasErrors": "Lỗi xác thực", "checkFields": "<PERSON><PERSON> lòng kiểm tra tất cả các trườ<PERSON> bắt buộc và thử lại"}, "success": "<PERSON><PERSON> tạo sản phẩm", "successUpdate": "<PERSON><PERSON> cập nh<PERSON>t sản phẩm", "successDescription": "<PERSON><PERSON><PERSON> phẩm đã đư<PERSON>c tạo thành công", "successDescriptionUpdate": "<PERSON><PERSON><PERSON> nh<PERSON>t sản phẩm thành công", "error": "Lỗi", "errorDescription": "<PERSON><PERSON><PERSON><PERSON> thể tạo sản phẩm. <PERSON><PERSON> lòng thử lại.", "errorDescriptionUpdate": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật sản phẩm. <PERSON><PERSON> lòng thử lại"}}, "choosePlan": {"forIndividuals": "Cá nhân", "forCompanies": "<PERSON><PERSON>ng ty", "monthly": "<PERSON><PERSON><PERSON><PERSON>", "annually": "Năm", "chooseAPlan": "<PERSON><PERSON><PERSON>", "planDescription": {"firstSection": "<PERSON><PERSON><PERSON> gói phù hợp nhất với nhu cầu kinh doanh của bạn.", "middleSection": "<PERSON><PERSON><PERSON> có thể nâng cấp hoặc hạ cấp sau này.", "secondSection": "T<PERSON><PERSON> cả các gói đều bao gồm các t<PERSON>h năng c<PERSON> bản."}, "planData": {"Up to 50 variants": "<PERSON><PERSON><PERSON> đến 50 biến thể, thống kê tổng quan", "Real-time inventory syncing": "<PERSON><PERSON>ng bộ tồn kho thời gian thực", "Ideal for startups (1,000 items)": "<PERSON><PERSON> tưởng cho các doanh nghiệp mới thành lập (1.000 sản phẩm)", "Analytics dashboard": "<PERSON><PERSON>ng điều khiển phân tích", "User-friendly interface": "<PERSON><PERSON><PERSON>n thân thiện với người dùng", "Support for multiple currencies and languages": "Hỗ trợ nhiều loại tiền tệ và ngôn ngữ", "Real time inventory": "<PERSON><PERSON><PERSON><PERSON> lý tồn kho thời gian thực"}, "planNames": {"Free": "<PERSON><PERSON><PERSON> phí", "Starter": "<PERSON><PERSON> bản", "Pro": "<PERSON><PERSON><PERSON><PERSON>", "Agency": "<PERSON><PERSON><PERSON>"}, "mostPopular": "<PERSON><PERSON>", "numberIntegrations": "<PERSON><PERSON> l<PERSON><PERSON> tích hợp", "explainNoIntegrations": "<PERSON><PERSON><PERSON><PERSON> có tích hợp nào", "getStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u"}, "synchronization": {"platforms": {"source": "<PERSON><PERSON><PERSON><PERSON>", "destination": "<PERSON><PERSON><PERSON>"}, "title": {"success": "<PERSON><PERSON><PERSON> {{source}} với {{destination}}", "error": "<PERSON><PERSON><PERSON> bộ hóa cấu hình"}, "description": "<PERSON>ọn một lĩnh vực phù hợp với sở thích và đối tượng mục tiêu của bạn.", "error": {"missingConnection": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết n<PERSON>i", "connectionError": "Lỗi kết nối", "sourceNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy nguồn", "destinationNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy đích"}, "success": {"completeTitle": "<PERSON><PERSON>t n<PERSON>i hoàn tất!", "gotoDashboard": "<PERSON><PERSON> đến bảng điều khiển"}, "syncSetting": {"title": "Cài đặt đồng bộ", "product": {"title": "<PERSON><PERSON><PERSON> p<PERSON>m", "description": "<PERSON><PERSON><PERSON> bộ sản phẩm từ {{source}} sang {{destination}}"}, "inventory": {"title": "<PERSON><PERSON><PERSON> tồn kho", "description": "<PERSON><PERSON><PERSON> mức tồn kho đ<PERSON><PERSON><PERSON> đồng bộ"}, "order": {"title": "<PERSON><PERSON><PERSON> hàng", "description": "<PERSON><PERSON><PERSON><PERSON> đơn hàng {{destination}} vào {{source}}"}, "buttonTitle": "<PERSON><PERSON><PERSON> n<PERSON>i với {{destination}}"}}, "syncRecords": {"title": "<PERSON><PERSON> s<PERSON>ch đồng bộ", "filters": {"search": {"placeholder": "T<PERSON><PERSON> kiếm đơn hàng trả lại..."}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "recordType": "<PERSON><PERSON><PERSON> bản <PERSON>hi", "channel": "<PERSON><PERSON><PERSON>", "connectionId": "ID Kế<PERSON> n<PERSON>", "fetchEventId": "ID Sự kiện lấy dữ liệu"}, "columns": {"channel": "<PERSON><PERSON><PERSON>", "header": "<PERSON><PERSON><PERSON> bản <PERSON>hi", "fetchEventId": "ID Sự kiện lấy dữ liệu", "connectionId": "ID Kế<PERSON> n<PERSON>", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "fetchedAt": "<PERSON><PERSON><PERSON> dữ liệu lúc", "finishedAt": "<PERSON><PERSON><PERSON> tất lúc", "publishedAt": "<PERSON><PERSON><PERSON> b<PERSON>", "transformedAt": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>i lúc"}}, "fetchEvents": {"title": "<PERSON><PERSON> kiện <PERSON>tch", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm sự kiện Fetch..."}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actionType": "<PERSON><PERSON><PERSON> hành động", "actionGroup": "<PERSON><PERSON><PERSON><PERSON> hành động", "eventTime": "<PERSON><PERSON><PERSON><PERSON> gian sự kiện", "eventSource": "<PERSON><PERSON><PERSON><PERSON> sự kiện", "fetchEventId": "ID sự kiện <PERSON>tch"}, "columns": {"channel": "<PERSON><PERSON><PERSON>", "header": "<PERSON><PERSON><PERSON> bản <PERSON>hi", "fetchEventId": "ID sự kiện <PERSON>tch", "connectionId": "<PERSON> kết n<PERSON>i", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "fetchedAt": "<PERSON><PERSON><PERSON> dữ liệu lúc", "finishedAt": "<PERSON><PERSON><PERSON> thành l<PERSON>c", "publishedAt": "<PERSON><PERSON><PERSON> b<PERSON>", "transformedAt": "<PERSON>y<PERSON><PERSON> đ<PERSON>i lúc"}, "headers": {"channel": "<PERSON><PERSON><PERSON>", "actionType": "<PERSON><PERSON><PERSON> hành động", "actionGroup": "<PERSON><PERSON><PERSON><PERSON> hành động", "eventSource": "<PERSON><PERSON><PERSON><PERSON>", "eventTime": "<PERSON><PERSON><PERSON><PERSON> gian sự kiện", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}}, "fetchEventDetail": {"title": "<PERSON> tiết sự kiện Fetch của", "actionGroup": "<PERSON><PERSON><PERSON><PERSON> hành động", "connectionId": "ID Kế<PERSON> n<PERSON>", "actionType": "<PERSON><PERSON><PERSON> hành động", "eventSource": "<PERSON><PERSON><PERSON><PERSON> sự kiện", "retryCount": "<PERSON><PERSON> lần thử lại", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "continuationToken": "<PERSON><PERSON> tiếp tục", "objectId": "<PERSON> đối t<PERSON>", "eventTime": "<PERSON><PERSON><PERSON><PERSON> gian sự kiện", "createdAt": "Tạo lúc", "updatedAt": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "eventNumber": "{{number}}. <PERSON><PERSON><PERSON><PERSON> có ID"}, "channel": {"title": "<PERSON><PERSON> s<PERSON>ch kết n<PERSON>i", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm kết n<PERSON>i..."}, "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "headers": {"channel": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "url": "URL", "createdAt": "<PERSON><PERSON><PERSON>", "lastUpdated": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "actions": {"install": "Cài đặt kênh mới", "configure": "<PERSON><PERSON><PERSON> h<PERSON>nh", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON> hi<PERSON> h<PERSON>a"}}, "supportedChannels": {"title": "<PERSON><PERSON> s<PERSON> k<PERSON>nh", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm kênh..."}}}, "installChannel": {"title": "Cài đặt kênh"}, "settings": {"themeSetting": "Cài đặt chủ đề", "saveSuccess": "<PERSON><PERSON><PERSON> sắc đã đ<PERSON><PERSON><PERSON> lưu", "saveError": "<PERSON><PERSON><PERSON><PERSON> thể lưu màu sắc", "colorSetting": "Cài đặt màu sắc", "lightMode": "<PERSON><PERSON> độ sáng", "darkMode": "<PERSON><PERSON> độ tối", "logoSetting": "Cài đặt logo", "logo": {"lightModeLogo": "Logo chế độ sáng", "darkModeLogo": "Logo chế độ tối", "lightModeIcon": "<PERSON>con chế độ sáng", "darkModeIcon": "<PERSON>con chế độ tối", "favicon": "Favicon", "lightModeLogoDescription": "<PERSON><PERSON><PERSON> lên logo cho chế độ sáng (k<PERSON><PERSON> thư<PERSON><PERSON> khu<PERSON>n nghị: 180x40px)", "darkModeLogoDescription": "<PERSON><PERSON>i lên logo cho chế độ tối (k<PERSON><PERSON> thư<PERSON><PERSON> k<PERSON>n nghị: 180x40px)", "lightModeIconDescription": "Tải lên icon cho chế độ sáng (k<PERSON><PERSON> thư<PERSON><PERSON> k<PERSON>n nghị: 40x40px)", "darkModeIconDescription": "Tải lên icon cho chế độ tối (k<PERSON><PERSON> thư<PERSON><PERSON> k<PERSON>n nghị: 40x40px)", "faviconDescription": "T<PERSON>i lên favicon cho website (k<PERSON><PERSON> th<PERSON><PERSON><PERSON> k<PERSON>n nghị: 32x32px)", "saveSuccess": "Logo đã đ<PERSON><PERSON><PERSON> l<PERSON>u", "saveError": "<PERSON><PERSON><PERSON>ng thể lưu logo", "noChangesToSave": "<PERSON><PERSON><PERSON><PERSON> có thay đổi để lưu", "resetSuccess": "Logo đã được đặt lại", "resetError": "Không thể đặt lại logo"}, "color": {"saveSuccess": "<PERSON><PERSON><PERSON> chủ đề đã đư<PERSON><PERSON> lưu", "saveError": "<PERSON><PERSON><PERSON><PERSON> thể lưu màu chủ đề", "resetSuccess": "<PERSON><PERSON>u chủ đề đã được đặt lại", "resetError": "<PERSON><PERSON><PERSON>ng thể đặt lại màu chủ đề"}}, "productMappingList": {"syncSuccess": "<PERSON><PERSON><PERSON><PERSON> yêu cầu đồng bộ sản phẩm thành công", "syncFail": "<PERSON><PERSON><PERSON><PERSON> yêu cầu đồng bộ sản phẩm thất bại", "noConnection": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kết n<PERSON>i", "title": "Đồng bộ <PERSON> phẩm", "description": "Đồng bộ sản phẩm của bạn từ Shopify đến Tiktok Shop", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON>"}, "status": {"all": "<PERSON><PERSON><PERSON> cả sản phẩm", "synced": "<PERSON><PERSON> đồng bộ", "mapped": "Đã map", "unMapped": "Chưa map", "errors": "Lỗi"}}, "alert": {"title": "<PERSON><PERSON><PERSON> nhận đồng bộ sản phẩm?", "description": "Bạn đang muốn đồng bộ tất cả sản phẩm giữa hai nền tảng. Qu<PERSON> trình này có thể mất một khoảng thời gian để hoàn tất.", "note": "<PERSON><PERSON><PERSON> ý:", "noteDescription": "<PERSON><PERSON><PERSON> bạn chỉ muốn đồng bộ một số sản phẩm, vui lòng chọn chúng từ bảng trước khi tiếp tục.", "confirm": "<PERSON><PERSON><PERSON> bộ", "cancel": "<PERSON><PERSON><PERSON>", "areYouSure": "Bạn có chắc chắn muốn tiếp tục?", "unmapSuccess": "Gỡ đồng bộ sản phẩm thành công", "unmapFail": "Gỡ đồng bộ sản phẩm thất bại"}, "groupButton": {"settingButton": "Cài đặt", "syncButton": "<PERSON><PERSON>ng bộ sản phẩm"}, "status": {"synced": "<PERSON><PERSON> đồng bộ", "mapped": "Đã map", "unmapped": "Chưa map", "error": "Lỗi"}, "actions": {"unmap": "Bỏ map", "map": "Map", "fix": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "headers": {"product": "<PERSON><PERSON><PERSON> phẩm {{product}}", "price": "Giá", "last_synced": "<PERSON><PERSON><PERSON> cu<PERSON>i đồng bộ", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "actions": "<PERSON><PERSON><PERSON>"}, "nomap": "Chưa map đến {{destination}}"}, "productMapping": {"advancedMapping": {"title": "Mapping nâng cao", "description": "<PERSON><PERSON><PERSON> hình các quy tắc mapping nâng cao cho sản phẩm của bạn.", "sourceField": "<PERSON><PERSON><PERSON><PERSON><PERSON> ngu<PERSON>n", "transformationType": "<PERSON><PERSON><PERSON> chuyển đổi", "addTransformation": "<PERSON><PERSON><PERSON><PERSON> chuyển đổi", "removeTransformation": "<PERSON><PERSON><PERSON> chuyển đổi", "ruleConfiguration": "<PERSON><PERSON><PERSON> hình quy tắc", "outputPreview": "<PERSON><PERSON> tr<PERSON><PERSON><PERSON> đ<PERSON>u ra", "finalOutput": "<PERSON><PERSON><PERSON> ra cuối cùng", "applyTransformations": "<PERSON><PERSON> dụng chuyển đổi", "transformationChain": "Chuỗi chuyển đổi", "sampleData": "<PERSON><PERSON> liệu mẫu", "preview": "<PERSON><PERSON>", "output": "<PERSON><PERSON><PERSON> ra", "singleValue": "<PERSON><PERSON><PERSON> trị đơn", "transformationForm": "<PERSON><PERSON><PERSON><PERSON> đổi", "exampleUsage": "Ví dụ sử dụng", "selectFieldsPlaceholder": "<PERSON><PERSON><PERSON> một trường", "searchFieldsPlaceholder": "T<PERSON><PERSON> kiếm trường...", "source": "<PERSON><PERSON><PERSON><PERSON>", "searchTransformationTypes": "T<PERSON>m kiếm loại chuyển đổi...", "selectTransformationTypes": "<PERSON><PERSON><PERSON> loại chuyển đổi..."}, "lastSynced": "<PERSON><PERSON><PERSON> bộ lần cu<PERSON>i", "errorLoading": "Lỗi tải chi tiết đồng bộ sản phẩm", "manualRetry": "<PERSON><PERSON><PERSON> lại thủ công", "cancelledMessage": "<PERSON><PERSON> hủy đồng bộ sản phẩm", "mappingStatus": "<PERSON>r<PERSON><PERSON> thái đồng bộ"}, "staff": {"title": "<PERSON><PERSON> s<PERSON>ch nhân viên", "filters": {"department": "Phòng ban", "role": "<PERSON>ai trò", "search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm nhân viên..."}}, "actionButton": {"create": "Tạo nhân viên"}, "columns": {"staff": "Nhân viên", "role": "<PERSON>ai trò", "skills": "<PERSON><PERSON> n<PERSON>ng", "task": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "conversations": "<PERSON><PERSON><PERSON> tho<PERSON>i", "actions": "<PERSON><PERSON><PERSON> đ<PERSON>"}, "actions": {"view": "Xem", "edit": "<PERSON><PERSON><PERSON>", "delete": "Xóa"}, "maxCharactersReached": "<PERSON><PERSON> đạt số ký tự tối đa", "online": "<PERSON><PERSON><PERSON><PERSON>", "noStaff": "<PERSON><PERSON><PERSON><PERSON> có nhân viên nào.", "loading": "<PERSON><PERSON> tả<PERSON>...", "interact": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "createStaff": "Tạo nhân viên", "staffName": "<PERSON><PERSON><PERSON> nhân viên", "enterStaffName": "<PERSON><PERSON><PERSON><PERSON> tên nhân viên", "staffNameRequired": "<PERSON><PERSON> lòng nhập tên nhân viên", "maxCharacters": "Tối đa 250 ký tự", "selectDepartment": "<PERSON><PERSON><PERSON> phòng ban", "searchDepartments": "<PERSON><PERSON>m kiếm phòng ban...", "selectRole": "<PERSON><PERSON><PERSON> vai trò", "searchRoles": "T<PERSON>m kiếm vai trò...", "creating": "<PERSON><PERSON> t<PERSON>o", "update": "<PERSON><PERSON><PERSON>", "role": "<PERSON>ai trò", "department": "Phòng ban", "expertise": "<PERSON><PERSON><PERSON><PERSON> môn", "knowledgeWarning": "<PERSON><PERSON><PERSON> thức hiện tại có thể quá hạn chế để trả lời chính xác. <PERSON>ui lòng xem xét thêm chi tiết để cải thiện hiệu suất.", "score": "<PERSON><PERSON><PERSON><PERSON>", "avatar": {"title": "Ảnh đại diện", "xbotAvatar": "Ảnh đại diện XBot", "image": "<PERSON><PERSON><PERSON>", "selectedAvatar": "Ảnh đại diện đã chọn", "avatar": "Ảnh đại diện"}, "knowledge": {"tab": "<PERSON><PERSON><PERSON> th<PERSON>", "baby": "Sơ khai", "warning": "<PERSON><PERSON><PERSON> thức hiện tại có thể quá hạn chế để trả lời chính xác. <PERSON><PERSON> lòng bổ sung thêm thông tin để cải thiện hiệu suất."}, "interactionStyle": {"tab": "<PERSON><PERSON> cách tư<PERSON> tác", "description": "<PERSON><PERSON><PERSON> hình cách nhân viên tương tác với người dùng", "communicationTone": "<PERSON><PERSON><PERSON><PERSON> đi<PERSON>u giao ti<PERSON>", "languagePreferences": "<PERSON><PERSON><PERSON> ch<PERSON>n ngôn ngữ", "responseLength": "<PERSON><PERSON> dài phản hồi", "personalityTraits": "Đặc điểm t<PERSON>h c<PERSON>ch", "temper": "Temper", "formal": "<PERSON><PERSON> trọng", "casual": "<PERSON><PERSON><PERSON> m<PERSON>t", "detailed": "<PERSON> ti<PERSON>", "concise": "<PERSON><PERSON><PERSON>", "creative": "<PERSON><PERSON><PERSON>", "analytical": "<PERSON><PERSON> tích", "ethicalConstraints": "<PERSON><PERSON><PERSON> bu<PERSON><PERSON> đ<PERSON><PERSON> đức", "contentFiltering": "<PERSON><PERSON><PERSON> l<PERSON> nội dung và kiểm tra an toàn", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hướng dẫn tùy chỉnh cho nhân viên này (kh<PERSON><PERSON> b<PERSON>t buộc)"}, "skills": {"tab": "<PERSON><PERSON><PERSON> hình truy cập dữ liệu", "description": "<PERSON><PERSON><PERSON> hình các nguồn dữ liệu mà nhân viên có thể truy cập", "products": "<PERSON><PERSON><PERSON> p<PERSON>m", "orders": "<PERSON><PERSON><PERSON> hàng", "inventory": "<PERSON><PERSON>"}, "staffInfo": {"tab": "Thông tin nhân viên", "description": "<PERSON><PERSON><PERSON><PERSON> lý thông tin cơ bản của nhân viên"}, "task": {"tab": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý công việc và nhiệm vụ của nhân viên", "noTasks": "<PERSON><PERSON><PERSON><PERSON> có công việc nào"}, "editStaff": {"tabs": {"staffInfo": "Thông tin nhân viên", "interactionStyle": "<PERSON><PERSON> cách tư<PERSON> tác", "knowledge": "<PERSON><PERSON><PERSON> th<PERSON>", "skills": "<PERSON><PERSON> n<PERSON>ng", "task": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>"}, "staffInfo": "Thông tin nhân viên", "interactionStyle": "<PERSON><PERSON> cách tư<PERSON> tác", "knowledge": "<PERSON><PERSON><PERSON> th<PERSON>", "skills": "<PERSON><PERSON> n<PERSON>ng", "task": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON>", "integration": "<PERSON><PERSON><PERSON>", "embedCodeInstructions": "<PERSON> h<PERSON> dẫn sau đây để nhúng widget nhân viên ảo vào trang web của bạn", "embedCodeTitle": "<PERSON><PERSON><PERSON><PERSON> widget nhân viên <PERSON>o", "title": "<PERSON><PERSON><PERSON> nhân viên", "staffName": "<PERSON><PERSON><PERSON> nhân viên", "rolePurpose": "<PERSON><PERSON> trò/<PERSON><PERSON>c tiêu", "department": "Phòng ban", "domainExpertise": "<PERSON><PERSON><PERSON><PERSON> môn", "customExpertisePlaceholder": "<PERSON><PERSON><PERSON><PERSON> chuyên môn tùy chỉnh và nhấn Enter", "noRoleFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy vai trò", "noDepartmentFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy phòng ban", "selectRole": "<PERSON><PERSON><PERSON> vai trò", "selectDepartment": "<PERSON><PERSON><PERSON> phòng ban", "searchRoles": "T<PERSON>m kiếm vai trò...", "searchDepartments": "<PERSON><PERSON>m kiếm phòng ban...", "namePhoneRequirement": "<PERSON><PERSON><PERSON>, số điện thoại yêu cầu", "roles": {"contentWriter": "<PERSON><PERSON><PERSON> văn nội dung", "seoSpecialist": "Chuyên gia SEO", "socialMediaManager": "<PERSON><PERSON><PERSON><PERSON> lý trang mạng xã hội"}, "embedVirtualStaffWidget": "<PERSON><PERSON><PERSON><PERSON> widget nhân viên <PERSON>o", "themesColor": "<PERSON><PERSON><PERSON> chủ đề", "embedCode": "<PERSON><PERSON> n<PERSON>g", "greeting": "OneXBots xin chào"}, "embed": {"instructions": {"title": "Hướng dẫn nhúng", "step1Title": "Bước 1: Thê<PERSON> mã script", "step1Description": "Sao chép thẻ script và dán vào HTML của trang web của bạn, tốt nhất là trong phần <head> hoặc ngay trước thẻ đóng </body>.", "step2Title": "Bước 2: Thêm container widget", "step2Description": "<PERSON>o ch<PERSON>p phần tử div và dán vào nơi bạn muốn widget nhân viên ảo xuất hiện trên trang của bạn. Widget sẽ tự động khởi tạo ở vị trí này.", "step3Title": "Bước 3: <PERSON><PERSON><PERSON> chỉnh widget (<PERSON><PERSON><PERSON>)", "step3Description": "Bạn có thể tùy chỉnh giao diện của widget bằng cách thêm CSS vào trang web của bạn. Container widget có ID là xbot-container.", "step4Title": "Bước 4: <PERSON><PERSON><PERSON> tra tích hợp", "step4Description": "<PERSON><PERSON> <PERSON>hi thêm mã, làm mới trang của bạn và xác minh rằng widget nhân viên ảo xuất hiện chính xác. Widget nên hiển thị thông tin của nhân viên và cho phép người truy cập tương tác với họ.", "troubleshootingTitle": "<PERSON>ử lý sự cố", "troubleshooting1": "<PERSON><PERSON><PERSON> b<PERSON>o URL script có thể truy cập đ<PERSON><PERSON><PERSON> từ trang web của bạn.", "troubleshooting2": "<PERSON><PERSON><PERSON> tra bảng điều khiển của trình duyệt để xem thông báo lỗi.", "troubleshooting3": "<PERSON><PERSON><PERSON> minh rằng ID và tên nhân viên là chính xác.", "troubleshooting4": "<PERSON><PERSON><PERSON> bảo trang web của bạn cho phép tải các script bên ngoài."}, "script": {"title": "<PERSON><PERSON> n<PERSON>g", "copy": "Sao chép", "copied": "Đã sao chép!", "containerInstructions": "1. Thêm container n<PERSON><PERSON> vào n<PERSON>i bạn muốn widget xuất hiện", "scriptInstructions": "2. <PERSON><PERSON><PERSON><PERSON> đoạn mã này vào HTML của bạn"}}, "promptExperiment": {"title": "<PERSON><PERSON><PERSON> Prompt", "datasetName": "<PERSON><PERSON><PERSON> bộ dữ liệu", "datasetNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên bộ dữ liệu", "datasetNameRequired": "<PERSON><PERSON>n bộ dữ liệu là bắt buộc", "runName": "<PERSON><PERSON><PERSON>", "runNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên ch<PERSON>y", "runNameRequired": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON> là b<PERSON> bu<PERSON>c", "cancel": "<PERSON><PERSON><PERSON>", "runExperiment": "<PERSON><PERSON><PERSON> thử nghi<PERSON>m", "success": "<PERSON><PERSON><PERSON> nghiệm prompt đ<PERSON> chạy thành công", "error": "<PERSON><PERSON><PERSON><PERSON> thể chạy thử nghiệm prompt"}}, "department": {"title": "Phòng ban", "createDepartment": "<PERSON><PERSON><PERSON> phòng ban", "createStaff": "Tạo nhân viên", "departmentName": "<PERSON>ên phòng ban", "enterDepartmentName": "<PERSON><PERSON><PERSON><PERSON> tên phòng ban", "description": "<PERSON><PERSON>", "enterDescription": "<PERSON><PERSON><PERSON><PERSON> mô tả...", "departmentNameRequired": "<PERSON><PERSON> lòng nhập tên phòng ban", "viewStaff": "<PERSON><PERSON> viên", "staffCount": "{{count}} nh<PERSON> viên", "interact": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>c", "upload": "<PERSON><PERSON><PERSON>", "deleteKnowledge": "<PERSON><PERSON><PERSON>c", "deleteKnowledgeDescription": "Bạn có chắc chắn muốn xóa kiến thức này? Hành động này không thể hoàn tác.", "deleteKnowledgeSuccess": "<PERSON><PERSON><PERSON> thức đã được xóa thành công", "deleteKnowledgeError": "<PERSON><PERSON><PERSON><PERSON> thể xóa kiến thức", "deleteKnowledgeConfirm": "Xóa", "deleteKnowledgeCancel": "<PERSON><PERSON><PERSON>", "knowledgeWarning": "<PERSON><PERSON><PERSON> thức hiện tại có thể quá hạn chế để trả lời chính xác. <PERSON>ui lòng xem xét thêm chi tiết để cải thiện hiệu suất.", "knowledge": {"tab": "<PERSON><PERSON><PERSON> th<PERSON>", "baby": "Sơ khai", "warning": "<PERSON><PERSON><PERSON> thức hiện tại có thể quá hạn chế để trả lời chính xác. <PERSON><PERSON> lòng bổ sung thêm thông tin để cải thiện hiệu suất.", "status": {"error": "Lỗi", "pending": "<PERSON><PERSON> lý", "success": "<PERSON><PERSON><PERSON><PERSON> công"}}}, "knowledge": {"title": "<PERSON><PERSON><PERSON> th<PERSON>", "headers": {"file": "File", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "size": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> lần <PERSON>i"}, "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> k<PERSON> file..."}, "fileType": "Loại file", "status": "<PERSON><PERSON><PERSON><PERSON> thái"}, "actions": {"upload": "<PERSON><PERSON><PERSON> lên file mới"}, "upload": {"dragAndDrop": "Kéo thả file vào đây hoặc", "uploadButton": "<PERSON><PERSON><PERSON>", "supportedFiles": "File PDF, DOCX, TXT, hoặc CSV", "totalSize": "Tổng dung lượng", "noFileSelected": "<PERSON><PERSON><PERSON> ch<PERSON> file", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công", "fileTooLarge": "File vượt quá dung lượng tối đa {{max}}MB", "file": "<PERSON><PERSON><PERSON> l<PERSON>", "url": "<PERSON><PERSON><PERSON><PERSON> trang web", "text": "<PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON> bản", "title": "<PERSON><PERSON><PERSON> lên ki<PERSON>n thức", "invalidUrl": "URL không hợp lệ", "urlAlreadyAdded": "URL đã tồn tại", "noUrlsToUpload": "<PERSON><PERSON> lòng nhập ít nhất một URL", "uploadError": "Lỗi khi tải lên", "uploaded": "<PERSON><PERSON><PERSON> thức đã tải lên", "knowledgeNameRequired": "<PERSON><PERSON><PERSON> ki<PERSON><PERSON> thức là bắt buộc", "knowledgeNameTooLong": "<PERSON>ên kiến thức ph<PERSON>i có ít hơn 250 ký tự", "textRequired": "<PERSON><PERSON><PERSON> bản là b<PERSON>t buộc", "textTooLong": "<PERSON><PERSON><PERSON> bản ph<PERSON>i có ít hơn 20000 ký tự", "search": "<PERSON><PERSON><PERSON> kiếm kiến thức", "textTitle": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON> thức", "fileTitle": "<PERSON><PERSON><PERSON> thức từ tệp", "urlTitle": "<PERSON><PERSON><PERSON> thức từ URL", "allTitle": "<PERSON><PERSON><PERSON> cả kiến thức", "deleteTitle": "<PERSON><PERSON><PERSON> k<PERSON> thức", "deleteDescription": "Bạn có chắc chắn muốn xóa kiến thức này? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON> xóa kiến thức thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa kiến thức", "deleteConfirm": "Xóa", "deleteCancel": "<PERSON><PERSON><PERSON>", "leaveTitle": "Rời đi mà không lưu?", "leaveDescription": "<PERSON><PERSON><PERSON> thay đổi chưa lưu sẽ bị mất.", "leaveConfirm": "<PERSON><PERSON><PERSON> đi", "leaveCancel": "Ở lại", "textInput": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON><PERSON> tiếp", "textInputPlaceholder": "<PERSON><PERSON><PERSON><PERSON> văn bản kiến thức của bạn ở đây...", "textInputTitle": "<PERSON><PERSON><PERSON> thức v<PERSON><PERSON> bản", "textTitlePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tiêu đề kiến thức", "pleaseSelectAtLeastOneFile": "<PERSON>ui lòng chọn ít nhất một file", "pleaseEnterAtLeastOneURL": "<PERSON><PERSON> lòng nhập ít nhất một URL", "pleaseEnterAtLeastOneTextFile": "<PERSON><PERSON> lòng nhập ít nhất một tệp văn bản", "pleaseEnterAllFields": "<PERSON><PERSON> lòng nhập đầy đủ tiêu đề và nội dung", "newest": "<PERSON><PERSON><PERSON>", "oldest": "<PERSON><PERSON> n<PERSON>", "noKnowledge": "<PERSON><PERSON><PERSON><PERSON> có kiến thức nào", "urlImport": "<PERSON><PERSON><PERSON><PERSON> từ URL", "urlImportDescription": "<PERSON><PERSON><PERSON><PERSON> kiến thức từ trang web", "deleteKnowledge": "<PERSON><PERSON><PERSON>c", "deleteKnowledgeDescription": "Bạn có chắc chắn muốn xóa kiến thức này? Hành động này không thể hoàn tác.", "deleteKnowledgeSuccess": "<PERSON><PERSON><PERSON> thức đã được xóa thành công", "deleteKnowledgeError": "<PERSON><PERSON><PERSON><PERSON> thể xóa kiến thức", "deleteKnowledgeConfirm": "Xóa", "deleteKnowledgeCancel": "<PERSON><PERSON><PERSON>", "fileImport": "<PERSON><PERSON><PERSON><PERSON> từ file", "fileImportDescription": "<PERSON><PERSON><PERSON><PERSON> kiến thức từ file", "fileImportSuccess": "<PERSON><PERSON><PERSON> thức đã đư<PERSON>c tải lên thành công", "fileImportError": "<PERSON><PERSON><PERSON><PERSON> thể tải lên kiến thức", "fileImportConfirm": "<PERSON><PERSON><PERSON>", "fileImportCancel": "<PERSON><PERSON><PERSON>", "textImport": "<PERSON><PERSON><PERSON><PERSON> từ văn bản", "textImportDescription": "<PERSON><PERSON><PERSON><PERSON> kiến thức từ văn bản", "textImportSuccess": "<PERSON><PERSON><PERSON> thức đã đư<PERSON>c tải lên thành công", "textImportError": "<PERSON><PERSON><PERSON><PERSON> thể tải lên kiến thức", "textImportConfirm": "<PERSON><PERSON><PERSON>", "textImportCancel": "<PERSON><PERSON><PERSON>", "urlImportSuccess": "<PERSON><PERSON><PERSON> thức đã đư<PERSON>c tải lên thành công", "urlImportError": "<PERSON><PERSON><PERSON><PERSON> thể tải lên kiến thức", "urlImportConfirm": "<PERSON><PERSON><PERSON>", "urlImportCancel": "<PERSON><PERSON><PERSON>"}}, "customer": {"details": {"customerDetails": "Thông tin khách hàng", "name": "<PERSON><PERSON><PERSON>", "birthday": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "phone": "<PERSON><PERSON><PERSON><PERSON> tho<PERSON>i", "email": "Email", "shippingAddress": "Đ<PERSON>a chỉ giao hàng", "billingAddress": "<PERSON><PERSON><PERSON> chỉ thanh toán", "groupName": "<PERSON><PERSON><PERSON>", "totalLoyalPoints": "<PERSON><PERSON><PERSON> điểm tích l<PERSON>y", "totalRedeemPoints": "Tổng điểm đổi thưởng", "tags": "Thẻ", "noTags": "---"}, "purchase": {"purchaseInfo": "Thông tin mua hàng", "totalSpent": "T<PERSON>ng chi tiêu", "totalProductsPurchased": "<PERSON>ổng sản phẩm đã mua", "purchasedOrder": "Đơn hàng đã mua", "totalProductsReturned": "<PERSON><PERSON>ng sản phẩm đã trả", "lastOrderAt": "Đơn hàng cuối cùng vào"}, "sales": {"suggestionInfo": "<PERSON><PERSON><PERSON><PERSON> tin đề xuất bán hàng", "defaultPriceGroup": "Nhóm giá mặc định", "defaultPaymentMethod": "<PERSON><PERSON><PERSON><PERSON> thức thanh toán mặc định", "discountPercent": "Phần tr<PERSON>m giảm giá"}, "order": {"orderHistory": "<PERSON><PERSON><PERSON> sử đơn hàng"}}, "conversation": {"title": "<PERSON><PERSON><PERSON> tho<PERSON>i", "whatCanIHelpWith": "Tôi có thể giúp gì?", "saySomething": "<PERSON><PERSON>i gì đó...", "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> k<PERSON>..."}, "source": "<PERSON><PERSON><PERSON><PERSON>", "unread": "<PERSON><PERSON><PERSON>", "read": "<PERSON><PERSON> đ<PERSON>", "assignee": "Ngườ<PERSON> phụ trách"}}, "tasks": {"promptContent": "<PERSON>ội dung prompt", "shortDescription": "<PERSON><PERSON>", "shortDescriptionPlaceholder": "<PERSON><PERSON> nội dung kiến thức của bạn vào đây...", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên công vi<PERSON>c", "promptContentPlaceholder": "Tóm tắt bài viết bằng [ngôn ngữ], tập trung vào các ý chính. Bản tóm tắt không quá [số từ] từ.", "editTask": "Chỉnh sửa công việc", "addTask": "<PERSON><PERSON><PERSON><PERSON> công vi<PERSON>c", "save": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "promptHelper": "Sử dụng dấu ngoặc vuông như [biến] để chèn các trường vào prompt c<PERSON><PERSON> bạn. Mỗi trường nên có tên duy nhất. N<PERSON>u một biến xuất hiện nhiều lần, người dùng chỉ cần nhập một lần."}, "activities": {"title": "<PERSON><PERSON><PERSON> đ<PERSON>", "unknown": "Không rõ", "scheduleActivity": {"groupButton": {"markAsDone": "<PERSON><PERSON><PERSON> th<PERSON>", "edit": "Chỉnh sửa", "cancel": "<PERSON><PERSON><PERSON>"}}, "history": {"tabs": {"history": "<PERSON><PERSON><PERSON> s<PERSON>", "comments": "<PERSON><PERSON><PERSON> lu<PERSON>", "placeholder": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>nh luậ<PERSON>..."}}, "headers": {"activity": "<PERSON><PERSON><PERSON> đ<PERSON>", "assignedTo": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> giao", "summary": "<PERSON><PERSON><PERSON>", "dueDate": "<PERSON><PERSON><PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "updatedAt": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c"}, "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> k<PERSON>..."}, "status": "<PERSON><PERSON><PERSON><PERSON> thái", "assignee": "Ngườ<PERSON> phụ trách"}, "status": {"overdue": "<PERSON><PERSON><PERSON> h<PERSON>n", "today": "<PERSON><PERSON><PERSON> nay", "upcoming": "<PERSON><PERSON><PERSON> t<PERSON>i", "noduedate": "<PERSON><PERSON><PERSON><PERSON> có hạn"}, "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t hoạt động thành công", "updateError": "<PERSON><PERSON><PERSON> nhật hoạt động thất bại", "deleteSuccess": "<PERSON><PERSON><PERSON> hoạt động thành công", "deleteError": "<PERSON><PERSON><PERSON> hoạt động thất bại", "error": "Lỗi tạo loại hoạt động", "errorActivityTypeExists": "Loại hoạt động với tên này đã tồn tại", "createSuccess": "<PERSON><PERSON>o loại hoạt động thành công", "createError": "<PERSON><PERSON><PERSON> hoạt động thất bại"}, "opportunityDetail": {"updateStatusSuccess": {"title": "<PERSON><PERSON><PERSON><PERSON> công", "description": "Tr<PERSON><PERSON> thái cơ hội đã đư<PERSON><PERSON> cập nhật thành công."}, "updateStatusError": {"title": "Lỗi", "description": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật trạng thái cơ hội. <PERSON><PERSON> lòng thử lại."}, "customerInfo": {"name": "<PERSON><PERSON><PERSON>", "company": "<PERSON><PERSON>ng ty", "country": "Quốc gia", "address": "Địa chỉ", "billingAddress": "(<PERSON><PERSON>)", "province": "Tỉnh/Thành phố", "ward": "Phường/Xã", "phone": "<PERSON><PERSON> đi<PERSON>n tho<PERSON>i", "job": "<PERSON><PERSON><PERSON> v<PERSON>", "website": "Website", "district": "Quận/Huyện", "placeholder": "<PERSON><PERSON><PERSON> kh<PERSON> h<PERSON>ng"}, "updateError": {"title": "Lỗi", "description": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật c<PERSON> hội. <PERSON><PERSON> lòng thử lại.", "probability": "<PERSON><PERSON><PERSON> su<PERSON>t phải lớn hơn hoặc bằng 0 và nhỏ hơn hoặc bằng 100.", "representativeEmail": "<PERSON><PERSON><PERSON> d<PERSON>ng email không hợp lệ"}, "updateSuccess": {"title": "<PERSON><PERSON><PERSON><PERSON> công", "description": "<PERSON><PERSON> hội đã đư<PERSON><PERSON> cập nhật thành công."}, "updateCustomerError": {"title": "Lỗi", "description": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật kh<PERSON>ch hàng. <PERSON><PERSON> lòng thử lại."}, "updateCustomerSuccess": {"title": "<PERSON><PERSON><PERSON><PERSON> công", "description": "<PERSON><PERSON><PERSON><PERSON> hàng đã đ<PERSON><PERSON><PERSON> cập nhật thành công."}, "createError": {"title": "Lỗi", "description": "<PERSON><PERSON><PERSON><PERSON> thể tạo cơ hội. <PERSON><PERSON> lòng thử lại."}, "createSuccess": {"title": "<PERSON><PERSON><PERSON><PERSON> công", "description": "<PERSON><PERSON> hội đã đư<PERSON><PERSON> tạo thành công."}}, "subscription": {"currentPlan": {"title": "<PERSON><PERSON><PERSON> hi<PERSON>n tại của bạn", "expiresOn": "<PERSON><PERSON><PERSON> hạn v<PERSON>o", "cancelSubscription": "<PERSON><PERSON><PERSON> đ<PERSON>ng ký", "upgrade": "<PERSON><PERSON><PERSON> c<PERSON>p"}, "usageStats": {"title": "Dung lượng đã sử dụng", "messages": "<PERSON>", "staff": "Nhân viên", "storage": "<PERSON><PERSON><PERSON> tr<PERSON>"}, "pricing": {"save": "<PERSON><PERSON><PERSON><PERSON>", "annually": "h<PERSON><PERSON> n<PERSON>m", "title": "<PERSON><PERSON><PERSON> c<PERSON>", "description": "<PERSON><PERSON> c<PERSON>c gói giá linh hoạt cho cá nhân và doanh nghiệp.", "mostPopular": "<PERSON><PERSON> biến nhất", "upgrade": "<PERSON><PERSON><PERSON> c<PERSON>p", "more": "<PERSON><PERSON><PERSON><PERSON>", "showLess": "Ẩn bớt"}, "billing": {"annualPlan": "<PERSON><PERSON><PERSON> h<PERSON> n<PERSON>m", "savings": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> 15%"}, "faq": {"title": "Câu hỏi thường gặp", "description": "<PERSON><PERSON><PERSON><PERSON> tìm thấy câu trả lời bạn cần?", "description2": "<PERSON><PERSON> lòng liên hệ đội ngũ hỗ trợ khách hàng của chúng tôi.", "q1": "Có thể truy cập đ<PERSON><PERSON><PERSON> không?", "a1": "<PERSON><PERSON><PERSON> <PERSON><PERSON> tuân theo mẫu thiết kế WAI-ARIA.", "q2": "<PERSON><PERSON> đư<PERSON> tạo kiểu không?", "a2": "<PERSON><PERSON><PERSON> <PERSON>ó đi kèm với các kiểu mặc định phù hợp với thẩm mỹ của các thành phần khác.", "q3": "Có hoạt ảnh không?", "a3": "<PERSON><PERSON><PERSON> <PERSON><PERSON> đ<PERSON> tạo hoạt ảnh theo mặc định, nhưng bạn có thể tắt nó nếu muốn.", "q4": "Tôi có thể sử dụng nó trên trang web của mình không?", "a4": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phần nà<PERSON> đ<PERSON><PERSON><PERSON> thiết kế để sử dụng trong bất kỳ ứng dụng React nào.", "q5": "<PERSON><PERSON>m thế nào để bắt đầu?", "a5": "Chỉ cần nhập thành phần và sử dụng nó trong JSX của bạn."}}, "layouts": {"title": "<PERSON><PERSON><PERSON><PERSON> lý b<PERSON> cục", "description": "<PERSON><PERSON><PERSON>n lý mặt bằng và sơ đồ đơn vị bất động sản", "totalLayouts": "Tổng số bố cục", "mappedUnits": "Đơn vị đã gán", "mapped": "Đã gán", "unmapped": "Chưa gán", "mappingStats": "<PERSON><PERSON><PERSON><PERSON> kê gán vị trí", "selectLayout": "<PERSON><PERSON><PERSON> b<PERSON> cục", "openMapping": "Mở gán vị trí", "floorPlans": "<PERSON><PERSON> đồ tầng", "addLayout": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> cục", "createLayout": "<PERSON><PERSON><PERSON> b<PERSON> cục", "createLayoutDescription": "<PERSON><PERSON><PERSON> sơ đồ mặt bằng mới cho bất động sản này", "layoutNamePlaceholder": "vd: <PERSON><PERSON><PERSON> 1, <PERSON><PERSON><PERSON>r<PERSON>", "layoutDescriptionPlaceholder": "<PERSON><PERSON> tả tùy chọn cho bố cục này", "searchLayouts": "<PERSON><PERSON><PERSON> k<PERSON>ếm bố cục...", "noLayouts": "<PERSON><PERSON><PERSON><PERSON> tìm thấy bố cục", "noLayoutsFound": "<PERSON><PERSON><PERSON><PERSON> có bố cục nào phù hợp với tìm kiếm", "createFirstLayout": "Tạ<PERSON> sơ đồ mặt bằng đầu tiên để bắt đầu", "tryDifferentSearch": "Thử từ khóa tìm kiếm khác", "selectPropertyFirst": "<PERSON><PERSON><PERSON> bất động sản để xem và quản lý sơ đồ mặt bằng", "deleteLayout": "<PERSON><PERSON><PERSON> b<PERSON> cục", "deleteLayoutConfirmation": "Bạn có chắc chắn muốn xóa bố cục này? Hành động này không thể hoàn tác.", "saveSuccess": "<PERSON><PERSON><PERSON> b<PERSON> cục thành công", "deleteSuccess": "<PERSON><PERSON><PERSON> b<PERSON> cục thành công", "createSuccess": "<PERSON><PERSON><PERSON> b<PERSON> cục thành công", "dimensions": "<PERSON><PERSON><PERSON>", "uploadImage": "<PERSON><PERSON><PERSON> lên sơ đồ mặt bằng", "uploadImageDescription": "<PERSON><PERSON><PERSON> lên hình ảnh sơ đồ mặt bằng để bắt đầu gán vị trí đơn vị", "noLayoutSelected": "<PERSON><PERSON><PERSON> ch<PERSON> b<PERSON> cục", "selectLayoutToStart": "<PERSON><PERSON><PERSON> bố cục từ danh sách để bắt đầu gán vị trí đơn vị", "mappedToLayout": "Đã gán vào bố cục", "assignUnit": "<PERSON><PERSON> vị", "selectUnit": "<PERSON><PERSON>n đơn vị", "mappingProgress": "Tiến độ gán vị trí", "systemHealth": "<PERSON><PERSON><PERSON> trạng hệ thống", "weeklyProgress": "<PERSON><PERSON><PERSON><PERSON> độ tuần", "recentChanges": "<PERSON>hay đổi gần đây", "complete": "<PERSON><PERSON><PERSON> th<PERSON>", "incomplete": "<PERSON><PERSON><PERSON> ho<PERSON>n thành", "withIssues": "<PERSON><PERSON> vấn đề", "duplicateAll": "<PERSON><PERSON> ch<PERSON>p tất cả", "exportAll": "<PERSON><PERSON><PERSON> tất cả", "importAll": "<PERSON><PERSON><PERSON><PERSON> tất cả", "shareAll": "<PERSON><PERSON> sẻ tất cả", "generateReport": "Tạo báo cáo", "saveAsTemplate": "<PERSON><PERSON><PERSON> là<PERSON> mẫu", "viewReport": "<PERSON><PERSON> b<PERSON>o c<PERSON>o", "lastActivity": "<PERSON><PERSON><PERSON> động cu<PERSON>i", "totalUnits": "Tổng số đơn vị", "exportSuccess": "<PERSON><PERSON>t dữ liệu thành công", "exportError": "Lỗi xuất dữ liệu", "importSuccess": "<PERSON><PERSON><PERSON><PERSON> thành công {{count}} b<PERSON> cục", "importError": "Lỗi nhập dữ liệu bố cục", "importParseError": "<PERSON><PERSON><PERSON> dạng tệp không hợp lệ. <PERSON><PERSON> lòng chọn tệp xuất bố cục hợp lệ.", "saveError": "Lỗi lưu bố cục: {{error}}", "deleteError": "Lỗi x<PERSON>a bố cục: {{error}}", "createError": "Lỗi tạo bố cục: {{error}}", "templateCreated": "Tạo mẫu thành công", "templateCreateError": "Lỗi tạo mẫu", "createTemplate": "Tạo mẫu", "createTemplateDescription": "<PERSON><PERSON><PERSON> bố cục này thành mẫu để sử dụng lại cho các bất động sản khác", "templateName": "<PERSON><PERSON>n mẫu", "templateNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên mẫu", "templateDescription": "<PERSON><PERSON>", "templateDescriptionPlaceholder": "<PERSON><PERSON> tả tùy chọn cho mẫu này", "templateCategory": "<PERSON><PERSON>", "categories": {"custom": "<PERSON><PERSON><PERSON> chỉnh", "residential": "<PERSON><PERSON> d<PERSON> c<PERSON>", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "mixed": "Hỗn hợp"}, "overlapWarning": "<PERSON><PERSON><PERSON> b<PERSON>o chồng lấp", "overlapDetected": "<PERSON><PERSON><PERSON> {{count}} hình chồng lấp", "floorNumber": "<PERSON><PERSON> tầng", "floorNumberDescription": "Số tầng tùy chọn cho bố cục này", "dropImageHere": "<PERSON><PERSON><PERSON> hình ảnh mặt bằng vào đây hoặc nhấp để chọn", "supportedFormats": "Hỗ trợ JPG, PNG, SVG, WebP (tối đa 10MB)", "chooseFile": "<PERSON><PERSON><PERSON>", "imageUploaded": "<PERSON><PERSON><PERSON> hình <PERSON>nh thành công", "uploading": "<PERSON><PERSON> t<PERSON> lên", "invalidFileType": "<PERSON><PERSON> lòng chọn tệp hình <PERSON>nh hợp lệ", "fileTooLarge": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> tệp quá lớn. T<PERSON><PERSON> đa cho phép 10MB", "uploadFailed": "Lỗi tải lên hình <PERSON>nh", "dropUnitHere": "<PERSON>h<PERSON> đơn vị vào đây để đặt trên bố cục"}, "properties": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "add": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "selectProperty": "<PERSON><PERSON><PERSON> bất động sản", "createProperty": "<PERSON><PERSON><PERSON> b<PERSON>t độ<PERSON> sản", "editProperty": "Chỉnh sửa bất động sản", "basicInformation": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "addressInformation": "Thông tin địa chỉ", "ownerInformation": "Thông tin chủ sở hữu", "purchaseInformation": "Thông tin mua", "images": "<PERSON><PERSON><PERSON> bất động sản", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t bất động sản thành công", "createSuccess": "<PERSON><PERSON><PERSON> b<PERSON>t động sản thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật bất động sản", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo bất động sản", "headers": {"propertyInfo": "Thông tin BĐS", "name": "<PERSON><PERSON><PERSON> b<PERSON>t động sản", "address": "Địa chỉ", "type": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "totalUnits": "Tổng đơn vị", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "updatedAt": "<PERSON><PERSON><PERSON> n<PERSON> l<PERSON>c", "actions": "<PERSON><PERSON>"}, "address": {"street": "Địa chỉ đường", "city": "<PERSON><PERSON><PERSON><PERSON> phố", "state": "Tỉnh/Thành phố", "zipCode": "<PERSON><PERSON> b<PERSON>u đi<PERSON>n", "country": "Quốc gia"}, "owner": {"name": "<PERSON>ên chủ sở hữu", "email": "<PERSON>ail chủ sở hữu", "phone": "<PERSON><PERSON><PERSON><PERSON> thoại chủ sở hữu"}, "purchase": {"price": "<PERSON><PERSON><PERSON> mua", "date": "<PERSON><PERSON><PERSON> mua"}, "types": {"residential": "<PERSON><PERSON> c<PERSON>", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON> mại", "mixed": "Hỗn hợp"}, "placeholders": {"name": "<PERSON><PERSON><PERSON><PERSON> tên bất động sản", "type": "<PERSON><PERSON><PERSON> lo<PERSON>i bất động sản", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả bất động sản", "street": "<PERSON><PERSON><PERSON><PERSON> địa chỉ đường", "city": "<PERSON><PERSON><PERSON><PERSON> thành phố", "state": "Nhập tỉnh/thành phố", "zipCode": "<PERSON><PERSON><PERSON><PERSON> mã b<PERSON>u đi<PERSON>n", "country": "<PERSON><PERSON><PERSON><PERSON> quốc gia", "ownerName": "<PERSON><PERSON><PERSON><PERSON> tên chủ sở hữu", "ownerEmail": "<PERSON><PERSON>ập email chủ sở hữu", "ownerPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại chủ sở hữu", "purchasePrice": "<PERSON><PERSON><PERSON><PERSON> gi<PERSON> mua", "uploadImages": "<PERSON><PERSON><PERSON><PERSON> để tải lên hình ảnh hoặc kéo thả"}, "filters": {"search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm bất động sản..."}, "type": "<PERSON><PERSON><PERSON> bất động sản", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON> c<PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "deleteSuccess": "<PERSON><PERSON><PERSON> bất động sản thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa bất động sản", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải thông tin bất động sản", "deleteProperty": "<PERSON><PERSON><PERSON>", "deleteConfirmation": "Bạn có chắc chắn muốn xóa {{name}}? Hành động này không thể hoàn tác.", "noImages": "<PERSON><PERSON><PERSON> c<PERSON> hình <PERSON>nh", "unitStatus": "Tổng quan trạng thái căn hộ", "stats": {"totalUnits": "<PERSON><PERSON>ng số căn hộ", "occupancyRate": "Tỷ lệ thuê", "occupiedUnits": "<PERSON><PERSON><PERSON> hộ đã thuê", "availableUnits": "<PERSON><PERSON><PERSON> hộ còn trống"}}, "contracts": {"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> đồng", "search": "<PERSON><PERSON><PERSON> kiếm hợp đồng...", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm theo số hợp đồng", "export": "<PERSON><PERSON><PERSON> dữ liệu", "confirmDelete": "Bạn có chắc chắn muốn xóa hợp đồng nà<PERSON>?", "viewContracts": "<PERSON><PERSON> đ<PERSON>", "headers": {"contractInfo": "<PERSON><PERSON><PERSON><PERSON> tin hợp đồng", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "duration": "<PERSON><PERSON><PERSON><PERSON> hạn", "rentAmount": "<PERSON><PERSON><PERSON> thu<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON>"}, "filters": {"status": "<PERSON><PERSON><PERSON><PERSON> thái", "contractType": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "clear": "Xóa bộ lọc"}, "status": {"draft": "<PERSON><PERSON><PERSON>", "active": "<PERSON><PERSON> l<PERSON>", "expired": "<PERSON><PERSON><PERSON>", "terminated": "<PERSON><PERSON> hủy", "pending": "<PERSON>ờ <PERSON>"}, "types": {"monthly": "<PERSON>", "annual": "<PERSON>", "profitSharing": "<PERSON><PERSON> sẻ lợi nhuận", "revenueSharing": "<PERSON>a sẻ doanh thu"}, "actions": {"view": "Xem", "edit": "Chỉnh sửa", "delete": "Xóa"}, "createContract": "<PERSON><PERSON><PERSON> đ<PERSON>", "editContract": "Chỉnh sửa hợp đồng", "createDescription": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng bất động sản mới với các điều khoản linh hoạt", "editDescription": "<PERSON><PERSON><PERSON> nhật chi tiết và điều kho<PERSON>n hợp đồng", "sections": {"propertyUnit": "Chọn Bất động sản & <PERSON><PERSON><PERSON> hộ", "contractDetails": "<PERSON> tiết hợp đồng", "financialTerms": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>n tài ch<PERSON>h", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n"}, "fields": {"property": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON> thuê", "contractType": "<PERSON><PERSON><PERSON> h<PERSON> đ<PERSON>ng", "startDate": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> đ<PERSON>u", "endDate": "<PERSON><PERSON><PERSON> k<PERSON> th<PERSON>c", "rentDueDay": "<PERSON><PERSON><PERSON> ti<PERSON>n", "rentAmount": "<PERSON><PERSON> tiền thuê", "depositAmount": "Tiền đặt cọc", "lateFeeAmount": "<PERSON><PERSON> chậm trễ", "noticePeriodDays": "<PERSON><PERSON>ờ<PERSON> gian b<PERSON><PERSON> tr<PERSON> (ngày)", "autoRenewal": "<PERSON><PERSON> hạn tự động", "profitSharingPercentage": "<PERSON><PERSON><PERSON> trăm chia sẻ lợi nhuận", "revenueSharingPercentage": "<PERSON><PERSON><PERSON> tr<PERSON>m chia sẻ doanh thu", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON> ki<PERSON>n"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "tenant": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "contractType": "<PERSON><PERSON><PERSON> lo<PERSON>i hợp đồng", "rentDueDay": "<PERSON><PERSON><PERSON><PERSON><PERSON> (1-31)", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền thuê hàng tháng", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> tiền đặt cọc", "lateFeeAmount": "<PERSON><PERSON><PERSON><PERSON> phí chậm tr<PERSON> (t<PERSON><PERSON> ch<PERSON>n)", "noticePeriodDays": "<PERSON><PERSON><PERSON><PERSON> th<PERSON>i gian b<PERSON> tr<PERSON> (ngày)", "profitSharingPercentage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> trăm (0-100)", "revenueSharingPercentage": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> trăm (0-100)", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> điều khoản và điều kiện hợp đồng"}, "contractTypes": {"monthly": "<PERSON><PERSON><PERSON> theo tháng", "annual": "<PERSON><PERSON><PERSON> the<PERSON> n<PERSON>", "profitSharing": "<PERSON><PERSON> sẻ lợi nhuận", "revenueSharing": "<PERSON>a sẻ doanh thu"}, "descriptions": {"autoRenewal": "Tự động gia hạn hợp đồng khi hết hạn"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thành công", "createError": "<PERSON><PERSON><PERSON> h<PERSON><PERSON> đồng thất bại", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t hợp đồng thành công", "updateError": "<PERSON><PERSON><PERSON> nhật hợp đồng thất bại"}, "contractDetails": "<PERSON>", "overview": "<PERSON><PERSON><PERSON>", "paymentHistory": "<PERSON><PERSON><PERSON>"}, "reports": {"title": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> B<PERSON><PERSON> động sản", "description": "<PERSON><PERSON><PERSON> báo cáo toàn diện và phân tích hiệu suất bất động sản", "generateReport": "Tạo Báo cáo", "downloadReport": "<PERSON><PERSON><PERSON>", "generating": "<PERSON><PERSON> tạo...", "categories": {"financial": {"title": "Báo c<PERSON>o T<PERSON> ch<PERSON>", "description": "<PERSON><PERSON> tích doanh thu, chi phí và lợi nhuận", "generate": "Tạo"}, "occupancy": {"title": "<PERSON><PERSON><PERSON> cáo Tỷ lệ <PERSON>p đ<PERSON>y", "description": "Tỷ lệ trống và giữ chân khách thuê", "generate": "Tạo"}, "performance": {"title": "<PERSON><PERSON> tích <PERSON>", "description": "Chỉ số hiệu suất bất động sản và đơn vị", "generate": "Tạo"}, "maintenance": {"title": "Báo c<PERSON>o <PERSON> trì", "description": "Chi phí bảo trì và xu hướng", "generate": "Tạo"}, "tenant": {"title": "<PERSON><PERSON> tích <PERSON>ch thuê", "description": "Thông tin nhân khẩu học và hành vi khách thuê", "generate": "Tạo"}, "custom": {"title": "Báo cáo Tù<PERSON> chỉnh", "description": "<PERSON>â<PERSON> dựng báo cáo tùy chỉnh với bộ lọc", "create": "Tạo"}}, "comingSoon": {"title": "<PERSON><PERSON> thống <PERSON> c<PERSON>o <PERSON> cao - Sắp ra mắt", "description": "<PERSON><PERSON> tích toà<PERSON>, báo cáo tùy chỉnh và tính năng xuất dữ liệu sẽ có sẵn tại đây."}}, "dashboard": {"propertyAssets": {"title": "<PERSON>ảng điều khiển tài sản bất động sản", "description": "Tổng quan hiệu suất danh mục bất động sản và các chỉ số chính"}, "metrics": {"totalProperties": "<PERSON>ổng số bất động sản", "totalUnits": "<PERSON><PERSON>ng số căn hộ", "activeContracts": "<PERSON><PERSON><PERSON> đồng đang hoạt động", "monthlyRevenue": "<PERSON><PERSON><PERSON> thu hàng tháng"}, "charts": {"occupancyOverview": "T<PERSON>ng quan tỷ lệ lấp đầy"}, "occupancy": {"occupied": "<PERSON><PERSON> thuê", "vacant": "<PERSON><PERSON><PERSON><PERSON>", "rate": "Tỷ lệ lấp đầy", "tenants": "<PERSON>ổng số khách thuê"}, "quickActions": {"title": "<PERSON><PERSON> t<PERSON> n<PERSON>h", "addProperty": "<PERSON><PERSON><PERSON><PERSON> bất động sản", "addUnit": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>n hộ", "createContract": "<PERSON><PERSON><PERSON> đ<PERSON>", "addTenant": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>ch thuê"}, "recentActivities": {"title": "<PERSON><PERSON><PERSON> động gần đây", "viewAll": "<PERSON><PERSON> tất cả hoạt động"}, "portfolio": {"title": "<PERSON><PERSON><PERSON> t<PERSON>t danh mục", "properties": "<PERSON><PERSON><PERSON> đ<PERSON> sản", "units": "<PERSON><PERSON><PERSON>", "contracts": "<PERSON><PERSON><PERSON>", "revenue": "<PERSON><PERSON>h thu", "performance": "<PERSON><PERSON><PERSON> su<PERSON>t tổng thể"}}, "units": {"title": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "addUnit": "<PERSON><PERSON><PERSON><PERSON>", "viewUnits": "<PERSON><PERSON>", "createUnit": "<PERSON><PERSON><PERSON>", "editUnit": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> c<PERSON>n hộ cho thuê mới", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin căn hộ", "deleteUnit": "<PERSON><PERSON><PERSON>", "deleteConfirmation": "Bạn có chắc chắn muốn xóa căn hộ {{unit}}? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> căn hộ thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa căn hộ", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t căn hộ thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật căn hộ: {{error}}", "createSuccess": "<PERSON><PERSON><PERSON> c<PERSON>n hộ thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo căn hộ", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải căn hộ", "unitNumber": "<PERSON><PERSON><PERSON>", "noImages": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh", "noUnitsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy căn hộ", "noPropertiesAvailable": "<PERSON><PERSON><PERSON><PERSON> có bất động sản", "searchUnits": "<PERSON><PERSON><PERSON> kiếm căn hộ...", "unitList": "<PERSON><PERSON>", "assignUnit": "<PERSON><PERSON>", "selectUnit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "basicInformation": "Thông Tin Cơ Bản", "specifications": "Thông Số K<PERSON>hu<PERSON>", "financialInformation": "Thông Tin Tài <PERSON>", "amenities": "<PERSON><PERSON><PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON> Ảnh", "headers": {"unitInfo": "<PERSON><PERSON>ông Tin <PERSON>", "property": "<PERSON><PERSON><PERSON>", "unitNumber": "Số Căn Hộ", "type": "<PERSON><PERSON><PERSON>", "size": "<PERSON><PERSON><PERSON>", "rent": "<PERSON><PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON>", "floor": "<PERSON><PERSON><PERSON>", "squareFootage": "<PERSON><PERSON><PERSON> (m²)", "bedrooms": "<PERSON><PERSON>ng <PERSON>", "bathrooms": "Phòng Tắm", "rentAmount": "<PERSON><PERSON>", "depositAmount": "<PERSON><PERSON><PERSON><PERSON>"}, "filters": {"type": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "property": "<PERSON><PERSON><PERSON>", "rentRange": "Khoảng G<PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "search": {"placeholder": "<PERSON><PERSON><PERSON> kiếm căn hộ..."}}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON> Cô<PERSON>"}, "status": {"available": "<PERSON><PERSON> Sẵn", "occupied": "<PERSON><PERSON>", "maintenance": "Bảo Trì", "inactive": "Không Hoạt Động"}, "types": {"studio": "Studio", "1br": "1 Phòng Ngủ", "2br": "2 Phòng Ngủ", "3br": "3 Phòng Ngủ", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "retail": "B<PERSON> Lẻ"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unitNumber": "<PERSON><PERSON><PERSON><PERSON> số căn hộ", "type": "<PERSON><PERSON><PERSON> lo<PERSON>i căn hộ", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả căn hộ", "floor": "<PERSON><PERSON><PERSON><PERSON> số tầng", "squareFootage": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>ch", "bedrooms": "Số phòng ngủ", "bathrooms": "Số phòng tắm", "rentAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền thuê", "depositAmount": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON> c<PERSON>c", "uploadImages": "<PERSON><PERSON><PERSON><PERSON> để tải lên hình <PERSON>nh căn hộ"}}, "tenants": {"title": "<PERSON><PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "editTenant": "Chỉnh <PERSON><PERSON><PERSON>", "createTenant": "<PERSON><PERSON><PERSON>", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin người thuê", "createDescription": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê mới", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm người thuê...", "viewTenant": "<PERSON><PERSON>", "noTenant": "<PERSON><PERSON><PERSON><PERSON>", "sections": {"personalInfo": "Thông Tin Cá Nhân", "identification": "<PERSON><PERSON><PERSON><PERSON>ờ Tùy Thân", "employment": "<PERSON><PERSON><PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON>"}, "fields": {"fullName": "<PERSON><PERSON>", "firstName": "<PERSON><PERSON><PERSON>", "lastName": "Họ", "email": "Email", "phone": "<PERSON><PERSON><PERSON><PERSON>", "dateOfBirth": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "identificationType": "Loại Giấ<PERSON>", "identificationNumber": "Số Giấy Tờ", "employmentStatus": "Tình Trạng Công <PERSON>", "employerName": "<PERSON><PERSON><PERSON>", "monthlyIncome": "<PERSON><PERSON><PERSON>", "emergencyContactName": "<PERSON><PERSON><PERSON>", "emergencyContactPhone": "Số Đ<PERSON>"}, "headers": {"tenantInfo": "T<PERSON>ông Tin Ngườ<PERSON>", "contact": "<PERSON><PERSON><PERSON>", "employmentStatus": "Tình Trạng Công <PERSON>", "status": "Trạng <PERSON>", "emergencyContact": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "filters": {"leaseStatus": "<PERSON>rạng <PERSON>", "property": "<PERSON><PERSON><PERSON>"}, "status": {"active": "Ho<PERSON><PERSON>", "inactive": "Không Hoạt Động", "expired": "<PERSON><PERSON><PERSON>", "pending": "Chờ <PERSON>"}, "employmentStatus": {"employed": "<PERSON><PERSON>", "unemployed": "<PERSON><PERSON><PERSON><PERSON>", "self_employed": "<PERSON><PERSON>", "student": "<PERSON><PERSON> Viê<PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON>"}, "stats": {"totalContracts": "Tổng Số H<PERSON>", "activeContracts": "<PERSON><PERSON><PERSON>", "joinDate": "<PERSON><PERSON><PERSON>"}, "quickStats": "<PERSON><PERSON><PERSON><PERSON>", "activeContracts": "<PERSON><PERSON><PERSON>", "identificationTypes": {"passport": "<PERSON><PERSON>", "national_id": "CMND/CCCD", "driver_license": "Bằng Lái Xe"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON>ười thuê mà bạn đang tìm không tồn tại hoặc đã bị xóa."}, "documents": "<PERSON><PERSON><PERSON>", "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa người thuê này? Hành động này không thể hoàn tác.", "hasActiveContracts": "<PERSON><PERSON><PERSON>i thuê này có hợp đồng đang hoạt động. Bạn có chắc chắn muốn xóa?"}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i thuê thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa người thuê"}}, "maintenance": {"title": "Bảo Trì", "add": "<PERSON><PERSON><PERSON>", "search": "T<PERSON><PERSON> kiếm bảo trì...", "searchPlaceholder": "T<PERSON><PERSON> kiếm theo tiêu đề yêu cầu", "export": "<PERSON><PERSON><PERSON>", "confirmDelete": "Bạn có chắc chắn muốn xóa yêu cầu bảo trì này?", "headers": {"requestInfo": "<PERSON><PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "requester": "<PERSON><PERSON><PERSON><PERSON>", "category": "<PERSON><PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "status": "Trạng <PERSON>", "assignedTo": "<PERSON><PERSON>", "estimatedCost": "Chi Phí Ước Tính", "dueDate": "<PERSON><PERSON><PERSON>", "createdAt": "<PERSON><PERSON><PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "filters": {"status": "Trạng <PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "category": "<PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>"}, "priorities": {"low": "<PERSON><PERSON><PERSON><PERSON>", "medium": "<PERSON><PERSON>", "high": "<PERSON>", "urgent": "<PERSON><PERSON><PERSON><PERSON>"}, "categories": {"plumbing": "Ống Nước", "electrical": "<PERSON><PERSON><PERSON><PERSON>", "hvac": "<PERSON><PERSON><PERSON><PERSON>", "structural": "<PERSON><PERSON><PERSON>", "appliance": "<PERSON><PERSON><PERSON>t Bị", "cosmetic": "<PERSON><PERSON><PERSON><PERSON>", "cleaning": "<PERSON><PERSON>", "security": "<PERSON>", "general": "<PERSON>", "other": "K<PERSON><PERSON><PERSON>"}, "actions": {"addManual": "<PERSON><PERSON><PERSON><PERSON>", "view": "Xem", "edit": "Chỉnh Sửa", "delete": "Xóa"}, "createRequest": "<PERSON><PERSON><PERSON>", "editRequest": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> yêu cầu bảo trì mới cho bất động sản của bạn", "editDescription": "<PERSON><PERSON><PERSON> nhật chi tiết yêu cầu bảo trì", "sections": {"propertyUnit": "Lựa Chọn Bất Động Sản & Căn Hộ", "requestDetails": "<PERSON>", "scheduling": "Lập L<PERSON> & Nhà Thầu", "costInfo": "Thông Tin Chi Phí", "additional": "T<PERSON>ông Tin Bổ Sung"}, "fields": {"property": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON>i<PERSON><PERSON>", "description": "<PERSON><PERSON>", "priority": "Đ<PERSON> Ưu Tiên", "category": "<PERSON><PERSON>", "status": "Trạng <PERSON>", "scheduledDate": "<PERSON><PERSON><PERSON>", "completedDate": "<PERSON><PERSON><PERSON>", "contractorName": "<PERSON><PERSON><PERSON>", "contractorPhone": "Số Điện Thoại Nhà <PERSON>hầ<PERSON>", "estimatedCost": "Chi Phí Ước Tính ($)", "actualCost": "<PERSON> Phí <PERSON> ($)", "notes": "<PERSON><PERSON>", "images": "<PERSON><PERSON><PERSON>"}, "placeholders": {"property": "<PERSON><PERSON><PERSON> bất động sản", "unit": "<PERSON><PERSON><PERSON> c<PERSON>n hộ", "tenant": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> thuê", "title": "<PERSON><PERSON> tả ngắn gọn về vấn đề", "description": "<PERSON><PERSON> tả chi tiết về vấn đề bảo trì", "priority": "<PERSON><PERSON><PERSON> mức độ ưu tiên", "category": "<PERSON><PERSON><PERSON> danh mục", "status": "<PERSON><PERSON><PERSON> trạng thái", "contractorName": "<PERSON><PERSON><PERSON><PERSON> tên nhà thầu", "contractorPhone": "<PERSON><PERSON><PERSON><PERSON> số điện thoại nhà thầu", "estimatedCost": "0.00", "actualCost": "0.00", "notes": "<PERSON><PERSON> chú hoặc hướng dẫn bổ sung"}, "options": {"propertyWide": "<PERSON><PERSON><PERSON> trì toàn bộ bất động sản", "noTenant": "<PERSON><PERSON><PERSON><PERSON> có người thuê cụ thể"}, "status": {"open": "Mở", "in_progress": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "cancelled": "Đã <PERSON>", "inProgress": "<PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> y<PERSON>u cầu bảo trì thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo yêu cầu bảo trì", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t yêu cầu bảo trì thành công", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật yêu cầu bảo trì"}, "viewRequests": "<PERSON><PERSON>"}, "payments": {"headers": {"paymentInfo": "Thông Tin Thanh Toán", "amount": "Số Tiền", "contract": "<PERSON><PERSON><PERSON>", "property": "<PERSON><PERSON><PERSON>", "status": "Trạng <PERSON>", "paymentDate": "<PERSON><PERSON><PERSON>", "createdAt": "Tạo Lú<PERSON>", "updatedAt": "<PERSON><PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON>"}, "status": {"pending": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON>"}}}, "table": {"pagination": {"rowsPerPage": "<PERSON><PERSON> hàng trên trang", "description": "{{start}} đến {{end}} hàng trong tổng số {{total}}", "next": "<PERSON><PERSON><PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "selected": {"title": "<PERSON><PERSON> ch<PERSON>n", "delete": "Xóa {{count}} đ<PERSON> chọn"}, "export": {"title": "<PERSON><PERSON><PERSON> dữ liệu", "description": "<PERSON><PERSON><PERSON> dữ liệu", "confirm": "<PERSON><PERSON><PERSON> dữ liệu", "cancel": "<PERSON><PERSON><PERSON>"}, "filter": {"clearFilter": "Xóa bộ lọc", "loadMore": "<PERSON><PERSON><PERSON>ê<PERSON>"}, "savedFilters": {"settings": {"settings": "Cài đặt", "title": "Cài đặt tab", "description": "6 tab đầu tiên sẽ được ưu tiên hiển thị trên trang chính. Tùy chỉnh chúng dựa trên nhu cầu của bạn."}}}, "validation": {"titleRequired": "Ti<PERSON><PERSON> đề là b<PERSON><PERSON> buộc", "required": "Trư<PERSON>ng này là bắ<PERSON> buộc", "invalidEmail": "<PERSON><PERSON> lòng nhập email hợp lệ", "minLength": "<PERSON><PERSON><PERSON> có <PERSON>t nhất {{count}} ký tự", "maxLength": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> quá {{count}} ký tự", "passwordMismatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "invalidUsername": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập ít nhất có 3 ký tự", "emailRequired": "<PERSON><PERSON> lòng nhập email", "usernameRequired": "<PERSON><PERSON> lòng nhập tên đăng nhập", "passwordRequired": "<PERSON><PERSON> lòng nhập mật kh<PERSON>u", "confirmPasswordRequired": "<PERSON><PERSON> lòng xác nhận mật kh<PERSON>u", "invalidPassword": "<PERSON><PERSON>t khẩu phải có ít nhất 8 ký tự", "passwordsDoNotMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "verificationCodeRequired": "<PERSON><PERSON> lòng nhập mã xác thực", "verificationCodeLength": "<PERSON>ã xác thực ph<PERSON>i có 6 ký tự", "sessionRequired": "Session l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "usernameSpecialCharacters": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập chỉ có thể chứa chữ cái, số và dấu gạch dưới", "skuFormat": "Mã SKU chỉ có thể chứa chữ cái, số và dấu gạch dưới", "skuRequired": "Mã SKU là bắt buộc", "nameRequired": "<PERSON><PERSON><PERSON> là b<PERSON><PERSON> bu<PERSON>c", "nameTooLong": "<PERSON>ên ph<PERSON>i có ít hơn 100 ký tự", "titleTooLong": "Ti<PERSON>u đề phải có ít hơn 100 ký tự", "priceRequired": "<PERSON><PERSON><PERSON> là b<PERSON> bu<PERSON>c", "imageRequired": "<PERSON><PERSON> nh<PERSON>t một ảnh là bắt buộc", "imageFormat": "<PERSON><PERSON><PERSON> dạng <PERSON>nh không hợp lệ", "priceMustBePositive": "<PERSON><PERSON> lòng nh<PERSON>p gi<PERSON> hợp lệ", "invalidPrice": "<PERSON><PERSON><PERSON> k<PERSON>ng h<PERSON>p lệ", "wrongUsernameOrPassword": "<PERSON><PERSON><PERSON> đ<PERSON>ng nhập hoặc mật khẩu không chính xác", "phoneRequired": "<PERSON><PERSON> điện thoại là bắt buộc", "phoneNumberAlreadyExists": "<PERSON><PERSON> điện thoại đã tồn tại", "contactRequired": "<PERSON><PERSON><PERSON> h<PERSON> là b<PERSON><PERSON> bu<PERSON>c", "customerRequired": "<PERSON><PERSON><PERSON><PERSON> hàng là b<PERSON> bu<PERSON><PERSON>", "emailOrPhoneRequired": "Email hoặc số điện thoại là bắt buộc", "activityTypeRequired": "Loại hoạt động là bắt buộc", "summaryRequired": "<PERSON><PERSON><PERSON> tắt là bắt buộc", "propertyRequired": "<PERSON><PERSON>t động sản là bắt buộc", "unitRequired": "<PERSON><PERSON><PERSON> hộ là b<PERSON>t buộc", "tenantRequired": "<PERSON><PERSON><PERSON><PERSON> thuê là bắ<PERSON> buộc", "contractTypeRequired": "<PERSON><PERSON><PERSON> hợp đồng là bắt buộc", "startDateRequired": "<PERSON><PERSON><PERSON> bắt đầu là bắt buộc", "rentAmountMustBePositive": "Số tiền thuê phải lớn hơn 0", "depositAmountMustBePositive": "Tiền đặt cọc phải lớn hơn hoặc bằng 0", "lateFeeAmountMustBePositive": "<PERSON><PERSON> chậm trễ phải lớn hơn hoặc bằng 0", "rentDueDayMin": "<PERSON><PERSON><PERSON>g tiền phải từ 1", "rentDueDayMax": "<PERSON><PERSON><PERSON> đ<PERSON>g tiền tối đa là 31", "percentageMax": "<PERSON><PERSON><PERSON> trăm tối đa là 100", "noticePeriodMustBePositive": "Thời gian b<PERSON><PERSON> tr<PERSON><PERSON><PERSON> phải lớn hơn 0", "sharingPercentageRequired": "Phần trăm chia sẻ là bắt buộc cho loại hợp đồng này", "idRequired": "ID là bắ<PERSON> buộc"}, "footer": {"crafted": "<PERSON><PERSON><PERSON><PERSON> tạo với", "by": "bởi", "team": "đội ngũ OneXAPIs", "heart": "tr<PERSON>i tim"}, "install": {"installing": "<PERSON><PERSON> cài đặt...", "pleaseWait": "<PERSON>ui lòng đợi trong gi<PERSON>y lát", "error": {"backToHome": "Trở về trang chủ", "notFound": "<PERSON><PERSON> vẻ như bạn đã đi vào vùng cực lạc không xác định", "installationFailed": "Cài đặt thất bại", "missingSourceChannel": "<PERSON><PERSON><PERSON><PERSON> tìm thấy kênh nguồn", "authorizeDestination": "<PERSON><PERSON> lòng xác thực kênh đích"}}, "error": {"backToHome": "Trở về trang chủ", "notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang", "notFoundDescription": "<PERSON>rang bạn đang tìm kiếm không tồn tại."}, "socialIntegration": {"authorize": "<PERSON><PERSON><PERSON> th<PERSON>c", "reAuthorize": "<PERSON><PERSON><PERSON> th<PERSON>c l<PERSON>i", "newAuthorize": "<PERSON><PERSON><PERSON> th<PERSON>c mới", "authorized": "<PERSON><PERSON> xác thực", "sessionExpired": "<PERSON><PERSON><PERSON> đã hết hạn", "failedToSwitchStatus": "<PERSON><PERSON><PERSON><PERSON> thể chuyển đổi trạng thái kết nối", "failedToSetup": "<PERSON><PERSON><PERSON><PERSON> thể thiết lập kết nối", "facebookSetup": "<PERSON><PERSON><PERSON><PERSON> lập t<PERSON><PERSON> h<PERSON>p <PERSON>", "zaloSetup": "<PERSON><PERSON><PERSON><PERSON> lập t<PERSON><PERSON> hợ<PERSON>", "defaultSetup": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> t<PERSON><PERSON> hợp"}, "filter": {"allTime": "<PERSON><PERSON><PERSON> cả thời gian", "today": "<PERSON><PERSON><PERSON> nay", "yesterday": "<PERSON><PERSON><PERSON> qua", "lastWeek": "<PERSON><PERSON><PERSON> tr<PERSON>", "lastMonth": "<PERSON><PERSON><PERSON><PERSON>", "last7Days": "7 ngày qua", "last30Days": "30 ngày qua", "last90Days": "90 ngày qua", "thisWeek": "<PERSON><PERSON><PERSON>", "thisMonth": "<PERSON><PERSON><PERSON><PERSON>", "thisYear": "<PERSON><PERSON><PERSON>", "customize": "<PERSON><PERSON><PERSON> chỉnh", "reset": "<PERSON><PERSON><PERSON>", "apply": "<PERSON><PERSON>"}, "financial": {"dashboard": {"title": "<PERSON><PERSON><PERSON> điều khiển tài ch<PERSON>h", "description": "Tổng quan hiệu suất tài ch<PERSON>h và chỉ số của bất động sản"}, "timeRange": {"month": "<PERSON><PERSON><PERSON><PERSON>", "quarter": "<PERSON><PERSON><PERSON>", "year": "Năm"}, "exportReport": "<PERSON><PERSON><PERSON> b<PERSON>o c<PERSON>o", "metrics": {"totalRevenue": "<PERSON><PERSON>ng doanh thu", "netIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ròng", "occupancyRate": "Tỷ lệ lấp đầy", "totalExpenses": "Tổng chi phí"}, "charts": {"revenueTrend": "<PERSON> h<PERSON> doanh thu", "expenseBreakdown": "<PERSON>ân tích chi phí", "occupancyTrend": "<PERSON> h<PERSON> lấp <PERSON>", "revenueAnalysis": "<PERSON><PERSON> tích doanh thu"}, "revenue": "<PERSON><PERSON>h thu", "netIncome": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> ròng", "performance": "<PERSON><PERSON><PERSON>", "profitMargin": "Tỷ suất lợi n<PERSON>n", "totalProperties": "<PERSON>ổng số bất động sản", "totalUnits": "<PERSON><PERSON>ng số căn hộ", "occupiedUnits": "<PERSON><PERSON><PERSON> hộ đã thuê", "vacancyRate": "Tỷ lệ trống", "ytdPerformance": "<PERSON><PERSON><PERSON> suất từ đầu năm", "topProperties": "<PERSON><PERSON><PERSON> động sản hiệu suất cao", "monthlyRevenue": "doanh thu hàng tháng", "expenses": "Chi phí", "alerts": {"title": "<PERSON><PERSON><PERSON> b<PERSON>o tài ch<PERSON>h", "rentDue": "<PERSON><PERSON><PERSON> đến hạn thu tiền thuê", "highExpenses": "<PERSON><PERSON><PERSON> báo chi phí cao", "leaseExpiring": "<PERSON><PERSON><PERSON> đồng sắ<PERSON> hết hạn"}, "revenueAnalytics": {"title": "<PERSON><PERSON> tích doanh thu", "description": "<PERSON><PERSON> tích doanh thu chi tiết với biểu đồ tương tác và xu hướng"}, "expenseAnalysis": {"title": "<PERSON>ân tích chi phí", "description": "<PERSON><PERSON> tích chi phí toàn diện và thông tin tối ưu hóa chi phí"}, "profitAnalysis": {"title": "<PERSON><PERSON> tích lợi n<PERSON>n", "description": "<PERSON> hướng lợi nhuận, biên lợi nhuận và theo dõi hiệu suất ROI"}}, "contracts": {"contractDetails": "<PERSON>", "contractId": "<PERSON><PERSON>", "overview": "<PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON>", "progress": "<PERSON>i<PERSON><PERSON>", "daysRemaining": "ng<PERSON><PERSON> còn lại", "openEnded": "<PERSON><PERSON><PERSON>ng giới hạn thời gian", "autoRenewalEnabled": "<PERSON><PERSON> hạn tự động đ<PERSON><PERSON><PERSON> bật", "perMonth": "mỗi tháng", "dayOfMonth": " của mỗi tháng", "sharingTerms": "<PERSON><PERSON><PERSON><PERSON> Sẻ", "paymentSummary": "<PERSON><PERSON>ng <PERSON>", "revenueProjections": "<PERSON><PERSON><PERSON>", "info": "<PERSON><PERSON>ông Tin <PERSON>", "days": "ng<PERSON>y", "quickActions": "<PERSON><PERSON>", "fields": {"property": "<PERSON><PERSON><PERSON>", "unit": "<PERSON><PERSON><PERSON>", "tenant": "<PERSON><PERSON><PERSON><PERSON>", "startDate": "<PERSON><PERSON><PERSON>", "endDate": "<PERSON><PERSON><PERSON>", "rentAmount": "<PERSON><PERSON>", "depositAmount": "Tiền Đặt Cọc", "lateFeeAmount": "<PERSON><PERSON>", "rentDueDay": "<PERSON><PERSON><PERSON>", "profitSharingPercentage": "Phần Tr<PERSON><PERSON> Sẻ <PERSON>ợ<PERSON>n", "revenueSharingPercentage": "<PERSON>ần <PERSON>r<PERSON><PERSON> Sẻ <PERSON>h <PERSON>hu", "noticePeriod": "Thời G<PERSON>"}, "sections": {"financialTerms": "<PERSON><PERSON><PERSON><PERSON>", "termsConditions": "<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON><PERSON>"}, "stats": {"totalPayments": "Tổng Thanh Toán", "completedPayments": "<PERSON><PERSON>", "pendingPayments": "Chờ Thanh To<PERSON>", "totalCollected": "<PERSON><PERSON><PERSON>"}, "projections": {"expectedMonthly": "<PERSON><PERSON><PERSON>", "expectedAnnual": "<PERSON><PERSON><PERSON>"}, "actions": {"recordPayment": "<PERSON><PERSON> <PERSON>", "viewPayments": "<PERSON><PERSON>"}, "errors": {"notFound": "<PERSON><PERSON><PERSON><PERSON>", "notFoundDescription": "<PERSON><PERSON><PERSON> đồng bạn đang tìm không tồn tại hoặc đã bị xóa."}, "deleteConfirmation": {"title": "<PERSON><PERSON><PERSON>", "description": "Bạn có chắc chắn muốn xóa hợp đồng này? Hành động này không thể hoàn tác.", "activeContract": "<PERSON><PERSON><PERSON> là hợp đồng đang hoạt động. Việ<PERSON> xóa có thể ảnh hưởng đến các thanh toán và ghi chép hiện tại."}, "messages": {"deleteSuccess": "<PERSON><PERSON><PERSON> hợp đồng thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa hợp đồng"}}, "unit": {"title": "<PERSON><PERSON><PERSON>", "type": {"apartment": "<PERSON><PERSON><PERSON>", "studio": "Studio", "1br": "1 Phòng Ngủ", "2br": "2 Phòng Ngủ", "3br": "3 Phòng Ngủ", "4br": "4 Phòng Ngủ", "penthouse": "Penthouse", "duplex": "Duplex", "commercial": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "office": "<PERSON><PERSON><PERSON>", "retail": "B<PERSON> Lẻ", "warehouse": "<PERSON><PERSON>"}}, "numbers": {"abbreviations": {"thousand": "K", "million": "tr", "billion": "tỷ", "trillion": "nghìn tỷ"}, "currency": {"symbol": "₫", "code": "VND"}}, "payments": {"createPayment": "<PERSON><PERSON><PERSON>", "editPayment": "Chỉnh <PERSON><PERSON><PERSON>", "createDescription": "<PERSON><PERSON><PERSON> bản ghi <PERSON>h toán mới cho tiền thuê, tiền đặt cọc và các khoản phí khác", "editDescription": "<PERSON><PERSON><PERSON> nhật thông tin thanh toán và trạng thái", "sections": {"contract": "<PERSON><PERSON>ông Tin <PERSON>", "paymentDetails": "<PERSON>"}, "fields": {"contract": "<PERSON><PERSON><PERSON>", "amount": "Số Tiền", "paymentDate": "<PERSON><PERSON><PERSON>", "paymentType": "<PERSON><PERSON><PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON>", "referenceNumber": "<PERSON><PERSON>", "status": "Trạng <PERSON>", "description": "<PERSON><PERSON>"}, "placeholders": {"contract": "<PERSON><PERSON><PERSON> h<PERSON> đồng", "amount": "0.00", "paymentType": "<PERSON><PERSON><PERSON> lo<PERSON>i thanh toán", "paymentMethod": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "referenceNumber": "<PERSON><PERSON><PERSON><PERSON> số tham chiếu", "status": "<PERSON><PERSON><PERSON> trạng thái", "description": "<PERSON><PERSON><PERSON><PERSON> mô tả hoặc ghi chú"}, "paymentTypes": {"rent": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Tiền Đặt Cọc", "lateFee": "<PERSON><PERSON> <PERSON>", "maintenance": "Bảo Trì", "other": "K<PERSON><PERSON><PERSON>"}, "paymentMethods": {"cash": "Tiền Mặt", "bankTransfer": "<PERSON><PERSON><PERSON><PERSON>", "creditCard": "Thẻ <PERSON>", "check": "Séc"}, "status": {"pending": "<PERSON><PERSON>", "completed": "<PERSON><PERSON><PERSON>", "failed": "<PERSON><PERSON><PERSON><PERSON>", "refunded": "<PERSON><PERSON><PERSON>"}, "contractInfo": {"rent": "<PERSON><PERSON><PERSON><PERSON>", "deposit": "Tiền Đặt Cọc", "dueDay": "<PERSON><PERSON><PERSON>"}, "quickFill": {"rent": "<PERSON><PERSON><PERSON>n <PERSON> T<PERSON>ền <PERSON>", "deposit": "<PERSON><PERSON><PERSON><PERSON>"}, "messages": {"createSuccess": "<PERSON><PERSON><PERSON> thanh toán thành công", "updateSuccess": "<PERSON><PERSON><PERSON> nhật thanh toán thành công", "createError": "<PERSON><PERSON><PERSON><PERSON> thể tạo thanh toán", "updateError": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật thanh toán", "deleteSuccess": "<PERSON><PERSON><PERSON>h toán thành công", "deleteError": "<PERSON><PERSON><PERSON><PERSON> thể xóa thanh toán"}}}