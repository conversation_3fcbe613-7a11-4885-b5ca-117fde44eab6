import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
import type { ApiResponse, CreatePayment, PaginatedResponse, Payment } from "../types";

// API functions (connecting to Vietnamese payments API endpoints)
const paymentsApi = {
  // Get all payments
  getPayments: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: Payment["status"];
    payment_type?: Payment["payment_type"];
    contract_id?: string;
    tenant_id?: string;
  }): Promise<PaginatedResponse<Payment>> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.limit) searchParams.append("limit", params.limit.toString());
    if (params?.search) searchParams.append("search", params.search);
    if (params?.status) searchParams.append("status", params.status);
    if (params?.payment_type) searchParams.append("payment_type", params.payment_type);
    if (params?.contract_id) searchParams.append("contract_id", params.contract_id);
    if (params?.tenant_id) searchParams.append("tenant_id", params.tenant_id);

    const response = await fetch(`/api/property-assets/payments?${searchParams.toString()}`);
    if (!response.ok) {
      throw new Error("Failed to fetch payments");
    }

    return response.json();
  },

  // Get payment by ID
  getPayment: async (id: string): Promise<Payment> => {
    const response = await fetch(`/api/property-assets/payments/${id}`);
    if (!response.ok) {
      throw new Error("Failed to fetch payment");
    }

    const result = await response.json();
    return result.data;
  },

  // Create payment
  createPayment: async (data: CreatePayment): Promise<Payment> => {
    const response = await fetch("/api/property-assets/payments", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to create payment");
    }

    const result = await response.json();
    return result.data;
  },

  // Update payment
  updatePayment: async (id: string, data: Partial<CreatePayment>): Promise<Payment> => {
    const response = await fetch(`/api/property-assets/payments/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update payment");
    }

    const result = await response.json();
    return result.data;
  },

  // Delete payment
  deletePayment: async (id: string): Promise<void> => {
    const response = await fetch(`/api/property-assets/payments/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error("Failed to delete payment");
    }
  },
};

// Query keys
export const paymentsKeys = {
  all: ["payments"] as const,
  lists: () => [...paymentsKeys.all, "list"] as const,
  list: (params?: any) => [...paymentsKeys.lists(), params] as const,
  details: () => [...paymentsKeys.all, "detail"] as const,
  detail: (id: string) => [...paymentsKeys.details(), id] as const,
};

// Hooks
export const usePayments = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: Payment["status"];
  payment_type?: Payment["payment_type"];
  contract_id?: string;
  tenant_id?: string;
}) => {
  return useQuery({
    queryKey: paymentsKeys.list(params),
    queryFn: () => paymentsApi.getPayments(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const usePayment = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: paymentsKeys.detail(id),
    queryFn: () => paymentsApi.getPayment(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreatePayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: paymentsApi.createPayment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: paymentsKeys.lists() });
      toast.success("Tạo thanh toán thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi tạo thanh toán: ${error.message}`);
    },
  });
};

export const useUpdatePayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreatePayment> }) =>
      paymentsApi.updatePayment(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: paymentsKeys.lists() });
      queryClient.setQueryData(paymentsKeys.detail(data.id), data);
      toast.success("Cập nhật thanh toán thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi cập nhật thanh toán: ${error.message}`);
    },
  });
};

export const useDeletePayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: paymentsApi.deletePayment,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: paymentsKeys.lists() });
      toast.success("Xóa thanh toán thành công");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi xóa thanh toán: ${error.message}`);
    },
  });
};
