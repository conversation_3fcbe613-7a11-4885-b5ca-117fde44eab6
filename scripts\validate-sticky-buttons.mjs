#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';

async function validateStickyButtons() {
  console.log('🔍 Validating Sticky Bottom Button Implementation...');
  
  const browser = await puppeteer.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1400, height: 900 });
    
    // Navigate directly to the form (assuming session/auth is handled)
    console.log('📋 Opening Properties form...');
    await page.goto('http://localhost:3000/property-assets/properties/new', { 
      waitUntil: 'networkidle2',
      timeout: 10000 
    });
    
    // Wait for form to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Test sticky button behavior
    console.log('🔧 Testing sticky button behavior...');
    
    const stickyButtonInfo = await page.evaluate(() => {
      // Find the sticky footer
      const stickyFooter = document.querySelector('.sticky.bottom-0');
      
      if (!stickyFooter) {
        return { hasSticky: false, error: 'No sticky footer found' };
      }
      
      // Get button information
      const buttons = Array.from(stickyFooter.querySelectorAll('button'));
      const buttonInfo = buttons.map(btn => ({
        text: btn.textContent?.trim(),
        type: btn.type,
        classes: btn.className,
        position: btn.getBoundingClientRect(),
        styles: {
          backgroundColor: window.getComputedStyle(btn).backgroundColor,
          position: window.getComputedStyle(btn).position,
          bottom: window.getComputedStyle(btn).bottom,
          zIndex: window.getComputedStyle(btn).zIndex
        }
      }));
      
      // Check if footer is properly positioned
      const footerRect = stickyFooter.getBoundingClientRect();
      const footerStyles = window.getComputedStyle(stickyFooter);
      
      return {
        hasSticky: true,
        footer: {
          position: footerStyles.position,
          bottom: footerStyles.bottom,
          zIndex: footerStyles.zIndex,
          classes: stickyFooter.className,
          rect: footerRect
        },
        buttons: buttonInfo,
        windowHeight: window.innerHeight
      };
    });
    
    console.log('\n📊 STICKY BUTTON VALIDATION RESULTS:');
    console.log('=' .repeat(50));
    
    if (stickyButtonInfo.hasSticky) {
      console.log('✅ Sticky footer found');
      console.log(`   Position: ${stickyButtonInfo.footer.position}`);
      console.log(`   Bottom: ${stickyButtonInfo.footer.bottom}`);
      console.log(`   Z-Index: ${stickyButtonInfo.footer.zIndex}`);
      console.log(`   Footer bottom position: ${Math.round(stickyButtonInfo.footer.rect.bottom)}px`);
      console.log(`   Window height: ${stickyButtonInfo.windowHeight}px`);
      
      const isAtBottom = Math.abs(stickyButtonInfo.footer.rect.bottom - stickyButtonInfo.windowHeight) < 5;
      console.log(`   Properly positioned at bottom: ${isAtBottom ? '✅ YES' : '❌ NO'}`);
      
      console.log(`\n🎯 Buttons found: ${stickyButtonInfo.buttons.length}`);
      stickyButtonInfo.buttons.forEach((btn, i) => {
        console.log(`   ${i + 1}. "${btn.text}" (${btn.type})`);
        console.log(`      Background: ${btn.styles.backgroundColor}`);
        console.log(`      Position: ${Math.round(btn.position.left)}px, ${Math.round(btn.position.top)}px`);
      });
      
    } else {
      console.log('❌ Sticky footer not found');
      console.log(`   Error: ${stickyButtonInfo.error}`);
    }
    
    // Test scrolling behavior
    console.log('\n🔄 Testing scroll behavior...');
    
    // Scroll to middle of form
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight / 2);
    });
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const middleScrollInfo = await page.evaluate(() => {
      const stickyFooter = document.querySelector('.sticky.bottom-0');
      if (!stickyFooter) return { visible: false };
      
      const rect = stickyFooter.getBoundingClientRect();
      return {
        visible: rect.bottom <= window.innerHeight && rect.top >= 0,
        bottom: rect.bottom,
        windowHeight: window.innerHeight
      };
    });
    
    console.log(`   Buttons visible during scroll: ${middleScrollInfo.visible ? '✅ YES' : '❌ NO'}`);
    
    // Take screenshot
    const screenshotDir = './screenshots/sticky-validation';
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }
    
    await page.screenshot({ 
      path: `${screenshotDir}/properties-sticky-buttons.png`,
      fullPage: false 
    });
    
    console.log(`\n📸 Screenshot saved: ${screenshotDir}/properties-sticky-buttons.png`);
    
    // Scroll to top and take another screenshot
    await page.evaluate(() => window.scrollTo(0, 0));
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await page.screenshot({ 
      path: `${screenshotDir}/properties-form-top.png`,
      fullPage: false 
    });
    
    console.log(`📸 Top screenshot saved: ${screenshotDir}/properties-form-top.png`);
    
    // Final validation summary
    const isCompliant = stickyButtonInfo.hasSticky && 
                       middleScrollInfo.visible &&
                       stickyButtonInfo.buttons.length >= 2;
    
    console.log('\n🏆 FINAL VALIDATION RESULT:');
    console.log('=' .repeat(40));
    console.log(`Sticky Bottom Buttons: ${isCompliant ? '✅ COMPLIANT' : '❌ NON-COMPLIANT'}`);
    
    if (isCompliant) {
      console.log('✅ Properties form now has sticky bottom buttons matching OneX ERP standards!');
    } else {
      console.log('❌ Issues found with sticky button implementation');
    }
    
    console.log('\n⏳ Keeping browser open for 8 seconds for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 8000));
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
  } finally {
    await browser.close();
  }
}

validateStickyButtons().catch(console.error);