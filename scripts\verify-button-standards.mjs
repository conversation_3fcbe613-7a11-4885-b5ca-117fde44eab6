#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';
import { navigateWithAuth } from './utils/auth.mjs';

async function verifyButtonStandards() {
  console.log('🔍 Verifying form button standards across Properties and Products forms...');
  
  const browser = await puppeteer.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1400, height: 900 });
    
    // Create screenshots directory
    if (!fs.existsSync('./screenshots/standards-verification')) {
      fs.mkdirSync('./screenshots/standards-verification', { recursive: true });
    }
    
    const results = {
      properties: null,
      products: null,
      comparison: null
    };
    
    // Function to analyze form buttons
    const analyzeFormButtons = async (url, formName) => {
      console.log(`\n📋 Analyzing ${formName} form at ${url}`);
      
      try {
        // Use authenticated navigation
        const navigationSuccess = await navigateWithAuth(page, url);
        if (!navigationSuccess) {
          throw new Error(`Failed to navigate to ${url} with authentication`);
        }
        
        // Wait a bit for the page to fully render
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Scroll to bottom to see buttons
        await page.evaluate(() => {
          window.scrollTo(0, document.body.scrollHeight);
        });
        
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Find and analyze form buttons
        const buttonData = await page.evaluate(() => {
          const formButtons = Array.from(document.querySelectorAll('button')).filter(btn => {
            const text = btn.textContent?.trim();
            return text === 'Cancel' || text === 'Add' || text === 'Create' || text === 'Update' || 
                   text === 'Save' || btn.type === 'submit' || btn.form;
          });
          
          return formButtons.map(btn => {
            const rect = btn.getBoundingClientRect();
            const styles = window.getComputedStyle(btn);
            
            return {
              text: btn.textContent?.trim(),
              type: btn.type,
              classes: btn.className,
              position: {
                top: rect.top,
                left: rect.left,
                bottom: rect.bottom,
                right: rect.right,
                visible: btn.offsetWidth > 0 && btn.offsetHeight > 0
              },
              styles: {
                backgroundColor: styles.backgroundColor,
                color: styles.color,
                width: styles.width,
                height: styles.height,
                borderRadius: styles.borderRadius,
                padding: styles.padding,
                margin: styles.margin
              },
              parent: {
                classes: btn.parentElement?.className || '',
                position: btn.parentElement ? window.getComputedStyle(btn.parentElement).position : ''
              }
            };
          });
        });
        
        // Take screenshot of button area
        const screenshotPath = `./screenshots/standards-verification/${formName}-buttons.png`;
        await page.screenshot({ 
          path: screenshotPath,
          clip: {
            x: 0,
            y: Math.max(0, await page.evaluate(() => document.body.scrollHeight - 400)),
            width: 1400,
            height: 400
          }
        });
        
        console.log(`📸 Screenshot saved: ${screenshotPath}`);
        
        return {
          success: true,
          buttons: buttonData,
          screenshot: screenshotPath
        };
        
      } catch (error) {
        console.error(`❌ Failed to analyze ${formName} form:`, error.message);
        return {
          success: false,
          error: error.message,
          buttons: []
        };
      }
    };
    
    // Test Properties form (skip login, go directly)
    results.properties = await analyzeFormButtons(
      'http://localhost:3000/property-assets/properties/new',
      'Properties'
    );
    
    // Test Products form  
    results.products = await analyzeFormButtons(
      'http://localhost:3000/products/new',
      'Products'
    );
    
    // Generate comparison report
    console.log('\n📊 BUTTON STANDARDS VERIFICATION REPORT');
    console.log('=' .repeat(60));
    
    if (results.properties.success && results.products.success) {
      const propertiesButtons = results.properties.buttons;
      const productsButtons = results.products.buttons;
      
      console.log('\n🎯 PROPERTIES FORM BUTTONS:');
      propertiesButtons.forEach((btn, i) => {
        console.log(`  ${i + 1}. "${btn.text}" (${btn.type})`);
        console.log(`     Background: ${btn.styles.backgroundColor}`);
        console.log(`     Color: ${btn.styles.color}`);
        console.log(`     Size: ${btn.styles.width} x ${btn.styles.height}`);
        console.log('');
      });
      
      console.log('\n🎯 PRODUCTS FORM BUTTONS:');
      productsButtons.forEach((btn, i) => {
        console.log(`  ${i + 1}. "${btn.text}" (${btn.type})`);
        console.log(`     Background: ${btn.styles.backgroundColor}`);
        console.log(`     Color: ${btn.styles.color}`);
        console.log(`     Size: ${btn.styles.width} x ${btn.styles.height}`);
        console.log('');
      });
      
      // Compare primary action buttons (Add/Create/Submit)
      const propertiesPrimary = propertiesButtons.find(btn => 
        btn.text === 'Add' || btn.text === 'Create' || btn.type === 'submit'
      );
      const productsPrimary = productsButtons.find(btn => 
        btn.text === 'Add' || btn.text === 'Create' || btn.type === 'submit'
      );
      
      console.log('\n🔍 PRIMARY BUTTON COMPARISON:');
      if (propertiesPrimary && productsPrimary) {
        const bgMatch = propertiesPrimary.styles.backgroundColor === productsPrimary.styles.backgroundColor;
        const colorMatch = propertiesPrimary.styles.color === productsPrimary.styles.color;
        
        console.log(`✅ Background Color Match: ${bgMatch ? 'YES' : 'NO'}`);
        console.log(`   Properties: ${propertiesPrimary.styles.backgroundColor}`);
        console.log(`   Products: ${productsPrimary.styles.backgroundColor}`);
        
        console.log(`✅ Text Color Match: ${colorMatch ? 'YES' : 'NO'}`);
        console.log(`   Properties: ${propertiesPrimary.styles.color}`);
        console.log(`   Products: ${productsPrimary.styles.color}`);
        
        results.comparison = {
          backgroundColorMatch: bgMatch,
          textColorMatch: colorMatch,
          standardsCompliant: bgMatch && colorMatch
        };
      }
      
      // Compare cancel buttons
      const propertiesCancel = propertiesButtons.find(btn => btn.text === 'Cancel');
      const productsCancel = productsButtons.find(btn => btn.text === 'Cancel');
      
      if (propertiesCancel && productsCancel) {
        const cancelBgMatch = propertiesCancel.styles.backgroundColor === productsCancel.styles.backgroundColor;
        console.log(`\n🔍 CANCEL BUTTON COMPARISON:`);
        console.log(`✅ Background Color Match: ${cancelBgMatch ? 'YES' : 'NO'}`);
        console.log(`   Properties: ${propertiesCancel.styles.backgroundColor}`);
        console.log(`   Products: ${productsCancel.styles.backgroundColor}`);
      }
      
    } else {
      console.log('❌ Could not complete comparison due to failed form analysis');
    }
    
    // Save detailed report
    const reportPath = './screenshots/standards-verification/verification-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(results, null, 2));
    console.log(`\n📄 Detailed report saved: ${reportPath}`);
    
    console.log('\n⏳ Keeping browser open for 5 seconds for manual inspection...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
  } finally {
    await browser.close();
  }
}

verifyButtonStandards().catch(console.error);