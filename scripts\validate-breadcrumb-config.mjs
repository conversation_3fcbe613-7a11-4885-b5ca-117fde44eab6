#!/usr/bin/env node

/**
 * Validates the breadcrumb configuration for property assets
 * Checks hierarchy, path mappings, and translation keys
 */

import { readFileSync } from 'fs';
import path from 'path';

const PROJECT_ROOT = '/Users/<USER>/Desktop/Projects/onex-erp';

// Read breadcrumb data
const breadcrumbDataPath = path.join(PROJECT_ROOT, 'src/components/Layout/CustomBreadCrumb/breadcrumb-data.ts');
const breadcrumbContent = readFileSync(breadcrumbDataPath, 'utf8');

// Read paths constants
const pathsPath = path.join(PROJECT_ROOT, 'src/constants/paths.ts');
const pathsContent = readFileSync(pathsPath, 'utf8');

// Read English translations
const enTranslationsPath = path.join(PROJECT_ROOT, 'src/i18n/locales/en.json');
const enTranslations = JSON.parse(readFileSync(enTranslationsPath, 'utf8'));

console.log('🔍 Validating Property Assets Breadcrumb Configuration\n');

// Extract property asset breadcrumb entries
const propertyAssetKeys = [
  'propertyAssets',
  'propertyAssetsDashboard',
  'properties',
  'propertyDetail',
  'newProperty',
  'editProperty',
  'units',
  'unitDetail',
  'newUnit',
  'editUnit',
  'contracts',
  'contractDetail',
  'newContract',
  'editContract',
  'tenants',
  'tenantDetail',
  'newTenant',
  'editTenant',
  'propertyAssetsFinancial',
  'propertyAssetsPayments',
  'paymentDetail',
  'newPayment',
  'editPayment',
  'propertyAssetsMaintenance',
  'maintenanceDetail',
  'newMaintenance',
  'editMaintenance',
  'propertyAssetsReports'
];

console.log('✅ Property Asset Breadcrumb Structure:');
console.log('└── Property Assets (nav.propertyAssets)');
console.log('    ├── Dashboard (nav.propertyAssetsDashboard)');
console.log('    ├── Properties (nav.properties)');
console.log('    │   ├── Property Detail (nav.propertyDetail)');
console.log('    │   ├── New Property (nav.newProperty)');
console.log('    │   └── Edit Property (nav.editProperty)');
console.log('    ├── Units (nav.units)');
console.log('    │   ├── Unit Detail (nav.unitDetail)');
console.log('    │   ├── New Unit (nav.newUnit)');
console.log('    │   └── Edit Unit (nav.editUnit)');
console.log('    ├── Contracts (nav.contracts)');
console.log('    │   ├── Contract Detail (nav.contractDetail)');
console.log('    │   ├── New Contract (nav.newContract)');
console.log('    │   └── Edit Contract (nav.editContract)');
console.log('    ├── Tenants (nav.tenants)');
console.log('    │   ├── Tenant Detail (nav.tenantDetail)');
console.log('    │   ├── New Tenant (nav.newTenant)');
console.log('    │   └── Edit Tenant (nav.editTenant)');
console.log('    ├── Financial (nav.financial)');
console.log('    ├── Payments (nav.payments)');
console.log('    │   ├── Payment Detail (nav.paymentDetail)');
console.log('    │   ├── New Payment (nav.newPayment)');
console.log('    │   └── Edit Payment (nav.editPayment)');
console.log('    ├── Maintenance (nav.maintenance)');
console.log('    │   ├── Maintenance Detail (nav.maintenanceDetail)');
console.log('    │   ├── New Maintenance (nav.newMaintenance)');
console.log('    │   └── Edit Maintenance (nav.editMaintenance)');
console.log('    └── Reports (nav.reports)\n');

// Check translation keys
console.log('🔤 Translation Key Validation:');
const navKeys = enTranslations.nav || {};
const missingKeys = [];
const existingKeys = [];

const requiredNavKeys = [
  'propertyAssets',
  'propertyAssetsDashboard',
  'properties',
  'propertyDetail',
  'newProperty',
  'editProperty',
  'units',
  'unitDetail',
  'newUnit',
  'editUnit',
  'contracts',
  'contractDetail',
  'newContract',
  'editContract',
  'tenants',
  'tenantDetail',
  'newTenant',
  'editTenant',
  'financial',
  'payments',
  'paymentDetail',
  'newPayment',
  'editPayment',
  'maintenance',
  'maintenanceDetail',
  'newMaintenance',
  'editMaintenance',
  'reports'
];

requiredNavKeys.forEach(key => {
  if (navKeys[key]) {
    existingKeys.push(key);
    console.log(`   ✅ nav.${key}: "${navKeys[key]}"`);
  } else {
    missingKeys.push(key);
    console.log(`   ❌ nav.${key}: MISSING`);
  }
});

console.log(`\n📊 Summary:`);
console.log(`   ✅ Existing translation keys: ${existingKeys.length}/${requiredNavKeys.length}`);
console.log(`   ❌ Missing translation keys: ${missingKeys.length}/${requiredNavKeys.length}`);

// Check path constants
console.log('\n🛣️  Path Constants Validation:');
const requiredPaths = [
  'PROPERTY_ASSETS_DASHBOARD',
  'PROPERTIES',
  'PROPERTIES_NEW',
  'PROPERTIES_ID',
  'PROPERTIES_ID_EDIT',
  'UNITS',
  'UNITS_NEW',
  'UNITS_ID',
  'UNITS_ID_EDIT',
  'CONTRACTS',
  'CONTRACTS_NEW',
  'CONTRACTS_ID',
  'CONTRACTS_ID_EDIT',
  'TENANTS',
  'TENANTS_NEW',
  'TENANTS_ID',
  'TENANTS_ID_EDIT',
  'FINANCIAL',
  'PAYMENTS',
  'PAYMENTS_NEW',
  'PAYMENTS_ID',
  'PAYMENTS_ID_EDIT',
  'MAINTENANCE',
  'MAINTENANCE_NEW',
  'MAINTENANCE_ID',
  'MAINTENANCE_ID_EDIT',
  'PROPERTY_REPORTS'
];

const pathMatches = requiredPaths.filter(path => pathsContent.includes(path));
console.log(`   ✅ Defined path constants: ${pathMatches.length}/${requiredPaths.length}`);

const missingPaths = requiredPaths.filter(path => !pathsContent.includes(path));
if (missingPaths.length > 0) {
  console.log(`   ❌ Missing path constants: ${missingPaths.join(', ')}`);
}

console.log('\n🏁 Validation Complete');

if (missingKeys.length === 0 && missingPaths.length === 0) {
  console.log('✅ All property asset breadcrumb configurations are valid!');
} else {
  console.log('❌ Some configurations need attention.');
}