import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import {
  DateColumn,
  ImageColumn,
  TextColumn,
} from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { authProtectedPaths } from "@/constants/paths";

import { Property } from "../../types";

export const columns = (
  useDeletePropertyMutation: UseMutationResult<any, any, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<Property>[] => [
  {
    id: "property",
    accessorKey: "property",
    header: t("pages.properties.headers.propertyInfo"),
    sorter: true,
    isMainColumn: true,
    sortKey: "name",
    cell: ({ row }: { row: Row<Property> }) => {
      const property = row.original;
      return (
        <div className="flex items-center gap-4 truncate">
          <ImageColumn
            src={property?.images?.[0]?.url || ""}
            alt={property?.name}
            width={0}
            iszoomable={true}
            height={0}
            sizes="100vw"
            className="flex size-10 items-center justify-center overflow-hidden rounded bg-muted object-contain"
          />
          <div className="flex flex-auto flex-col justify-between gap-1 truncate">
            <TextColumn text={property?.name} className="font-medium" />
            <TextColumn text={property?.owner_name} className="text-xs text-muted-foreground" />
          </div>
        </div>
      );
    },
  },
  {
    id: "address",
    accessorKey: "address",
    sorter: true,
    sortKey: "address.city",
    header: t("pages.properties.headers.address"),
    cell: ({ row }: { row: Row<Property> }) => {
      const address = row?.original?.address;
      const fullAddress = address
        ? `${address.street}, ${address.city}, ${address.state} ${address.zip_code}`
        : "";
      return <TextColumn text={fullAddress} className="max-w-xs truncate" />;
    },
  },
  {
    id: "property_type",
    accessorKey: "property_type",
    sorter: true,
    sortKey: "property_type",
    header: t("pages.properties.headers.type"),
    cell: ({ row }: { row: Row<Property> }) => {
      const type = row?.original?.property_type;
      const typeLabel =
        type === "residential"
          ? t("pages.properties.types.residential")
          : type === "commercial"
            ? t("pages.properties.types.commercial")
            : type === "mixed"
              ? t("pages.properties.types.mixed")
              : type;
      return <TextColumn text={typeLabel} />;
    },
  },
  {
    id: "total_units",
    accessorKey: "total_units",
    sorter: true,
    sortKey: "total_units",
    header: t("pages.properties.headers.totalUnits"),
    cell: ({ row }: { row: Row<Property> }) => (
      <TextColumn text={row?.original?.total_units?.toString()} />
    ),
  },
  {
    id: "status",
    accessorKey: "status",
    sorter: true,
    sortKey: "status",
    header: t("pages.properties.headers.status"),
    cell: ({ row }: { row: Row<Property> }) => {
      const status = row?.original?.status;
      const statusClass = status === "active" ? "text-success" : "text-muted-foreground";
      const statusLabel =
        status === "active" ? t("common.status.active") : t("common.status.inactive");
      return <TextColumn text={statusLabel} className={statusClass} />;
    },
  },
  {
    id: "lastUpdated",
    accessorKey: "lastUpdated",
    sorter: true,
    sortKey: "updated_at",
    header: t("pages.properties.headers.updatedAt"),
    cell: ({ row }: { row: Row<Property> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.properties.headers.actions"),
    cell: ({ row }: { row: Row<Property> }) => (
      <ActionCell
        useDeletePropertyMutation={useDeletePropertyMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];

const ActionCell = ({
  useDeletePropertyMutation,
  row,
  isDeleting,
}: {
  useDeletePropertyMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Property>;
}) => {
  const router = useRouter();
  const property = row.original;
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(authProtectedPaths.PROPERTIES_ID.replace(":id", property.id) as any);
    });
  }, [router, property.id]);

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.PROPERTIES_ID_EDIT.replace(":id", property.id) as any);
  }, [router, property.id]);

  const handleDelete = useCallback(async () => {
    return useDeletePropertyMutation.mutateAsync(property.id);
  }, [useDeletePropertyMutation, property.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};
