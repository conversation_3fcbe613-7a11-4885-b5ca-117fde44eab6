export const AUTH_ENDPOINTS = {
  LOGIN: "/auth/login",
  REFRESH_TOKEN: "/auth/refresh",
  FORGOT: "/auth/forgot",
  VERIFY_CODE_RESET: "/auth/confirm_reset_password_code",
  NEW_PASSWORD: "/auth/new_password",
  CONFIRM: "/auth/confirm",
  GET_INFO: "/auth/user_info",
  CHANGE_PASSWORD: "/auth/change_password",
  REGISTER: "/auth/register",
  VERIFY_CODE: "/auth/confirm",
  RESEND_CODE: "/auth/resend_code",
} as const;

export const REPORT_ENDPOINTS = {
  DASHBOARD_SUMMARY: "/report/onexbots/onexbots_dashboard_summary",
  ACCURACY_RATE: "/report/onexbots/get_accuracy_rate_report",
  RESPONSE_TIME: "/report/onexbots/top_response_time_report",
} as const;

export const SAVE_FILTER_ENDPOINTS = {
  GET_LIST: "/filter/filters",
  CREATE: "/filter/filters",
  UPDATE: "/filter/filters",
  DELETE: "/filter/filters",
} as const;

export const PRODUCT_ENDPOINTS = {
  LIST: "/product/products",
  CREATE: "/product/products",
  UPDATE: "/product/products/:id",
  DELETE: "/product/products/:id",
  GET_BY_ID: "/product/products/:id",
  LIST_DELETE: "product/products/bulk-delete",

  // Additional product-related endpoints
  BRANDS: "/product/brands",
  CATEGORIES: "/product/categories",
  VARIANTS: "/product/variants",
  UNITS: "/product/units",
} as const;

export const VARIANT_ENDPOINTS = {
  LIST: "/product/variants",
  DELETE: "/product/variants/:id",
} as const;

export const BRAND_ENDPOINTS = {
  LIST: "/product/brands",
  CREATE: "/product/brands",
  UPDATE: "/product/brands/:id",
  DELETE: "/product/brands/:id",
} as const;

export const MEDIA_ENDPOINTS = {
  UPLOAD_IMAGE: "/media/images/upload",
} as const;

export const AI_ENDPOINTS = {
  OPTIMIZE: "/ai/product_optimizer",
} as const;

export const ORDER_ENDPOINTS = {
  LIST: "/order/orders",
  CREATE: "order/orders",
  UPDATE: "/order/orders/:id",
  GET_BY_ID: "order/orders/:id",
} as const;

export const INTEGRATION_ENDPOINTS = {
  SYNC_RECORD_LIST: "/flows/connections/list_sync_records",
  SYNC_RECORD_DETAIL: "/flows/connections/get_sync_records",
  FETCH_EVENT_LIST: "/flows/connections/list_fetch_events",
  FETCH_EVENT_DETAIL: (id: string) => `/flows/connections/get_fetch_events/${id}`,
  CONNECTION_LIST: "/connections/list",
  CONNECTION_UPDATE_STATUS: (id: string) =>
    `/connections/connections/${id}/switch_connection_status`,
  CONNECTION_SETUP_FIELDS: (channelKey: string) => `/connections/setup_fields/${channelKey}`,
} as const;

export const MAPPING_PRODUCT_ENDPOINTS = {
  LIST: "/flows/mappings/product",
  UNMAP: "/flows/mappings/unmap",
  MAP: "/flows/mappings/map",
  DETAIL: "/flows/mappings/product/details",
} as const;

export const PRODUCT_DESTINATION_DATA = {
  LIST: (connectionId: string) => `flows/connections/${connectionId}/destination_data/product`,
  DETAIL: (connectionId: string, productId: string) =>
    `flows/connections/${connectionId}/destination_data/product/${productId}`,
} as const;

export const PRODUCT_ATTRIBUTE_ENDPOINTS = {
  LIST: (connectionId: string) => `flows/${connectionId}/product/attributes`,
  DETAIL: (connectionId: string) => `flows/${connectionId}/product/mapping_attributes`,
  UPDATE: (connectionId: string) => `flows/${connectionId}/product/mapping_attributes`,
  DELETE: (connectionId: string) => `flows/${connectionId}/product/mapping_attributes`,
} as const;

export const TRANSFORMATION_ENDPOINTS = {
  LIST: "/flows/mappings/transformations",
  HANDLE_TRANSFORMATION: "/flows/mappings/transform",
} as const;

export const SYNC_PRODUCT_ENDPOINTS = {
  SYNC: (connectionId: string) => `/flows/sync_mapping/${connectionId}/product`,
} as const;

export const CHANNEL_ENDPOINTS = {
  LIST: "/connections/channels",
  CONNECTION_SETUP_FIELDS: (channelKey: string) => `/connections/setup_fields/${channelKey}`,
  CONNECTION_SETUP: (connectionType: string) => `/connections/setup/${connectionType}`,
  GET_CONNECTION: (connectionId: string) => `/connections/connection/${connectionId}`,
  INSTALL_CONNECTION: "/connections/install",
  CONNECTION_SYNC_SETTINGS: `/connections/connection/synchronization`,
  LIST_PLANS: "/connections/list_plans",
  SUBSCRIBE_PLAN: "/connections/subscribe_plan",
  SUBSCRIPTION_CALLBACK: (connectionId: string, chargeId: string) =>
    `/subscription_callback/${connectionId}/${chargeId}`,
  SETUP_DEFAULT_CONNECTION: (connectionType: string) =>
    `/connections/setup_default/${connectionType}`,
  GET_DEFAULT_CONNECTIONS: (connectionType: string) =>
    `/connections/connections/default/${connectionType}`,
} as const;

export const MAPPING_BOT_ENDPOINTS = {
  GET_PAGES: (botId: string, connectionId: string) =>
    `/connections/mapping/channel/bot/${botId}/${connectionId}/pages`,
  CREATE_MAPPING: (botId: string, connectionId: string, channelId: string) =>
    `/connections/mapping/channel/bot/${botId}/${connectionId}/${channelId}`,
  DELETE_MAPPING: (connectionId: string, channelId: string) =>
    `/connections/mapping/channel/bot/${connectionId}/${channelId}`,
};

export const VERSION_ENDPOINTS = {
  GET_VERSION: "/version/get_version",
} as const;

export const KNOWLEDGE_ENDPOINTS = {
  LIST: "/onexbots/knowledge",
  CREATE: "/onexbots/knowledge",
  GET_BY_ID: (id: string) => `/onexbots/knowledge/${id}`,
  UPDATE: (id: string) => `/onexbots/knowledge/${id}`,
  DELETE: (id: string) => `/onexbots/knowledge/${id}`,
  CREATE_KNOWLEDGE_BY_URL: "/onexbots/knowledge/url",
  CREATE_KNOWLEDGE_BY_FILE: "/onexbots/knowledge/file",
  CREATE_KNOWLEDGE_BY_TEXT: "/onexbots/knowledge/text",
  GET_UPLOAD_URLS: "/onexbots/knowledge/get_upload_urls",
} as const;

export const SETTING_ENDPOINTS = "/settings/settings" as const;

export const DEPARTMENT_ENDPOINTS = {
  LIST: "/onexbots/departments",
  GET_BY_ID: "/onexbots/departments/:id",
  CREATE: "/onexbots/departments",
  UPDATE: "/onexbots/departments/:id",
} as const;

export const CONVERSATION_ENDPOINTS = {
  LIST: "/onexbots/conversations",
  GET_LIST_MESSENGER: (id: string) => `/onexbots/conversations/${id}/messages`,
  CHAT_MESSENGER: (id: string) => `/api/v1/staff/staff/${id}/chat/stream`,
  CREATE_CONVERSATION: "/onexbots/public/conversations",
  CREATE_MESSAGE: (id: string) => `/onexbots/conversations/${id}/messages`,
};
export const TASK_ENDPOINTS = {
  LIST: "/onexbots/tasks",
  GET_BY_ID: "/onexbots/tasks/:id",
  CREATE: "/onexbots/tasks",
  UPDATE: "/onexbots/tasks/:id",
  DELETE: "/onexbots/tasks/:id",
} as const;

export const STAFF_ENDPOINTS = {
  LIST: "/onexbots/virtual-staff",
  DETAIL: "/onexbots/virtual-staff/:id",
  PUBLIC_DETAIL: "/onexbots/public/virtual-staff/:id",
  CREATE: "/onexbots/virtual-staff",
  UPDATE: "/onexbots/virtual-staff/:id",
  DELETE: "/onexbots/virtual-staff/:id",
} as const;

export const ONBOARDING_ENDPOINTS = {
  ONBOARDING_GET: "/onexbots/onboardings",
  ONBOARDING_SUBMIT: "/onexbots/onboardings",
} as const;

export const CRM_ENDPOINTS = {
  STAGES: "/onexbots/crm/stages",
  REORDER_STAGES: (stageId: string) => `/onexbots/crm/stages/${stageId}/reorder`,
  UPDATE_STAGE: (stageId: string) => `/onexbots/crm/stages/${stageId}`,
  OPPORTUNITIES: `/onexbots/crm/opportunities`,
  OPPORTUNITY: (opportunityId: string) => `/onexbots/crm/opportunities/${opportunityId}`,
  UPDATE_OPPORTUNITY: (opportunityId: string) => `/onexbots/crm/opportunities/${opportunityId}`,
  DELETE_OPPORTUNITY: (opportunityId: string) => `/onexbots/crm/opportunities/${opportunityId}`,
  OPPORTUNITY_HISTORY: (id: string) => `/onexbots/crm/opportunities/${id}/history`,
  CREATE_OPPORTUNITY: `/onexbots/crm/opportunities`,
  RE_ORDER_OPPORTUNITY: (opportunityId: string) =>
    `/onexbots/crm/opportunities/${opportunityId}/reorder`,
  ACTIVITY_TYPES: "/onexbots/crm/activity_types",
  SCHEDULE_ACTIVITIES: "/onexbots/crm/schedule_activities",
  UPDATE_SCHEDULE_ACTIVITY: (activityId: string) =>
    `/onexbots/crm/schedule_activities/${activityId}`,
  DELETE_SCHEDULE_ACTIVITY: (activityId: string) =>
    `/onexbots/crm/schedule_activities/${activityId}`,
  OPPORTUNITY_COMMENTS: "/onexbots/crm/opportunity-comments",
  UPDATE_OPPORTUNITY_STATUS: (opportunityId: string) =>
    `/onexbots/crm/opportunities/${opportunityId}/status`,
} as const;
export const IMPORT_ENDPOINTS = {
  IMPORT_PRODUCT_BY_XLSX: "/import/import_product_by_xlsx_file",
  SCHEDULE_ACTIVITIES: "/onexbots/crm/schedule_activities",
} as const;

export const USERS_ENDPOINTS = {
  LIST: "/admin/users",
} as const;

export const PROMPT_EXPERIMENT_ENDPOINTS = {
  TEST: (staff_id: string) => `/api/v1/langfuse/langfuse/${staff_id}/test-prompt-experiment`,
} as const;

export const PROPERTY_ASSETS_ENDPOINTS = {
  // Properties
  PROPERTIES: "/api/property-assets/properties",
  CREATE_PROPERTY: "/api/property-assets/properties",
  UPDATE_PROPERTY: "/api/property-assets/properties/:id",
  DELETE_PROPERTY: "/api/property-assets/properties/:id",
  GET_PROPERTY: "/api/property-assets/properties/:id",

  // Units
  UNITS: "/api/property-assets/units",
  CREATE_UNIT: "/api/property-assets/units",
  UPDATE_UNIT: "/api/property-assets/units/:id",
  DELETE_UNIT: "/api/property-assets/units/:id",
  GET_UNIT: "/api/property-assets/units/:id",
  PROPERTY_UNITS: "/api/property-assets/properties/:propertyId/units",

  // Tenants
  TENANTS: "/api/property-assets/tenants",
  CREATE_TENANT: "/api/property-assets/tenants",
  UPDATE_TENANT: "/api/property-assets/tenants/:id",
  DELETE_TENANT: "/api/property-assets/tenants/:id",
  GET_TENANT: "/api/property-assets/tenants/:id",

  // Contracts
  CONTRACTS: "/api/property-assets/contracts",
  CREATE_CONTRACT: "/api/property-assets/contracts",
  UPDATE_CONTRACT: "/api/property-assets/contracts/:id",
  DELETE_CONTRACT: "/api/property-assets/contracts/:id",
  GET_CONTRACT: "/api/property-assets/contracts/:id",
  TENANT_CONTRACTS: "/api/property-assets/tenants/:tenantId/contracts",
  UNIT_CONTRACTS: "/api/property-assets/units/:unitId/contracts",

  // Payments
  PAYMENTS: "/api/property-assets/payments",
  CREATE_PAYMENT: "/api/property-assets/payments",
  UPDATE_PAYMENT: "/api/property-assets/payments/:id",
  DELETE_PAYMENT: "/api/property-assets/payments/:id",
  GET_PAYMENT: "/api/property-assets/payments/:id",
  CONTRACT_PAYMENTS: "/api/property-assets/contracts/:contractId/payments",

  // Maintenance
  MAINTENANCE: "/api/property-assets/maintenance",
  CREATE_MAINTENANCE: "/api/property-assets/maintenance",
  UPDATE_MAINTENANCE: "/api/property-assets/maintenance/:id",
  DELETE_MAINTENANCE: "/api/property-assets/maintenance/:id",
  GET_MAINTENANCE: "/api/property-assets/maintenance/:id",

  // Financial
  FINANCIAL_SUMMARY: "/api/property-assets/financial/summary",
  PROFIT_LOSS_REPORT: "/api/property-assets/financial/profit-loss",
  REVENUE_ANALYTICS: "/api/property-assets/financial/revenue-analytics",
  TRANSACTIONS: "/api/property-assets/financial/transactions",

  // Dashboard
  DASHBOARD_STATS: "/api/property-assets/dashboard/stats",
} as const;

export const ENDPOINTS = {
  AUTH: AUTH_ENDPOINTS,
  PRODUCT: PRODUCT_ENDPOINTS,
  VARIANT: VARIANT_ENDPOINTS,
  BRAND: BRAND_ENDPOINTS,
  ORDER: ORDER_ENDPOINTS,
  MEDIA: MEDIA_ENDPOINTS,
  AI: AI_ENDPOINTS,
  CHANNEL: CHANNEL_ENDPOINTS,
  INTEGRATION_ENDPOINTS: INTEGRATION_ENDPOINTS,
  MAPPING_PRODUCT_ENDPOINTS: MAPPING_PRODUCT_ENDPOINTS,
  PRODUCT_DESTINATION_DATA: PRODUCT_DESTINATION_DATA,
  PRODUCT_ATTRIBUTE_ENDPOINTS: PRODUCT_ATTRIBUTE_ENDPOINTS,
  TRANSFORMATION_ENDPOINTS: TRANSFORMATION_ENDPOINTS,
  VERSION: VERSION_ENDPOINTS,
  SYNC_PRODUCT_ENDPOINTS: SYNC_PRODUCT_ENDPOINTS,
  SETTING: SETTING_ENDPOINTS,
  CONVERSATION: CONVERSATION_ENDPOINTS,
  STAFF: STAFF_ENDPOINTS,
  KNOWLEDGE: KNOWLEDGE_ENDPOINTS,
  TASK: TASK_ENDPOINTS,
  ONBOARDING: ONBOARDING_ENDPOINTS,
  MAPPING_BOT: MAPPING_BOT_ENDPOINTS,
  REPORT: REPORT_ENDPOINTS,
  IMPORT: IMPORT_ENDPOINTS,
  CRM: CRM_ENDPOINTS,
  USERS: USERS_ENDPOINTS,
  PROMPT_EXPERIMENT: PROMPT_EXPERIMENT_ENDPOINTS,
  PROPERTY_ASSETS: PROPERTY_ASSETS_ENDPOINTS,
} as const;
