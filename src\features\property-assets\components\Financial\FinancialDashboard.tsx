"use client";

import { useCallback, useState } from "react";
import Loading from "@/app/loading";
import {
  BarChart3,
  Calendar,
  DollarSign,
  Download,
  Filter,
  Home,
  PieChart,
  TrendingDown,
  TrendingUp,
  Users,
  Wrench,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Line,
  LineChart,
  Pie,
  <PERSON>hart as RechartsPieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { formatNumberShort } from "@/utils/numberFormatter";

import {
  useFinancialSummary,
  useProfitLossReport,
  useRevenueAnalytics,
} from "../../hooks/useFinancial";

interface FinancialDashboardProps {
  propertyId?: string;
}

export function FinancialDashboard({ propertyId }: FinancialDashboardProps) {
  const { t, i18n } = useTranslation();
  const [timeRange, setTimeRange] = useState<"month" | "quarter" | "year">("month");
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");

  const { data: financialSummary, isLoading: loadingSummary } = useFinancialSummary({
    property_id: selectedProperty === "all" ? undefined : selectedProperty,
  });

  const { data: profitLoss, isLoading: loadingProfitLoss } = useProfitLossReport({
    property_id: selectedProperty === "all" ? undefined : selectedProperty,
    time_range: timeRange,
  });

  const { data: revenueAnalytics, isLoading: loadingRevenue } = useRevenueAnalytics({
    property_id: selectedProperty === "all" ? undefined : selectedProperty,
    time_range: timeRange,
  });

  const handleExportReport = useCallback(() => {
    // TODO: Implement financial report export functionality
  }, []);

  // Mock data for charts - replace with actual data
  const monthlyRevenueData = [
    { month: "Jan", revenue: 45000, expenses: 12000, netIncome: 33000 },
    { month: "Feb", revenue: 47000, expenses: 13000, netIncome: 34000 },
    { month: "Mar", revenue: 46000, expenses: 15000, netIncome: 31000 },
    { month: "Apr", revenue: 48000, expenses: 11000, netIncome: 37000 },
    { month: "May", revenue: 50000, expenses: 14000, netIncome: 36000 },
    { month: "Jun", revenue: 52000, expenses: 16000, netIncome: 36000 },
  ];

  const expenseBreakdownData = [
    { name: "Maintenance", value: 35, amount: 15400 },
    { name: "Utilities", value: 25, amount: 11000 },
    { name: "Insurance", value: 20, amount: 8800 },
    { name: "Property Tax", value: 15, amount: 6600 },
    { name: "Other", value: 5, amount: 2200 },
  ];

  const occupancyData = [
    { month: "Jan", occupancy: 92 },
    { month: "Feb", occupancy: 89 },
    { month: "Mar", occupancy: 94 },
    { month: "Apr", occupancy: 91 },
    { month: "May", occupancy: 96 },
    { month: "Jun", occupancy: 93 },
  ];

  // Using CSS custom properties for theme-aware chart colors
  const COLORS = [
    "hsl(var(--primary))",
    "hsl(var(--success))",
    "hsl(var(--warning))",
    "hsl(var(--destructive))",
    "hsl(var(--secondary))",
  ];

  const isLoading = loadingSummary || loadingProfitLoss || loadingRevenue;

  return (
    <div className="space-y-8">
      {/* Header with Controls - Always Visible */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-foreground">
            {t("financial.dashboard.title")}
          </h1>
          <p className="text-sm text-muted-foreground">{t("financial.dashboard.description")}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select
            value={timeRange}
            onValueChange={(value: any) => setTimeRange(value)}
            disabled={isLoading}>
            <SelectTrigger className="w-32 border-input bg-background text-foreground">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">{t("financial.timeRange.month")}</SelectItem>
              <SelectItem value="quarter">{t("financial.timeRange.quarter")}</SelectItem>
              <SelectItem value="year">{t("financial.timeRange.year")}</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleExportReport} variant="outline" disabled={isLoading}>
            <Download className="mr-2 size-4" />
            {t("financial.exportReport")}
          </Button>
        </div>
      </div>

      {/* Content Area - Shows Loading or Dashboard Content */}
      {isLoading ? (
        <div className="flex h-96 items-center justify-center">
          <Loading />
        </div>
      ) : (
        <div className="space-y-8">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between gap-4">
                  <div className="min-w-0 flex-1">
                    <p className="truncate text-sm font-medium text-muted-foreground">
                      {t("financial.metrics.totalRevenue")}
                    </p>
                    <div className="mt-2 flex items-center space-x-2">
                      <p className="truncate text-2xl font-bold text-foreground">
                        {formatNumberShort(
                          financialSummary?.monthly_rental_income,
                          i18n.language,
                          true
                        )}
                      </p>
                      <Badge className="shrink-0 border-success/20 bg-success/10 text-success">
                        <TrendingUp className="mr-1 size-3" />
                        12%
                      </Badge>
                    </div>
                  </div>
                  <div className="shrink-0 rounded-full bg-primary/10 p-3">
                    <DollarSign className="size-6 text-primary" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between gap-4">
                  <div className="min-w-0 flex-1">
                    <p className="truncate text-sm font-medium text-muted-foreground">
                      {t("financial.metrics.netIncome")}
                    </p>
                    <div className="mt-2 flex items-center space-x-2">
                      <p className="truncate text-2xl font-bold text-foreground">
                        {formatNumberShort(
                          financialSummary?.net_monthly_income,
                          i18n.language,
                          true
                        )}
                      </p>
                      <Badge className="shrink-0 border-success/20 bg-success/10 text-success">
                        <TrendingUp className="mr-1 size-3" />
                        8%
                      </Badge>
                    </div>
                  </div>
                  <div className="shrink-0 rounded-full bg-success/10 p-3">
                    <TrendingUp className="size-6 text-success" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between gap-4">
                  <div className="min-w-0 flex-1">
                    <p className="truncate text-sm font-medium text-muted-foreground">
                      {t("financial.metrics.occupancyRate")}
                    </p>
                    <div className="mt-2 flex items-center space-x-2">
                      <p className="truncate text-2xl font-bold text-foreground">
                        {((1 - (financialSummary?.vacancy_rate || 0)) * 100).toFixed(1)}%
                      </p>
                      <Badge className="shrink-0 border-primary/20 bg-primary/10 text-primary">
                        <Home className="mr-1 size-3" />
                        {financialSummary?.occupied_units || 0}/{financialSummary?.total_units || 0}
                      </Badge>
                    </div>
                  </div>
                  <div className="shrink-0 rounded-full bg-secondary/10 p-3">
                    <Home className="size-6 text-secondary-foreground" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between gap-4">
                  <div className="min-w-0 flex-1">
                    <p className="truncate text-sm font-medium text-muted-foreground">
                      {t("financial.metrics.totalExpenses")}
                    </p>
                    <div className="mt-2 flex items-center space-x-2">
                      <p className="truncate text-2xl font-bold text-foreground">
                        {formatNumberShort(financialSummary?.monthly_expenses, i18n.language, true)}
                      </p>
                      <Badge className="shrink-0 border-destructive/20 bg-destructive/10 text-destructive">
                        <TrendingDown className="mr-1 size-3" />
                        3%
                      </Badge>
                    </div>
                  </div>
                  <div className="shrink-0 rounded-full bg-destructive/10 p-3">
                    <Wrench className="size-6 text-destructive" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Revenue Trend */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="size-5" />
                  <span>{t("financial.charts.revenueTrend")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={monthlyRevenueData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip
                      formatter={(value) => [
                        formatNumberShort(value as number, i18n.language, true),
                        "",
                      ]}
                    />
                    <Legend />
                    <Area
                      type="monotone"
                      dataKey="revenue"
                      stackId="1"
                      stroke="#8884d8"
                      fill="#8884d8"
                      fillOpacity={0.3}
                      name={t("financial.revenue")}
                    />
                    <Area
                      type="monotone"
                      dataKey="netIncome"
                      stackId="2"
                      stroke="#82ca9d"
                      fill="#82ca9d"
                      fillOpacity={0.3}
                      name={t("financial.netIncome")}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Expense Breakdown */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <PieChart className="size-5" />
                  <span>{t("financial.charts.expenseBreakdown")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={expenseBreakdownData}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="value"
                      label={({ name, value }) => `${name}: ${value}%`}>
                      {expenseBreakdownData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value, name) => [
                        formatNumberShort(
                          expenseBreakdownData.find((d) => d.name === name)?.amount,
                          i18n.language,
                          true
                        ),
                        name,
                      ]}
                    />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Occupancy and Performance */}
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
            {/* Occupancy Trend */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Home className="size-5" />
                  <span>{t("financial.charts.occupancyTrend")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={250}>
                  <LineChart data={occupancyData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis domain={[80, 100]} />
                    <Tooltip formatter={(value) => [`${value}%`, "Occupancy"]} />
                    <Line
                      type="monotone"
                      dataKey="occupancy"
                      stroke="#8884d8"
                      strokeWidth={3}
                      dot={{ fill: "#8884d8", strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Property Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="size-5" />
                  <span>{t("financial.performance")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="mb-2 flex justify-between text-sm">
                    <span className="text-muted-foreground">{t("financial.profitMargin")}</span>
                    <span className="font-semibold">
                      {profitLoss?.profit_margin ? `${profitLoss.profit_margin.toFixed(1)}%` : "0%"}
                    </span>
                  </div>
                  <Progress value={profitLoss?.profit_margin || 0} className="h-2" />
                </div>

                <Separator />

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("financial.totalProperties")}
                    </span>
                    <span className="font-semibold">{financialSummary?.total_properties || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("financial.totalUnits")}
                    </span>
                    <span className="font-semibold">{financialSummary?.total_units || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("financial.occupiedUnits")}
                    </span>
                    <span className="font-semibold">{financialSummary?.occupied_units || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("financial.vacancyRate")}
                    </span>
                    <span className="font-semibold text-destructive">
                      {financialSummary?.vacancy_rate
                        ? `${(financialSummary.vacancy_rate * 100).toFixed(1)}%`
                        : "0%"}
                    </span>
                  </div>
                </div>

                <Separator />

                <div className="text-center">
                  <p className="mb-2 text-sm text-muted-foreground">
                    {t("financial.ytdPerformance")}
                  </p>
                  <div className="text-2xl font-bold text-foreground">
                    {formatNumberShort(financialSummary?.ytd_net_income, i18n.language, true)}
                  </div>
                  <p className="text-xs text-muted-foreground">{t("financial.netIncome")}</p>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Recent Transactions and Insights */}
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {/* Top Performing Properties */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="size-5" />
                  <span>{t("financial.topProperties")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: "Sunset Apartments", revenue: 15000, growth: 12 },
                    { name: "Downtown Lofts", revenue: 12000, growth: 8 },
                    { name: "Garden View Complex", revenue: 10000, growth: 15 },
                    { name: "Riverside Towers", revenue: 8000, growth: 5 },
                  ].map((property, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between rounded-lg bg-muted/50 p-3">
                      <div>
                        <div className="font-medium text-foreground">{property.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {formatNumberShort(property.revenue, i18n.language, true)}{" "}
                          {t("financial.monthlyRevenue")}
                        </div>
                      </div>
                      <Badge className="border-success/20 bg-success/10 text-success">
                        +{property.growth}%
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Financial Alerts */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="size-5" />
                  <span>{t("financial.alerts.title")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3 rounded-lg border border-warning/20 bg-warning/5 p-3">
                    <div className="rounded bg-warning/10 p-1">
                      <Calendar className="size-4 text-warning" />
                    </div>
                    <div>
                      <div className="font-medium text-warning">
                        {t("financial.alerts.rentDue")}
                      </div>
                      <div className="text-sm text-warning">
                        5 tenants have rent due within 3 days
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 rounded-lg border border-destructive/20 bg-destructive/5 p-3">
                    <div className="rounded bg-destructive/10 p-1">
                      <TrendingDown className="size-4 text-destructive" />
                    </div>
                    <div>
                      <div className="font-medium text-destructive">
                        {t("financial.alerts.highExpenses")}
                      </div>
                      <div className="text-sm text-destructive">
                        Maintenance costs 25% above budget this month
                      </div>
                    </div>
                  </div>

                  <div className="flex items-start space-x-3 rounded-lg border border-primary/20 bg-primary/5 p-3">
                    <div className="rounded bg-primary/10 p-1">
                      <Users className="size-4 text-primary" />
                    </div>
                    <div>
                      <div className="font-medium text-primary">
                        {t("financial.alerts.leaseExpiring")}
                      </div>
                      <div className="text-sm text-primary">
                        3 leases expiring next month - consider renewals
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}
