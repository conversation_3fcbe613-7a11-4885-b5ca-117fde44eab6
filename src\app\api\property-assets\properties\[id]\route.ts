import { NextRequest, NextResponse } from "next/server";

import type { CreateProperty, Property } from "@/features/property-assets/types";

import { propertiesMockData } from "../../mock-data";

// GET /api/property-assets/properties/[id]
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const property = propertiesMockData.find((p) => p.id === id);

    if (!property) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 200));

    return NextResponse.json({
      data: property,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching property:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PUT /api/property-assets/properties/[id]
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    const body: Partial<CreateProperty> = await request.json();

    const propertyIndex = propertiesMockData.findIndex((p) => p.id === id);

    if (propertyIndex === -1) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Handle image transformation if images are being updated
    let transformedImages;
    if (body.images) {
      transformedImages = body.images.map((img, index) => ({
        id: `img_${Date.now()}_${index}`,
        name: img.name,
        url: img.image,
        is_primary: index === 0,
      }));
    }

    // Update property
    const { images: _, layouts: __, ...bodyWithoutImages } = body;
    const updatedProperty: Property = {
      ...propertiesMockData[propertyIndex],
      ...bodyWithoutImages,
      ...(transformedImages && { images: transformedImages }),
      updated_at: new Date().toISOString(),
    };

    propertiesMockData[propertyIndex] = updatedProperty;

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 300));

    return NextResponse.json({
      data: updatedProperty,
      success: true,
      message: "Property updated successfully",
    });
  } catch (error) {
    console.error("Error updating property:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE /api/property-assets/properties/[id]
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const propertyIndex = propertiesMockData.findIndex((p) => p.id === id);

    if (propertyIndex === -1) {
      return NextResponse.json({ error: "Property not found" }, { status: 404 });
    }

    // Remove property from mock data
    propertiesMockData.splice(propertyIndex, 1);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 200));

    return NextResponse.json({
      success: true,
      message: "Property deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting property:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
