#!/usr/bin/env node

import puppeteer from 'puppeteer';

async function testTranslations() {
  console.log('🌐 Testing translation system...');
  
  const browser = await puppeteer.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Login first
    await page.goto('http://localhost:3000/login', { waitUntil: 'networkidle2' });
    
    const usernameInput = await page.$('input[name="email"]') || await page.$('input[type="email"]');
    await usernameInput.type('onexapis_admin');
    
    const passwordInput = await page.$('input[name="password"]') || await page.$('input[type="password"]');
    await passwordInput.type('Admin@123');
    
    const loginButton = await page.$('button[type="submit"]');
    await loginButton.click();
    await page.waitForNavigation({ waitUntil: 'networkidle2' });
    
    // Go to form page
    await page.goto('http://localhost:3000/property-assets/properties/new', { waitUntil: 'networkidle2' });
    
    // Check if translations are working
    const translationTest = await page.evaluate(() => {
      // Check the i18next instance
      const i18n = window.i18n || window.i18next;
      
      // Check if React i18n hook is working
      const hasReactI18n = !!window.ReactI18next;
      
      // Get the actual text content
      const titleElement = document.querySelector('h1');
      const cardTitles = Array.from(document.querySelectorAll('[data-testid], h2, h3, .text-lg')).map(el => ({
        tag: el.tagName,
        text: el.textContent?.trim(),
        classes: el.className
      }));
      
      // Check language setting
      const htmlLang = document.documentElement.lang;
      
      return {
        hasI18n: !!i18n,
        hasReactI18n,
        currentLanguage: i18n?.language || 'unknown',
        htmlLang,
        titleText: titleElement?.textContent?.trim(),
        cardTitles: cardTitles.slice(0, 10), // First 10 elements
        translationKeys: {
          createProperty: i18n?.t ? i18n.t('properties.createProperty') : 'no i18n',
          basicInfo: i18n?.t ? i18n.t('properties.basicInformation') : 'no i18n'
        }
      };
    });
    
    console.log('📊 Translation System Status:');
    console.log(`   i18n Instance: ${translationTest.hasI18n ? '✅' : '❌'}`);
    console.log(`   React i18n: ${translationTest.hasReactI18n ? '✅' : '❌'}`);
    console.log(`   Current Language: ${translationTest.currentLanguage}`);
    console.log(`   HTML Lang: ${translationTest.htmlLang}`);
    console.log(`   Page Title: "${translationTest.titleText}"`);
    
    console.log('\\n🔤 Translation Test:');
    console.log(`   properties.createProperty: "${translationTest.translationKeys.createProperty}"`);
    console.log(`   properties.basicInformation: "${translationTest.translationKeys.basicInfo}"`);
    
    console.log('\\n📝 Page Elements:');
    translationTest.cardTitles.forEach((item, i) => {
      console.log(`   ${i + 1}. ${item.tag}: "${item.text}"`);
    });
    
    // Keep browser open for inspection
    console.log('\\n⏳ Keeping browser open for 15 seconds...');
    await new Promise(resolve => setTimeout(resolve, 15000));
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

testTranslations().catch(console.error);