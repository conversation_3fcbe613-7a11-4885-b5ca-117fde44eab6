#!/usr/bin/env node

/**
 * Reusable authentication utility for Puppeteer scripts
 * Extracted from validate-property-form.mjs for common use
 */

const AUTH_CONFIG = {
  BASE_URL: 'http://localhost:3000',
  LOGIN_URL: 'http://localhost:3000/login',
  CREDENTIALS: {
    username: 'onexapis_admin',
    password: 'Admin@123'
  },
  TIMEOUT: 30000
};

/**
 * Performs login authentication on the given page
 * @param {Page} page - Puppeteer page instance
 * @param {Object} config - Optional configuration override
 * @returns {Promise<boolean>} - Success status
 */
export async function performLogin(page, config = {}) {
  const loginConfig = { ...AUTH_CONFIG, ...config };
  console.log('🔐 Performing authentication...');
  
  try {
    await page.goto(loginConfig.LOGIN_URL, { 
      waitUntil: 'networkidle2', 
      timeout: loginConfig.TIMEOUT 
    });
    
    // Wait for page to load and find login elements
    await page.waitForSelector(
      'input[name="email"], input[type="email"], input[placeholder*="email" i]', 
      { timeout: 10000 }
    );
    
    // Find username/email input
    const usernameInput = await page.$('input[name="email"]') || 
                         await page.$('input[type="email"]') || 
                         await page.$('input[placeholder*="email" i]');
    
    if (!usernameInput) {
      throw new Error('Username input field not found');
    }
    
    // Clear and type username
    await usernameInput.click({ clickCount: 3 });
    await usernameInput.type(loginConfig.CREDENTIALS.username);
    
    // Find and fill password
    const passwordInput = await page.$('input[name="password"]') || 
                         await page.$('input[type="password"]');
    if (!passwordInput) {
      throw new Error('Password input field not found');
    }
    
    await passwordInput.click({ clickCount: 3 });
    await passwordInput.type(loginConfig.CREDENTIALS.password);
    
    // Find and click login button
    const loginButton = await page.$('button[type="submit"]') || 
                       await page.$('button:has-text("Login")') ||
                       await page.$('button:has-text("Sign in")') ||
                       await page.$('input[type="submit"]');
    
    if (!loginButton) {
      throw new Error('Login button not found');
    }
    
    await loginButton.click();
    
    // Wait for navigation after login
    await page.waitForNavigation({ 
      waitUntil: 'networkidle2', 
      timeout: 30000 
    });
    
    console.log('   ✅ Authentication successful');
    return true;
  } catch (error) {
    console.log(`   ❌ Authentication failed: ${error.message}`);
    return false;
  }
}

/**
 * Checks if the current page requires authentication
 * @param {Page} page - Puppeteer page instance
 * @returns {Promise<boolean>} - True if login is required
 */
export async function isAuthRequired(page) {
  const url = page.url();
  return url.includes('/login') || url.includes('/auth');
}

/**
 * Navigates to a protected route with automatic authentication
 * @param {Page} page - Puppeteer page instance
 * @param {string} targetUrl - The protected URL to navigate to
 * @param {Object} config - Optional configuration override
 * @returns {Promise<boolean>} - Success status
 */
export async function navigateWithAuth(page, targetUrl, config = {}) {
  const navigationConfig = { ...AUTH_CONFIG, ...config };
  
  try {
    // First attempt to navigate directly
    await page.goto(targetUrl, { 
      waitUntil: 'networkidle2', 
      timeout: navigationConfig.TIMEOUT 
    });
    
    // Check if we were redirected to login
    if (await isAuthRequired(page)) {
      console.log('🔒 Authentication required, logging in...');
      
      // Perform login
      const loginSuccess = await performLogin(page, navigationConfig);
      if (!loginSuccess) {
        return false;
      }
      
      // Navigate to target URL again
      await page.goto(targetUrl, { 
        waitUntil: 'networkidle2', 
        timeout: navigationConfig.TIMEOUT 
      });
    }
    
    console.log(`✅ Successfully navigated to: ${targetUrl}`);
    return true;
    
  } catch (error) {
    console.log(`❌ Navigation failed: ${error.message}`);
    return false;
  }
}

export { AUTH_CONFIG };