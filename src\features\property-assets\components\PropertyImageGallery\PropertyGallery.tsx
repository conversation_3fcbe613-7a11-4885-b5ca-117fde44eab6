"use client";

import { useState } from "react";
import {
  Calendar,
  Camera,
  ChevronLeft,
  ChevronRight,
  Download,
  Edit,
  Eye,
  Filter,
  Grid,
  Image as ImageIcon,
  List,
  Maximize,
  MoreHorizontal,
  Move,
  Plus,
  RotateCw,
  Search,
  Star,
  Tag,
  Trash2,
  Upload,
  User,
  X,
  ZoomIn,
  ZoomOut,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface PropertyGalleryProps {
  propertyId?: string;
  className?: string;
}

export function PropertyGallery({ propertyId, className }: PropertyGalleryProps) {
  const { t } = useTranslation();
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const [isLightboxOpen, setIsLightboxOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState<any>(null);
  const [lightboxIndex, setLightboxIndex] = useState(0);

  // Mock image data
  const images = [
    {
      id: "1",
      filename: "sunset_apartments_exterior.jpg",
      title: "Building Exterior - Main View",
      description: "Front view of the main building with landscaping",
      property: "Sunset Apartments",
      category: "Exterior",
      url: "/api/images/sunset_apartments_exterior.jpg",
      thumbnail: "/api/thumbnails/sunset_apartments_exterior.jpg",
      size: "2.4 MB",
      dimensions: "1920x1080",
      uploadDate: "2024-01-15",
      uploadedBy: "Property Manager",
      isPrimary: true,
      tags: ["exterior", "building", "landscaping", "main"],
      views: 234,
      downloads: 45,
    },
    {
      id: "2",
      filename: "unit_101_living_room.jpg",
      title: "Unit 101 - Living Room",
      description: "Spacious living room with modern furnishings",
      property: "Sunset Apartments",
      category: "Interior",
      url: "/api/images/unit_101_living_room.jpg",
      thumbnail: "/api/thumbnails/unit_101_living_room.jpg",
      size: "1.8 MB",
      dimensions: "1600x1200",
      uploadDate: "2024-01-12",
      uploadedBy: "Photography Team",
      isPrimary: false,
      tags: ["interior", "living room", "unit 101", "furnished"],
      views: 156,
      downloads: 23,
    },
    {
      id: "3",
      filename: "downtown_lofts_lobby.jpg",
      title: "Downtown Lofts - Lobby",
      description: "Modern lobby with reception area",
      property: "Downtown Lofts",
      category: "Common Areas",
      url: "/api/images/downtown_lofts_lobby.jpg",
      thumbnail: "/api/thumbnails/downtown_lofts_lobby.jpg",
      size: "2.1 MB",
      dimensions: "1800x1200",
      uploadDate: "2024-01-10",
      uploadedBy: "Interior Designer",
      isPrimary: true,
      tags: ["lobby", "common area", "reception", "modern"],
      views: 189,
      downloads: 67,
    },
    {
      id: "4",
      filename: "garden_view_pool.jpg",
      title: "Garden View - Pool Area",
      description: "Outdoor pool with deck and seating area",
      property: "Garden View Complex",
      category: "Amenities",
      url: "/api/images/garden_view_pool.jpg",
      thumbnail: "/api/thumbnails/garden_view_pool.jpg",
      size: "3.2 MB",
      dimensions: "2048x1536",
      uploadDate: "2024-01-08",
      uploadedBy: "Property Manager",
      isPrimary: false,
      tags: ["pool", "amenities", "outdoor", "deck"],
      views: 298,
      downloads: 89,
    },
    {
      id: "5",
      filename: "parking_garage_level1.jpg",
      title: "Parking Garage - Level 1",
      description: "Well-lit underground parking with security cameras",
      property: "Sunset Apartments",
      category: "Parking",
      url: "/api/images/parking_garage_level1.jpg",
      thumbnail: "/api/thumbnails/parking_garage_level1.jpg",
      size: "1.5 MB",
      dimensions: "1440x1080",
      uploadDate: "2024-01-05",
      uploadedBy: "Security Team",
      isPrimary: false,
      tags: ["parking", "garage", "security", "underground"],
      views: 78,
      downloads: 12,
    },
    {
      id: "6",
      filename: "rooftop_terrace.jpg",
      title: "Rooftop Terrace - Evening View",
      description: "Rooftop terrace with city skyline view at sunset",
      property: "Downtown Lofts",
      category: "Amenities",
      url: "/api/images/rooftop_terrace.jpg",
      thumbnail: "/api/thumbnails/rooftop_terrace.jpg",
      size: "2.8 MB",
      dimensions: "1920x1280",
      uploadDate: "2024-01-03",
      uploadedBy: "Marketing Team",
      isPrimary: false,
      tags: ["rooftop", "terrace", "skyline", "evening", "amenities"],
      views: 445,
      downloads: 134,
    },
  ];

  const categories = [
    { id: "all", name: "All Categories", count: images.length },
    {
      id: "Exterior",
      name: "Exterior",
      count: images.filter((img) => img.category === "Exterior").length,
    },
    {
      id: "Interior",
      name: "Interior",
      count: images.filter((img) => img.category === "Interior").length,
    },
    {
      id: "Common Areas",
      name: "Common Areas",
      count: images.filter((img) => img.category === "Common Areas").length,
    },
    {
      id: "Amenities",
      name: "Amenities",
      count: images.filter((img) => img.category === "Amenities").length,
    },
    {
      id: "Parking",
      name: "Parking",
      count: images.filter((img) => img.category === "Parking").length,
    },
  ];

  const properties = [
    { id: "all", name: "All Properties" },
    { id: "Sunset Apartments", name: "Sunset Apartments" },
    { id: "Downtown Lofts", name: "Downtown Lofts" },
    { id: "Garden View Complex", name: "Garden View Complex" },
  ];

  const filteredImages = images.filter((image) => {
    const matchesProperty = selectedProperty === "all" || image.property === selectedProperty;
    const matchesCategory = selectedCategory === "all" || image.category === selectedCategory;
    const matchesSearch =
      searchTerm === "" ||
      image.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      image.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      image.tags.some((tag) => tag.toLowerCase().includes(searchTerm.toLowerCase()));

    return matchesProperty && matchesCategory && matchesSearch;
  });

  const openLightbox = (image: any) => {
    setSelectedImage(image);
    setLightboxIndex(filteredImages.findIndex((img) => img.id === image.id));
    setIsLightboxOpen(true);
  };

  const navigateLightbox = (direction: "prev" | "next") => {
    const newIndex =
      direction === "prev"
        ? (lightboxIndex - 1 + filteredImages.length) % filteredImages.length
        : (lightboxIndex + 1) % filteredImages.length;

    setLightboxIndex(newIndex);
    setSelectedImage(filteredImages[newIndex]);
  };

  const totalSize = images.reduce((sum, img) => {
    const size = parseFloat(img.size.replace(/[^\d.]/g, ""));
    return sum + size;
  }, 0);

  const totalViews = images.reduce((sum, img) => sum + img.views, 0);
  const totalDownloads = images.reduce((sum, img) => sum + img.downloads, 0);
  const primaryImages = images.filter((img) => img.isPrimary).length;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Button */}
      <div className="flex justify-end">
        <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
          <DialogTrigger asChild>
            <Button>
              <Upload className="mr-2 size-4" />
              Upload Images
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-lg">
            <DialogHeader>
              <DialogTitle>Upload Property Images</DialogTitle>
              <DialogDescription>Add new images to the property gallery</DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="property">Property</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select property" />
                    </SelectTrigger>
                    <SelectContent>
                      {properties
                        .filter((p) => p.id !== "all")
                        .map((property) => (
                          <SelectItem key={property.id} value={property.id}>
                            {property.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="category">Category</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories
                        .filter((c) => c.id !== "all")
                        .map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div>
                <Label htmlFor="files">Images</Label>
                <div className="rounded-lg border-2 border-dashed border-muted p-8 text-center">
                  <Camera className="mx-auto mb-4 size-12 text-muted-foreground" />
                  <p className="mb-2 text-sm text-muted-foreground">
                    Drag and drop images here, or click to browse
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Supports JPG, PNG, WebP up to 10MB each
                  </p>
                  <Input type="file" multiple accept="image/*" className="hidden" />
                  <Button variant="outline" className="mt-4">
                    Select Images
                  </Button>
                </div>
              </div>
              <div>
                <Label htmlFor="tags">Tags (optional)</Label>
                <Input placeholder="Enter tags separated by commas" />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsUploadOpen(false)}>
                Cancel
              </Button>
              <Button onClick={() => setIsUploadOpen(false)}>Upload Images</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Images</p>
                <p className="text-2xl font-bold">{images.length}</p>
                <p className="text-xs text-primary">{primaryImages} primary</p>
              </div>
              <ImageIcon className="size-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Size</p>
                <p className="text-2xl font-bold">{totalSize.toFixed(1)} MB</p>
                <p className="text-xs text-success">Optimized</p>
              </div>
              <Download className="size-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Views</p>
                <p className="text-2xl font-bold">{totalViews.toLocaleString()}</p>
                <p className="text-xs text-secondary-foreground">This month</p>
              </div>
              <Eye className="size-8 text-secondary-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Downloads</p>
                <p className="text-2xl font-bold">{totalDownloads}</p>
                <p className="text-xs text-warning">All time</p>
              </div>
              <Download className="size-8 text-warning" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-4">
        {/* Sidebar - Categories */}
        <div className="lg:col-span-1">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Categories</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full rounded-lg p-2 text-left transition-colors ${
                    selectedCategory === category.id
                      ? "bg-primary text-primary-foreground"
                      : "hover:bg-muted"
                  }`}>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">{category.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {category.count}
                    </Badge>
                  </div>
                </button>
              ))}
            </CardContent>
          </Card>

          {/* Popular Tags */}
          <Card className="mt-4">
            <CardHeader>
              <CardTitle className="text-base">Popular Tags</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {["exterior", "interior", "amenities", "modern", "furnished", "pool"].map((tag) => (
                  <Badge
                    key={tag}
                    variant="outline"
                    className="cursor-pointer text-xs hover:bg-primary hover:text-primary-foreground">
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Gallery */}
        <div className="lg:col-span-3">
          {/* Controls */}
          <div className="mb-6 flex flex-col gap-4 sm:flex-row sm:items-center">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search images..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Select value={selectedProperty} onValueChange={setSelectedProperty}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {properties.map((property) => (
                    <SelectItem key={property.id} value={property.id}>
                      {property.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <div className="flex rounded-lg border">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none">
                  <Grid className="size-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none">
                  <List className="size-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Images Display */}
          {viewMode === "grid" ? (
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 xl:grid-cols-3">
              {filteredImages.map((image) => (
                <Card
                  key={image.id}
                  className="group cursor-pointer transition-all hover:shadow-lg">
                  <CardContent className="p-0">
                    <div className="relative">
                      <div
                        className="aspect-video rounded-t-lg bg-muted bg-cover bg-center"
                        style={{
                          backgroundImage: `url(${image.thumbnail})`,
                          backgroundColor: "#f3f4f6",
                        }}
                        onClick={() => openLightbox(image)}>
                        <div className="0 group-hover:20 absolute inset-0 flex items-center justify-center rounded-t-lg bg-black transition-all">
                          <Button
                            variant="secondary"
                            size="sm"
                            className="opacity-0 transition-opacity group-hover:opacity-100">
                            <Maximize className="mr-2 size-4" />
                            View
                          </Button>
                        </div>
                        {image.isPrimary && (
                          <div className="absolute left-2 top-2">
                            <Badge className="text-warning-foreground bg-warning">
                              <Star className="mr-1 size-3" />
                              Primary
                            </Badge>
                          </div>
                        )}
                        <div className="absolute right-2 top-2">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button
                                variant="secondary"
                                size="sm"
                                className="size-8 p-0 opacity-0 transition-opacity group-hover:opacity-100">
                                <MoreHorizontal className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => openLightbox(image)}>
                                <Eye className="mr-2 size-4" />
                                View
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Star className="mr-2 size-4" />
                                Set as Primary
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Download className="mr-2 size-4" />
                                Download
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 size-4" />
                                Edit Details
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-destructive">
                                <Trash2 className="mr-2 size-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>

                      <div className="space-y-2 p-4">
                        <div className="flex items-start justify-between">
                          <h4 className="line-clamp-2 text-sm font-medium">{image.title}</h4>
                        </div>
                        <p className="line-clamp-2 text-xs text-muted-foreground">
                          {image.description}
                        </p>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline" className="text-xs">
                              {image.category}
                            </Badge>
                            <span className="text-xs text-muted-foreground">{image.size}</span>
                          </div>
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                            <div className="flex items-center space-x-1">
                              <Eye className="size-3" />
                              <span>{image.views}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Download className="size-3" />
                              <span>{image.downloads}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-wrap gap-1">
                          {image.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="outline" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                          {image.tags.length > 3 && (
                            <span className="text-xs text-muted-foreground">
                              +{image.tags.length - 3} more
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card>
              <CardContent className="p-0">
                <div className="space-y-4 p-4">
                  {filteredImages.map((image) => (
                    <div
                      key={image.id}
                      className="flex items-center space-x-4 rounded-lg border p-4 transition-shadow hover:shadow-md">
                      <div
                        className="size-16 cursor-pointer rounded bg-muted bg-cover bg-center"
                        style={{
                          backgroundImage: `url(${image.thumbnail})`,
                          backgroundColor: "#f3f4f6",
                        }}
                        onClick={() => openLightbox(image)}
                      />
                      <div className="min-w-0 flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="truncate font-medium">{image.title}</h4>
                          {image.isPrimary && (
                            <Badge className="text-warning-foreground bg-warning">
                              <Star className="mr-1 size-3" />
                              Primary
                            </Badge>
                          )}
                          <Badge variant="outline">{image.category}</Badge>
                        </div>
                        <p className="truncate text-sm text-muted-foreground">
                          {image.description}
                        </p>
                        <div className="mt-1 flex items-center space-x-4 text-xs text-muted-foreground">
                          <span>{image.property}</span>
                          <span>{image.dimensions}</span>
                          <span>{image.size}</span>
                          <span>{new Date(image.uploadDate).toLocaleDateString()}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                        <div className="flex items-center space-x-1">
                          <Eye className="size-4" />
                          <span>{image.views}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Download className="size-4" />
                          <span>{image.downloads}</span>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="size-8 p-0">
                            <MoreHorizontal className="size-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => openLightbox(image)}>
                            <Eye className="mr-2 size-4" />
                            View
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Star className="mr-2 size-4" />
                            Set as Primary
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Download className="mr-2 size-4" />
                            Download
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="mr-2 size-4" />
                            Edit Details
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem className="text-destructive">
                            <Trash2 className="mr-2 size-4" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {filteredImages.length === 0 && (
            <Card>
              <CardContent className="p-12 text-center">
                <ImageIcon className="mx-auto mb-4 size-12 text-muted-foreground" />
                <h3 className="mb-2 text-lg font-medium text-foreground">No images found</h3>
                <p className="mb-4 text-muted-foreground">
                  Try adjusting your search criteria or upload new images.
                </p>
                <Button onClick={() => setIsUploadOpen(true)}>
                  <Upload className="mr-2 size-4" />
                  Upload Images
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Lightbox */}
      <Dialog open={isLightboxOpen} onOpenChange={setIsLightboxOpen}>
        <DialogContent className="max-h-[90vh] max-w-4xl p-0">
          {selectedImage && (
            <div className="relative">
              <div className="flex items-center justify-between border-b p-4">
                <div>
                  <h3 className="font-semibold">{selectedImage.title}</h3>
                  <p className="text-sm text-muted-foreground">{selectedImage.property}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm">
                    <Download className="size-4" />
                  </Button>
                  <Button variant="ghost" size="sm" onClick={() => setIsLightboxOpen(false)}>
                    <X className="size-4" />
                  </Button>
                </div>
              </div>

              <div className="relative bg-black">
                <img
                  src={selectedImage.url}
                  alt={selectedImage.title}
                  className="h-[60vh] w-full object-contain"
                />

                {filteredImages.length > 1 && (
                  <>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute left-4 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
                      onClick={() => navigateLightbox("prev")}>
                      <ChevronLeft className="size-6" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-4 top-1/2 -translate-y-1/2 bg-black/50 text-white hover:bg-black/70"
                      onClick={() => navigateLightbox("next")}>
                      <ChevronRight className="size-6" />
                    </Button>
                  </>
                )}
              </div>

              <div className="p-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">Description</p>
                    <p>{selectedImage.description}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Category</p>
                    <p>{selectedImage.category}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Dimensions</p>
                    <p>{selectedImage.dimensions}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">File Size</p>
                    <p>{selectedImage.size}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Upload Date</p>
                    <p>{new Date(selectedImage.uploadDate).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">Uploaded By</p>
                    <p>{selectedImage.uploadedBy}</p>
                  </div>
                </div>

                <div className="mt-4">
                  <p className="mb-2 text-sm text-muted-foreground">Tags</p>
                  <div className="flex flex-wrap gap-2">
                    {selectedImage.tags.map((tag: string) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
