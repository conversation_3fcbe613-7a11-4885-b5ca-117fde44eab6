#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';
import { navigateWithAuth } from './utils/auth.mjs';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class LayoutVisualValidator {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      passed: 0,
      failed: 0,
      tests: [],
      screenshots: []
    };
  }

  async init() {
    console.log('🚀 Starting Layout Visual Validation...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox', '--disable-web-security']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
    
    // Enable console logging
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log('🔴 Console Error:', msg.text());
      }
    });
  }

  async test(name, testFn) {
    console.log(`🧪 Testing: ${name}`);
    
    try {
      await testFn();
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED', error: null });
      console.log(`✅ ${name} - PASSED`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', error: error.message });
      console.log(`❌ ${name} - FAILED: ${error.message}`);
    }
  }

  async screenshot(name, description = '') {
    try {
      await fs.mkdir(join(__dirname, '..', 'screenshots'), { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
    
    const timestamp = Date.now();
    const filename = `${name}-${timestamp}.png`;
    const screenshotPath = join(__dirname, '..', 'screenshots', filename);
    
    await this.page.screenshot({ 
      path: screenshotPath, 
      fullPage: true 
    });
    
    this.results.screenshots.push({
      name,
      description,
      filename,
      path: screenshotPath,
      timestamp
    });
    
    console.log(`📸 Screenshot: ${filename}${description ? ` - ${description}` : ''}`);
    return screenshotPath;
  }

  async navigateToLayout() {
    console.log('🔍 Navigating to Layout Management...');
    
    const success = await navigateWithAuth(
      this.page, 
      'http://localhost:3000/property-assets/layout'
    );
    
    if (!success) {
      throw new Error('Failed to navigate to layout page');
    }
    
    // Wait for the main component to load - look for our simplified layout page elements
    await this.page.waitForSelector('.grid, h1', { timeout: 15000 });
    await new Promise(resolve => setTimeout(resolve, 2000)); // Let dynamic content load
    
    // Verify we're not on the login page
    const isLoginPage = await this.page.$('input[type="email"], input[placeholder*="email"]');
    if (isLoginPage) {
      throw new Error('Still on login page after authentication');
    }
    
    console.log('✅ Successfully loaded layout management page');
  }

  async validateLayoutManagementPage() {
    await this.test('Navigate to Layout Management Page', async () => {
      await this.navigateToLayout();
    });

    await this.screenshot('01-layout-page-overview', 'Main layout management page');

    await this.test('Validate Analytics Dashboard', async () => {
      // Check for the 4-card analytics dashboard
      const analyticsCards = await this.page.$$('.grid .card, [class*="grid"] > div');
      if (analyticsCards.length < 4) {
        throw new Error(`Expected at least 4 analytics cards, found ${analyticsCards.length}`);
      }

      // Check for specific metric cards
      const metrics = ['Total Layouts', 'Mapping Progress', 'System Health', 'Weekly Progress'];
      for (const metric of metrics) {
        const found = await this.page.$eval('body', (body, text) => 
          body.textContent.includes(text), metric);
        if (!found) {
          console.log(`⚠️ Metric "${metric}" not found (might be translated)`);
        }
      }
    });

    await this.test('Validate Property Selector', async () => {
      // Look for property selector dropdown
      const propertySelector = await this.page.$('select, [role="combobox"], .select-trigger');
      if (!propertySelector) {
        throw new Error('Property selector not found');
      }

      // Check for property information display
      const propertyInfo = await this.page.$('.space-y-2, .property-info, [class*="space-y"]');
      if (!propertyInfo) {
        throw new Error('Property information panel not found');
      }
    });

    await this.test('Validate Layout Grid', async () => {
      // Look for layout cards or empty state
      const layoutGrid = await this.page.$('.grid, [class*="grid"]');
      if (!layoutGrid) {
        throw new Error('Layout grid container not found');
      }

      // Check for either layout cards or empty state message
      const hasLayouts = await this.page.$('.card img, [class*="card"]');
      const hasEmptyState = await this.page.$('[class*="text-center"], .empty-state');
      
      if (!hasLayouts && !hasEmptyState) {
        throw new Error('Neither layout cards nor empty state found');
      }
    });

    await this.test('Validate Search and Filter Controls', async () => {
      // Look for search input
      const searchInput = await this.page.$('input[placeholder*="search" i], input[placeholder*="layouts" i]');
      if (!searchInput) {
        console.log('⚠️ Search input not found (might be styled differently)');
      }

      // Look for filter controls
      const filterControls = await this.page.$$('select, [role="combobox"]');
      if (filterControls.length < 2) {
        console.log('⚠️ Expected multiple filter controls');
      }
    });

    await this.screenshot('02-analytics-dashboard', 'Analytics dashboard with metrics');
  }

  async validateResponsiveDesign() {
    await this.test('Test Mobile Responsive Design', async () => {
      // Switch to mobile viewport
      await this.page.setViewport({ width: 375, height: 812 });
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if layout adapts to mobile
      const isMobileLayout = await this.page.evaluate(() => {
        const grids = document.querySelectorAll('[class*="grid"]');
        return Array.from(grids).some(grid => {
          const style = window.getComputedStyle(grid);
          return style.gridTemplateColumns?.includes('1fr') || 
                 style.flexDirection === 'column';
        });
      });

      await this.screenshot('03-mobile-layout', 'Mobile responsive layout');
      
      if (!isMobileLayout) {
        console.log('⚠️ Mobile layout adaptation not clearly detected');
      }
    });

    await this.test('Test Tablet Responsive Design', async () => {
      await this.page.setViewport({ width: 768, height: 1024 });
      await new Promise(resolve => setTimeout(resolve, 1000));
      await this.screenshot('04-tablet-layout', 'Tablet responsive layout');
    });

    await this.test('Test Desktop Layout', async () => {
      await this.page.setViewport({ width: 1920, height: 1080 });
      await new Promise(resolve => setTimeout(resolve, 1000));
      await this.screenshot('05-desktop-layout', 'Desktop full layout');
    });
  }

  async validateInteractiveElements() {
    await this.test('Test Interactive Buttons', async () => {
      // Look for interactive buttons
      const buttons = await this.page.$$('button');
      if (buttons.length === 0) {
        throw new Error('No interactive buttons found');
      }

      // Test hover effects on first button
      if (buttons.length > 0) {
        await buttons[0].hover();
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    });

    await this.test('Test Property Selection', async () => {
      // Try to interact with property selector
      const propertySelector = await this.page.$('select, [role="combobox"]');
      if (propertySelector) {
        await propertySelector.click();
        await new Promise(resolve => setTimeout(resolve, 500));
        await this.screenshot('06-property-selector-open', 'Property selector dropdown open');
        
        // Close it by clicking elsewhere
        await this.page.click('body');
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    });

    await this.test('Test Layout Card Interactions', async () => {
      // Look for layout cards
      const layoutCards = await this.page.$$('.card, [class*="card"]');
      if (layoutCards.length > 0) {
        // Hover over first card
        await layoutCards[0].hover();
        await new Promise(resolve => setTimeout(resolve, 500));
        await this.screenshot('07-layout-card-hover', 'Layout card hover effect');
      }
    });
  }

  async validateLoadingStates() {
    await this.test('Check for Loading States', async () => {
      // Reload page to potentially see loading states
      await this.page.reload({ waitUntil: 'domcontentloaded' });
      
      // Look for skeleton loaders or loading indicators
      try {
        await this.page.waitForSelector('.animate-pulse, [class*="skeleton"], .loading', { timeout: 2000 });
        await this.screenshot('08-loading-state', 'Loading state detected');
        console.log('🎯 Loading state captured');
      } catch (error) {
        console.log('ℹ️ Loading states may have completed too quickly');
      }
      
      // Wait for final content
      await this.page.waitForSelector('.grid, [class*="grid"]', { timeout: 10000 });
    });
  }

  async validateAccessibility() {
    await this.test('Basic Accessibility Check', async () => {
      // Check for proper headings
      const headings = await this.page.$$('h1, h2, h3, h4, h5, h6');
      if (headings.length === 0) {
        console.log('⚠️ No heading elements found');
      }

      // Check for alt text on images
      const images = await this.page.$$('img');
      for (const img of images) {
        const alt = await img.evaluate(el => el.getAttribute('alt'));
        if (!alt) {
          console.log('⚠️ Image without alt text found');
        }
      }

      // Check for proper button labels
      const buttons = await this.page.$$('button');
      for (const button of buttons) {
        const text = await button.evaluate(el => el.textContent);
        const ariaLabel = await button.evaluate(el => el.getAttribute('aria-label'));
        if (!text?.trim() && !ariaLabel) {
          console.log('⚠️ Button without accessible label found');
        }
      }
    });
  }

  async validateErrorHandling() {
    await this.test('Check Error Handling', async () => {
      // Look for any visible errors
      const errorElements = await this.page.$$('.error, [class*="error"], .text-red-500, .text-destructive');
      
      if (errorElements.length > 0) {
        console.log(`ℹ️ Found ${errorElements.length} error-related elements`);
        await this.screenshot('09-error-states', 'Error states if any');
      }

      // Check console for JavaScript errors
      const consoleLogs = [];
      this.page.on('console', msg => {
        if (msg.type() === 'error') {
          consoleLogs.push(msg.text());
        }
      });

      // Wait a bit to catch any console errors
      await new Promise(resolve => setTimeout(resolve, 2000));
    });
  }

  async tryToAccessMappingInterface() {
    await this.test('Attempt to Access Mapping Interface', async () => {
      // Look for buttons that might lead to mapping interface
      const mappingButtons = await this.page.$$('button, a');
      
      for (const button of mappingButtons) {
        const text = await button.evaluate(el => el.textContent);
        if (text && text.toLowerCase().includes('map')) {
          console.log(`🔍 Found potential mapping button: "${text}"`);
          await button.click();
          await new Promise(resolve => setTimeout(resolve, 2000));
          await this.screenshot('10-mapping-interface', 'Mapping interface accessed');
          break;
        }
      }
    });
  }

  async generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.results.passed + this.results.failed,
        passed: this.results.passed,
        failed: this.results.failed,
        success_rate: this.results.passed + this.results.failed > 0 ? 
          ((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1) : '0'
      },
      tests: this.results.tests,
      screenshots: this.results.screenshots,
      environment: {
        url: this.page.url(),
        viewport: await this.page.viewport(),
        userAgent: await this.page.evaluate(() => navigator.userAgent)
      }
    };

    try {
      await fs.mkdir(join(__dirname, '..', 'test-reports'), { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    const reportPath = join(__dirname, '..', 'test-reports', `layout-visual-validation-${Date.now()}.json`);
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));

    console.log('\n📊 VISUAL VALIDATION REPORT');
    console.log('============================');
    console.log(`✅ Passed: ${report.summary.passed}`);
    console.log(`❌ Failed: ${report.summary.failed}`);
    console.log(`📈 Success Rate: ${report.summary.success_rate}%`);
    console.log(`📸 Screenshots: ${report.screenshots.length}`);
    console.log(`📄 Report: ${reportPath}`);

    return report;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async run() {
    try {
      await this.init();

      // Run all validation tests
      await this.validateLayoutManagementPage();
      await this.validateResponsiveDesign();
      await this.validateInteractiveElements();
      await this.validateLoadingStates();
      await this.validateAccessibility();
      await this.validateErrorHandling();
      await this.tryToAccessMappingInterface();

      // Generate final report
      const report = await this.generateReport();

      return report;

    } catch (error) {
      console.error('💥 Validation failed:', error);
      await this.screenshot('error-state', 'Validation error occurred');
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new LayoutVisualValidator();
  
  validator.run()
    .then(report => {
      console.log(`\n🎉 Visual validation completed with ${report.summary.success_rate}% success rate`);
      process.exit(report.summary.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('❌ Validation failed:', error.message);
      process.exit(1);
    });
}

export { LayoutVisualValidator };