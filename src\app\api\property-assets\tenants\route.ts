import { NextRequest, NextResponse } from "next/server";

import type { <PERSON>reate<PERSON><PERSON><PERSON>, Tenant } from "@/features/property-assets/types";

import { tenantsMockData } from "../mock-data";

// GET /api/property-assets/tenants
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") as Tenant["status"] | null;
    const employment_status = searchParams.get("employment_status") as
      | Tenant["employment_status"]
      | null;

    let filteredTenants = [...tenantsMockData];

    // Apply search filter
    if (search) {
      filteredTenants = filteredTenants.filter(
        (tenant) =>
          tenant.first_name.toLowerCase().includes(search.toLowerCase()) ||
          tenant.last_name.toLowerCase().includes(search.toLowerCase()) ||
          tenant.email.toLowerCase().includes(search.toLowerCase()) ||
          tenant.phone.includes(search) ||
          (tenant.employer_name &&
            tenant.employer_name.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply status filter
    if (status) {
      filteredTenants = filteredTenants.filter((tenant) => tenant.status === status);
    }

    // Apply employment status filter
    if (employment_status) {
      filteredTenants = filteredTenants.filter(
        (tenant) => tenant.employment_status === employment_status
      );
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTenants = filteredTenants.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 350));

    return NextResponse.json({
      items: paginatedTenants,
      total: filteredTenants.length,
      page,
      limit,
      totalPages: Math.ceil(filteredTenants.length / limit),
    });
  } catch (error) {
    console.error("Error fetching tenants:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/property-assets/tenants
export async function POST(request: NextRequest) {
  try {
    const body: CreateTenant = await request.json();

    // Validate required fields
    if (!body.first_name || !body.last_name || !body.email || !body.phone) {
      return NextResponse.json(
        { error: "Missing required fields: first_name, last_name, email, phone" },
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingTenant = tenantsMockData.find(
      (tenant) => tenant.email.toLowerCase() === body.email.toLowerCase()
    );

    if (existingTenant) {
      return NextResponse.json({ error: "Email already exists" }, { status: 409 });
    }

    // Check if phone already exists
    const existingPhone = tenantsMockData.find((tenant) => tenant.phone === body.phone);

    if (existingPhone) {
      return NextResponse.json({ error: "Phone number already exists" }, { status: 409 });
    }

    // Transform documents to match Tenant format
    const transformedDocuments = body.documents?.map((doc, index) => ({
      id: `doc_${Date.now()}_${index}`,
      document_type: doc.document_type as "id" | "income_proof" | "reference" | "other",
      file_name: doc.file_name,
      file_url: doc.file_data,
      uploaded_at: new Date().toISOString(),
    }));

    // Create new tenant
    const { documents: _, ...bodyWithoutDocuments } = body;
    const newTenant: Tenant = {
      id: `tenant_${Date.now()}`,
      ...bodyWithoutDocuments,
      documents: transformedDocuments,
      status: "active",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company1", // Mock company ID
    };

    // Add to mock data
    tenantsMockData.push(newTenant);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 550));

    return NextResponse.json({
      data: newTenant,
      success: true,
      message: "Tenant created successfully",
    });
  } catch (error) {
    console.error("Error creating tenant:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
