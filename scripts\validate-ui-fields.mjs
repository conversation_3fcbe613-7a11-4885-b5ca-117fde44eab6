#!/usr/bin/env node

/**
 * Comprehensive UI Field Validation Script
 * 
 * A reusable tool that validates any React component for:
 * - Dark/Light mode compatibility
 * - Field visibility and accessibility
 * - Interactive element functionality
 * - Translation/i18n compliance
 * - Manual testing procedures
 * 
 * Usage:
 *   node validate-ui-fields.mjs --component path/to/Component.tsx
 *   node validate-ui-fields.mjs --directory path/to/components/
 *   node validate-ui-fields.mjs --interactive --component Form.tsx
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration for validation rules
const validationConfig = {
  themeClasses: {
    // Required theme-aware classes
    required: [
      'bg-background', 'bg-card', 'bg-muted', 'bg-accent',
      'text-foreground', 'text-muted-foreground', 'text-destructive',
      'border-border', 'border-input', 'border-accent',
      'hover:bg-accent', 'hover:text-accent-foreground'
    ],
    // Prohibited hardcoded classes
    prohibited: [
      'bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200',
      'text-gray-900', 'text-gray-700', 'text-gray-500', 'text-gray-400',
      'text-red-500', 'text-red-600',
      'border-gray-200', 'border-gray-300',
      'hover:bg-gray-50', 'hover:text-gray-600'
    ],
    // Allowed brand colors
    brandColors: [
      'focus:border-blue-500', 'focus:ring-blue-500',
      'bg-blue-500', 'bg-green-500', 'bg-purple-500', 
      'bg-orange-500', 'bg-pink-500', 'bg-red-500',
      'hover:bg-red-600', 'text-white'
    ]
  },
  
  fieldTypes: {
    inputs: ['Input', 'Textarea', 'SelectTrigger', 'input', 'textarea', 'select'],
    buttons: ['Button', 'button'],
    labels: ['FormLabel', 'Label', 'CardTitle', 'label'],
    containers: ['Card', 'CardHeader', 'CardContent', 'Dialog', 'Modal'],
    interactive: ['Select', 'Checkbox', 'RadioGroup', 'Switch', 'Slider'],
    forms: ['Form', 'FormField', 'FormItem', 'FormControl', 'form']
  },
  
  textPatterns: {
    placeholders: [/placeholder=["']([^"']+)["']/g, /placeholder={["']([^"']+)["']}/g],
    labels: [/FormLabel[^>]*>([^<]+)</g, /<label[^>]*>([^<]+)</g],
    titles: [/CardTitle[^>]*>([^<]+)</g, /<h[1-6][^>]*>([^<]+)</g],
    ariaLabels: [/aria-label=["']([^"']+)["']/g],
    buttonText: [/<Button[^>]*>([^<]+)</g, /<button[^>]*>([^<]+)</g]
  },
  
  // Translation validation patterns
  translation: {
    // i18n function calls
    tFunction: [/\bt\(["']([^"']+)["']\)/g, /\bt\(`([^`]+)`\)/g],
    // Translation keys in JSX
    translationKeys: [/{t\(["']([^"']+)["']\)}/g, /{t\(`([^`]+)`\)}/g],
    // Hardcoded text that should be translated (common patterns)
    hardcodedText: {
      // Text in JSX elements that are not translation calls
      jsxText: [/>\s*([A-Z][^<{]*[a-zA-Z])\s*</g],
      // String literals in placeholders
      placeholderStrings: [/placeholder=["']([A-Z][^"']*[a-zA-Z])["']/g],
      // Button text that's not translated
      buttonStrings: [/>\s*([A-Z][^<{]*[a-zA-Z])\s*<\/Button>/g],
      // Common hardcoded words
      commonWords: [/>\s*(Save|Cancel|Submit|Update|Create|Delete|Edit|Add|Remove|Loading|Error|Success|Warning|Info|Confirm|Yes|No|Enter|Please|Required|Optional|Choose|Select|User Profile Form|Email Address|fill out all required fields)\s*</gi]
    },
    // Exempt patterns that shouldn't be translated
    exemptPatterns: [
      /PNG, JPG, GIF/g,  // File format descriptions
      /className=/g,     // CSS classes
      /import /g,        // Import statements
      /console\./g,      // Console logs
      /\d+/g,           // Numbers only
      /[A-Z_]+:/g,      // Constants/keys
      /\b(id|name|type|value|href|src|alt)=/g // HTML attributes
    ]
  },
  
  stateClasses: {
    focus: ['focus:', 'focus-within:'],
    hover: ['hover:'],
    disabled: ['disabled:', 'disabled='],
    error: ['error', 'destructive', 'invalid']
  }
};

// Command line argument parsing
const args = process.argv.slice(2);
const options = {
  component: null,
  directory: null,
  interactive: false,
  checklist: false,
  fullReport: false,
  manualTests: false,
  output: null,
  config: null
};

// Parse arguments
for (let i = 0; i < args.length; i++) {
  switch (args[i]) {
    case '--component':
      options.component = args[++i];
      break;
    case '--directory':
      options.directory = args[++i];
      break;
    case '--interactive':
      options.interactive = true;
      break;
    case '--checklist':
      options.checklist = true;
      break;
    case '--full-report':
      options.fullReport = true;
      break;
    case '--manual-tests':
      options.manualTests = true;
      break;
    case '--output':
      options.output = args[++i];
      break;
    case '--config':
      options.config = args[++i];
      break;
    case '--help':
      showHelp();
      process.exit(0);
    default:
      if (!options.component && !options.directory && !args[i].startsWith('--')) {
        options.component = args[i];
      }
  }
}

function showHelp() {
  console.log(`
🔍 UI Field Validation Script

Usage:
  node validate-ui-fields.mjs [options] [component-path]

Options:
  --component <path>     Validate single component file
  --directory <path>     Validate all components in directory
  --interactive          Run interactive manual testing mode
  --checklist           Generate manual testing checklist only
  --full-report         Generate comprehensive HTML report
  --manual-tests        Include manual testing procedures
  --output <file>       Output file for reports
  --config <file>       Custom validation configuration
  --help               Show this help message

Examples:
  # Single component validation
  node validate-ui-fields.mjs --component src/components/Form.tsx
  
  # Directory batch validation
  node validate-ui-fields.mjs --directory src/features/property-assets/components/
  
  # Interactive testing mode
  node validate-ui-fields.mjs --interactive --component PropertyForm.tsx
  
  # Generate testing checklist
  node validate-ui-fields.mjs --checklist --manual-tests --component UnitForm.tsx
`);
}

class UIFieldValidator {
  constructor(config = validationConfig) {
    this.config = config;
    this.results = [];
  }

  // Discover field types in component content
  discoverFields(content, filePath) {
    const discovery = {
      filePath,
      fileName: path.basename(filePath),
      fields: {
        inputs: [],
        buttons: [],
        labels: [],
        containers: [],
        interactive: [],
        forms: []
      },
      textContent: {
        placeholders: [],
        labels: [],
        titles: [],
        ariaLabels: [],
        buttonText: []
      },
      translation: {
        tFunctionCalls: [],
        translationKeys: [],
        hardcodedText: [],
        untranslatedPlaceholders: [],
        missingTranslations: [],
        hasUseTranslation: false
      },
      themeClasses: {
        required: [],
        prohibited: [],
        brandColors: []
      },
      states: {
        focus: [],
        hover: [],
        disabled: [],
        error: []
      },
      issues: [],
      warnings: [],
      suggestions: []
    };

    // Check for useTranslation import
    discovery.translation.hasUseTranslation = /import.*useTranslation.*from.*react-i18next/.test(content) || /const.*{.*t.*}.*=.*useTranslation/.test(content);

    // Discover field types
    Object.entries(this.config.fieldTypes).forEach(([category, patterns]) => {
      patterns.forEach(pattern => {
        const regex = new RegExp(`<${pattern}\\b[^>]*>`, 'g');
        const matches = [...content.matchAll(regex)];
        matches.forEach(match => {
          discovery.fields[category].push({
            type: pattern,
            match: match[0],
            line: this.getLineNumber(content, match.index)
          });
        });
      });
    });

    // Extract text content
    Object.entries(this.config.textPatterns).forEach(([category, patterns]) => {
      patterns.forEach(pattern => {
        const matches = [...content.matchAll(pattern)];
        matches.forEach(match => {
          discovery.textContent[category].push({
            text: match[1],
            match: match[0],
            line: this.getLineNumber(content, match.index)
          });
        });
      });
    });

    // Check theme classes
    this.config.themeClasses.required.forEach(className => {
      const matches = content.match(new RegExp(className, 'g'));
      if (matches) {
        discovery.themeClasses.required.push({ className, count: matches.length });
      }
    });

    this.config.themeClasses.prohibited.forEach(className => {
      const matches = content.match(new RegExp(className, 'g'));
      if (matches) {
        discovery.themeClasses.prohibited.push({ className, count: matches.length });
        discovery.issues.push(`❌ Found prohibited class: "${className}" (${matches.length} times)`);
      }
    });

    this.config.themeClasses.brandColors.forEach(className => {
      const matches = content.match(new RegExp(className, 'g'));
      if (matches) {
        discovery.themeClasses.brandColors.push({ className, count: matches.length });
      }
    });

    // Check for interactive states
    Object.entries(this.config.stateClasses).forEach(([state, patterns]) => {
      patterns.forEach(pattern => {
        const matches = content.match(new RegExp(pattern, 'g'));
        if (matches) {
          discovery.states[state].push({ pattern, count: matches.length });
        }
      });
    });

    // Validate translations
    this.validateTranslations(content, discovery);

    // Generate warnings and suggestions
    this.generateInsights(discovery);

    return discovery;
  }

  getLineNumber(content, index) {
    return content.substring(0, index).split('\n').length;
  }

  // Validate translation usage
  validateTranslations(content, discovery) {
    // Find all translation function calls
    this.config.translation.tFunction.forEach(pattern => {
      const matches = [...content.matchAll(pattern)];
      matches.forEach(match => {
        discovery.translation.tFunctionCalls.push({
          key: match[1],
          match: match[0],
          line: this.getLineNumber(content, match.index)
        });
      });
    });

    // Find translation keys in JSX
    this.config.translation.translationKeys.forEach(pattern => {
      const matches = [...content.matchAll(pattern)];
      matches.forEach(match => {
        discovery.translation.translationKeys.push({
          key: match[1],
          match: match[0],
          line: this.getLineNumber(content, match.index)
        });
      });
    });

    // Check for hardcoded text that should be translated
    if (discovery.translation.hasUseTranslation) {
      // Check JSX text content
      const jsxTextMatches = [...content.matchAll(this.config.translation.hardcodedText.jsxText)];
      jsxTextMatches.forEach(match => {
        const text = match[1].trim();
        // Skip if it's likely not user-facing text
        if (this.isUserFacingText(text, content, match.index)) {
          discovery.translation.hardcodedText.push({
            text,
            match: match[0],
            line: this.getLineNumber(content, match.index),
            type: 'jsx-text'
          });
        }
      });

      // Check placeholder strings
      const placeholderMatches = [...content.matchAll(this.config.translation.hardcodedText.placeholderStrings)];
      placeholderMatches.forEach(match => {
        const text = match[1];
        if (this.isUserFacingText(text, content, match.index)) {
          discovery.translation.untranslatedPlaceholders.push({
            text,
            match: match[0],
            line: this.getLineNumber(content, match.index)
          });
        }
      });

      // Check common hardcoded words
      const commonWordMatches = [...content.matchAll(this.config.translation.hardcodedText.commonWords)];
      commonWordMatches.forEach(match => {
        const text = match[1] || match[0];
        if (this.isUserFacingText(text, content, match.index)) {
          discovery.translation.hardcodedText.push({
            text,
            match: match[0],
            line: this.getLineNumber(content, match.index),
            type: 'common-word'
          });
        }
      });
    }
  }

  // Helper to determine if text is user-facing and should be translated
  isUserFacingText(text, content, index) {
    // Skip if text is too short or contains only numbers/symbols
    if (text.length < 2 || /^[\d\s\-_.,!@#$%^&*()+={}\[\]|\\:;"'<>?/~`]+$/.test(text)) {
      return false;
    }

    // Skip if it matches exempt patterns
    for (const pattern of this.config.translation.exemptPatterns) {
      if (pattern.test(text)) {
        return false;
      }
    }

    // Skip if it's inside a comment or import statement
    const contextBefore = content.substring(Math.max(0, index - 100), index);
    const contextAfter = content.substring(index, index + 100);
    const fullContext = contextBefore + contextAfter;

    if (/\/\*|\*\/|\/\/|import\s|console\.|className|const\s|let\s|var\s/.test(fullContext)) {
      return false;
    }

    // Check if it's already wrapped in a translation function
    if (/t\(|\bt\s*\(/.test(contextBefore)) {
      return false;
    }

    return true;
  }

  generateInsights(discovery) {
    const totalFields = Object.values(discovery.fields).reduce((sum, arr) => sum + arr.length, 0);
    const totalText = Object.values(discovery.textContent).reduce((sum, arr) => sum + arr.length, 0);

    // Check for missing accessibility features
    if (discovery.fields.inputs.length > 0 && discovery.textContent.labels.length === 0) {
      discovery.warnings.push('⚠️ Input fields found but no labels detected - check accessibility');
    }

    if (discovery.fields.inputs.length > 0 && discovery.states.focus.length === 0) {
      discovery.warnings.push('⚠️ Input fields found but no focus states - check keyboard navigation');
    }

    if (discovery.fields.buttons.length > 0 && discovery.states.disabled.length === 0) {
      discovery.warnings.push('⚠️ Buttons found but no disabled states - check loading/submit states');
    }

    // Translation validation insights
    if (discovery.translation.hasUseTranslation) {
      if (discovery.translation.hardcodedText.length > 0) {
        discovery.issues.push(`❌ Found ${discovery.translation.hardcodedText.length} hardcoded text(s) that should use translation`);
      }

      if (discovery.translation.untranslatedPlaceholders.length > 0) {
        discovery.issues.push(`❌ Found ${discovery.translation.untranslatedPlaceholders.length} untranslated placeholder(s)`);
      }

      if (discovery.translation.tFunctionCalls.length === 0 && discovery.translation.translationKeys.length === 0) {
        discovery.warnings.push('⚠️ useTranslation imported but no translation calls found');
      }
    } else {
      // Check if component has user-facing text but no translation setup
      const hasUserText = totalText > 0 || discovery.fields.inputs.length > 0;
      if (hasUserText) {
        discovery.warnings.push('⚠️ Component has user-facing content but no i18n setup detected');
      }
    }

    // Suggest improvements
    if (discovery.themeClasses.required.length < 3) {
      discovery.suggestions.push('💡 Consider using more theme-aware CSS classes for better dark mode support');
    }

    if (totalText === 0) {
      discovery.warnings.push('⚠️ No text content detected - component may be missing user-facing text');
    }

    if (totalFields > 10 && discovery.textContent.ariaLabels.length === 0) {
      discovery.suggestions.push('💡 Complex form detected - consider adding ARIA labels for better accessibility');
    }

    // Translation suggestions
    if (discovery.translation.hasUseTranslation && discovery.translation.tFunctionCalls.length > 0) {
      discovery.suggestions.push('💡 Great job using translations! Consider reviewing keys for consistency');
    }

    if (!discovery.translation.hasUseTranslation && totalText > 5) {
      discovery.suggestions.push('💡 Consider adding i18n support for better internationalization');
    }
  }

  // Generate manual testing procedures
  generateManualTests(discovery) {
    const tests = {
      component: discovery.fileName,
      route: this.inferRoute(discovery.filePath),
      visualTests: [],
      functionalTests: [],
      accessibilityTests: [],
      themeTests: [],
      interactionTests: []
    };

    // Visual validation tests
    tests.visualTests = [
      {
        category: 'Light Mode Visual Check',
        steps: [
          '□ Navigate to component in light mode',
          '□ Verify all text is readable with proper contrast',
          '□ Check that input borders are visible and distinct',
          '□ Ensure button states are clearly differentiated',
          '□ Confirm cards/containers have proper visual separation',
          '□ Validate that focus indicators are visible when tabbing'
        ]
      },
      {
        category: 'Dark Mode Visual Check',
        steps: [
          '□ Switch to dark mode (OS setting or browser extension)',
          '□ Refresh the page to ensure clean load',
          '□ Verify all elements adapt to dark theme',
          '□ Check for any "flash" of light colors during load',
          '□ Ensure text maintains readability against dark backgrounds',
          '□ Confirm interactive elements remain discoverable'
        ]
      }
    ];

    // Field-specific functional tests
    if (discovery.fields.inputs.length > 0) {
      const inputTests = {
        category: 'Form Field Testing',
        steps: []
      };

      discovery.fields.inputs.forEach(input => {
        inputTests.steps.push(`□ Test ${input.type} field: accepts input properly`);
        inputTests.steps.push(`□ Verify validation messages appear correctly`);
        inputTests.steps.push(`□ Check placeholder text provides clear guidance`);
      });

      if (discovery.textContent.labels.length > 0) {
        inputTests.steps.push('□ Verify all fields have visible labels');
      }

      tests.functionalTests.push(inputTests);
    }

    // Button interaction tests
    if (discovery.fields.buttons.length > 0) {
      tests.interactionTests.push({
        category: 'Button Interaction Testing',
        steps: [
          '□ Click each button to verify it responds',
          '□ Test hover states show visual feedback',
          '□ Verify disabled states prevent interaction',
          '□ Check loading states (if applicable) display correctly',
          '□ Test keyboard activation (Enter/Space keys)'
        ]
      });
    }

    // Interactive element tests
    if (discovery.fields.interactive.length > 0) {
      tests.interactionTests.push({
        category: 'Interactive Element Testing',
        steps: [
          '□ Test dropdown menus open and close properly',
          '□ Verify checkboxes/radio buttons toggle correctly',
          '□ Check select components show options',
          '□ Test slider/range controls (if present)',
          '□ Verify switch components toggle states'
        ]
      });
    }

    // Accessibility tests
    tests.accessibilityTests = [
      {
        category: 'Keyboard Navigation',
        steps: [
          '□ Tab through all interactive elements in logical order',
          '□ Verify focus indicators are visible in both light and dark modes',
          '□ Test Enter/Space keys activate buttons properly',
          '□ Check Escape key closes modals/dropdowns (if applicable)',
          '□ Verify arrow keys work for select/radio components'
        ]
      },
      {
        category: 'Screen Reader Testing',
        steps: [
          '□ Enable screen reader (VoiceOver/NVDA/JAWS)',
          '□ Verify labels are properly associated with form fields',
          '□ Check that error messages are announced',
          '□ Confirm required fields are identified as such',
          '□ Test that button purposes are clearly communicated'
        ]
      }
    ];

    // Theme-specific tests
    tests.themeTests = [
      {
        category: 'Theme Switching Performance',
        steps: [
          '□ Switch between light and dark mode multiple times',
          '□ Check for smooth transitions without flickering',
          '□ Verify no elements become invisible during switch',
          '□ Test theme persistence across page reloads',
          '□ Check that system theme changes are respected'
        ]
      }
    ];

    // Translation/i18n tests
    if (discovery.translation.hasUseTranslation) {
      tests.translationTests = [
        {
          category: 'Language Switching',
          steps: [
            '□ Switch language to Vietnamese in settings/preferences',
            '□ Verify all text elements are translated properly',
            '□ Check that form labels appear in the selected language',
            '□ Ensure button text and placeholders are translated',
            '□ Verify error messages show in correct language',
            '□ Test that no English text remains visible'
          ]
        },
        {
          category: 'Translation Completeness',
          steps: [
            '□ Check for any hardcoded English text',
            '□ Verify all user-facing strings use t() function',
            '□ Test placeholder text in all input fields',
            '□ Confirm tooltip and help text are translated',
            '□ Validate date/time formats respect locale settings'
          ]
        }
      ];
    }

    return tests;
  }

  inferRoute(filePath) {
    // Try to infer the route from file path structure
    const pathParts = filePath.split('/');
    const featureIndex = pathParts.indexOf('features');
    
    if (featureIndex !== -1 && featureIndex + 1 < pathParts.length) {
      const feature = pathParts[featureIndex + 1];
      if (feature === 'property-assets') {
        if (filePath.includes('PropertyForm')) return '/property-assets/properties/new';
        if (filePath.includes('UnitForm')) return '/property-assets/units/new';
        if (filePath.includes('PropertyList')) return '/property-assets/properties';
        if (filePath.includes('UnitList')) return '/property-assets/units';
      }
      if (feature === 'orders') {
        if (filePath.includes('AddOrder')) return '/orders/new';
        return '/orders';
      }
    }
    
    return 'Route not detected - manual navigation required';
  }

  // Validate single component file
  async validateComponent(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      const content = fs.readFileSync(filePath, 'utf8');
      const discovery = this.discoverFields(content, filePath);
      
      // Calculate compatibility score
      const score = this.calculateCompatibilityScore(discovery);
      
      const result = {
        ...discovery,
        compatibilityScore: score,
        manualTests: options.manualTests || options.interactive ? this.generateManualTests(discovery) : null
      };

      this.results.push(result);
      return result;

    } catch (error) {
      console.error(`❌ Error validating ${filePath}: ${error.message}`);
      return null;
    }
  }

  // Calculate compatibility score based on findings
  calculateCompatibilityScore(discovery) {
    let score = 100;

    // Deduct for issues
    score -= discovery.issues.length * 15; // Major issues
    score -= discovery.warnings.length * 5; // Warnings
    score -= discovery.themeClasses.prohibited.length * 10; // Prohibited classes
    score -= discovery.translation.hardcodedText.length * 8; // Translation issues
    score -= discovery.translation.untranslatedPlaceholders.length * 5; // Placeholder issues

    // Bonus for good practices
    if (discovery.themeClasses.required.length >= 3) score += 5;
    if (discovery.states.focus.length > 0) score += 5;
    if (discovery.textContent.ariaLabels.length > 0) score += 5;
    if (discovery.translation.hasUseTranslation && discovery.translation.hardcodedText.length === 0) score += 10; // Good i18n practices

    return Math.max(0, Math.min(100, score));
  }

  // Display validation results
  displayResults(result) {
    console.log(`\n🔍 Analyzing ${result.fileName}:`);
    console.log('='.repeat(60));

    // Field discovery summary
    const totalFields = Object.values(result.fields).reduce((sum, arr) => sum + arr.length, 0);
    const totalText = Object.values(result.textContent).reduce((sum, arr) => sum + arr.length, 0);

    console.log(`\n📊 Component Analysis:`);
    console.log(`   Fields discovered: ${totalFields}`);
    console.log(`   Text elements: ${totalText}`);
    console.log(`   Theme classes: ${result.themeClasses.required.length} required, ${result.themeClasses.prohibited.length} prohibited`);
    console.log(`   Interactive states: ${Object.values(result.states).reduce((sum, arr) => sum + arr.length, 0)}`);
    
    // Translation analysis
    if (result.translation.hasUseTranslation) {
      console.log(`\n🌐 Translation Analysis:`);
      console.log(`   Translation calls: ${result.translation.tFunctionCalls.length + result.translation.translationKeys.length}`);
      console.log(`   Hardcoded text found: ${result.translation.hardcodedText.length}`);
      console.log(`   Untranslated placeholders: ${result.translation.untranslatedPlaceholders.length}`);
      console.log(`   i18n setup: ✅ Configured`);
    } else {
      console.log(`\n🌐 Translation Analysis:`);
      console.log(`   i18n setup: ❌ Not detected`);
    }

    // Issues and warnings
    if (result.issues.length > 0) {
      console.log(`\n🚨 Critical Issues (${result.issues.length}):`);
      result.issues.forEach(issue => console.log(`   ${issue}`));
    }

    if (result.warnings.length > 0) {
      console.log(`\n⚠️ Warnings (${result.warnings.length}):`);
      result.warnings.forEach(warning => console.log(`   ${warning}`));
    }

    if (result.suggestions.length > 0) {
      console.log(`\n💡 Suggestions (${result.suggestions.length}):`);
      result.suggestions.forEach(suggestion => console.log(`   ${suggestion}`));
    }

    // Compatibility score
    console.log(`\n🎯 Compatibility Score: ${result.compatibilityScore}/100`);
    if (result.compatibilityScore >= 85) {
      console.log('   🎉 EXCELLENT - Ready for production');
    } else if (result.compatibilityScore >= 70) {
      console.log('   ✅ GOOD - Minor improvements recommended');
    } else if (result.compatibilityScore >= 50) {
      console.log('   ⚠️ FAIR - Significant improvements needed');
    } else {
      console.log('   ❌ POOR - Major fixes required');
    }

    // Manual testing info
    if (result.manualTests) {
      const testCategories = Object.keys(result.manualTests).length - 2;
      console.log(`\n📝 Manual Testing Required:`);
      console.log(`   Route: ${result.manualTests.route}`);
      console.log(`   Test Categories: ${testCategories}`);
      if (result.translation.hasUseTranslation) {
        console.log(`   Translation Testing: ✅ Included`);
      }
    }
  }

  // Interactive testing mode
  async runInteractiveTests(result) {
    if (!result.manualTests) {
      console.log('❌ No manual tests generated for this component');
      return;
    }

    console.log(`\n🎯 Interactive Testing Mode: ${result.fileName}`);
    console.log('='.repeat(60));
    console.log(`Route: ${result.manualTests.route}`);
    console.log('\nPress Enter after completing each test category...\n');

    const testCategories = [
      { name: 'Visual Tests', tests: result.manualTests.visualTests },
      { name: 'Functional Tests', tests: result.manualTests.functionalTests },
      { name: 'Interaction Tests', tests: result.manualTests.interactionTests },
      { name: 'Accessibility Tests', tests: result.manualTests.accessibilityTests },
      { name: 'Theme Tests', tests: result.manualTests.themeTests },
      { name: 'Translation Tests', tests: result.manualTests.translationTests || [] }
    ].filter(cat => cat.tests && cat.tests.length > 0);

    for (const category of testCategories) {
      console.log(`\n🧪 ${category.name}:`);
      console.log('-'.repeat(40));
      
      for (const test of category.tests) {
        console.log(`\n${test.category}:`);
        test.steps.forEach(step => console.log(`  ${step}`));
      }

      if (options.interactive) {
        // In a real implementation, we'd wait for user input
        console.log('\n⏳ Complete the above tests, then press Enter to continue...');
        // await waitForEnter(); // Would implement this for real interactivity
      }
    }

    console.log('\n✅ Interactive testing completed!');
  }

  // Generate detailed checklist
  generateChecklist(result) {
    if (!result.manualTests) return;

    console.log(`\n📋 MANUAL TESTING CHECKLIST: ${result.fileName}`);
    console.log('='.repeat(80));
    console.log(`Component: ${result.fileName}`);
    console.log(`Route: ${result.manualTests.route}`);
    console.log(`Compatibility Score: ${result.compatibilityScore}/100`);
    console.log(`Generated: ${new Date().toISOString()}`);

    const allTests = [
      ...result.manualTests.visualTests,
      ...result.manualTests.functionalTests,
      ...result.manualTests.interactionTests,
      ...result.manualTests.accessibilityTests,
      ...result.manualTests.themeTests,
      ...(result.manualTests.translationTests || [])
    ];

    allTests.forEach((testGroup, index) => {
      console.log(`\n${index + 1}. ${testGroup.category}`);
      console.log('-'.repeat(testGroup.category.length + 3));
      testGroup.steps.forEach(step => console.log(`   ${step}`));
    });

    console.log('\n📝 Test Results:');
    console.log('   [ ] All visual tests passed');
    console.log('   [ ] All functional tests passed');
    console.log('   [ ] All interaction tests passed');
    console.log('   [ ] All accessibility tests passed');
    console.log('   [ ] All theme tests passed');
    if (result.manualTests.translationTests && result.manualTests.translationTests.length > 0) {
      console.log('   [ ] All translation tests passed');
    }
    console.log('\n📸 Screenshots taken: [ ] Light mode [ ] Dark mode');
    console.log('🐛 Issues found: _______________________________________________');
    console.log('📅 Tested by: _________________ Date: _________________');
  }

  // Validate directory of components
  async validateDirectory(dirPath) {
    if (!fs.existsSync(dirPath)) {
      throw new Error(`Directory not found: ${dirPath}`);
    }

    const files = this.findComponentFiles(dirPath);
    console.log(`\n🔍 Found ${files.length} component files in ${dirPath}`);

    const results = [];
    for (const file of files) {
      const result = await this.validateComponent(file);
      if (result) {
        results.push(result);
        this.displayResults(result);
      }
    }

    this.displayBatchSummary(results);
    return results;
  }

  findComponentFiles(dirPath) {
    const files = [];
    const entries = fs.readdirSync(dirPath, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        files.push(...this.findComponentFiles(fullPath));
      } else if (entry.isFile() && (entry.name.endsWith('.tsx') || entry.name.endsWith('.jsx'))) {
        // Skip test files and utility files
        if (!entry.name.includes('.test.') && !entry.name.includes('.spec.') && 
            !entry.name.startsWith('use') && !entry.name.includes('utils')) {
          files.push(fullPath);
        }
      }
    }

    return files;
  }

  displayBatchSummary(results) {
    console.log('\n' + '='.repeat(80));
    console.log('📊 BATCH VALIDATION SUMMARY');
    console.log('='.repeat(80));

    const totalComponents = results.length;
    const averageScore = results.reduce((sum, r) => sum + r.compatibilityScore, 0) / totalComponents;
    const issueCount = results.reduce((sum, r) => sum + r.issues.length, 0);
    const warningCount = results.reduce((sum, r) => sum + r.warnings.length, 0);

    console.log(`Total Components: ${totalComponents}`);
    console.log(`Average Score: ${Math.round(averageScore)}/100`);
    console.log(`Total Issues: ${issueCount}`);
    console.log(`Total Warnings: ${warningCount}`);

    // Component rankings
    console.log('\n🏆 Component Rankings:');
    results
      .sort((a, b) => b.compatibilityScore - a.compatibilityScore)
      .forEach((result, index) => {
        const emoji = result.compatibilityScore >= 85 ? '🎉' : 
                     result.compatibilityScore >= 70 ? '✅' : 
                     result.compatibilityScore >= 50 ? '⚠️' : '❌';
        console.log(`   ${index + 1}. ${emoji} ${result.fileName} (${result.compatibilityScore}/100)`);
      });

    if (issueCount > 0) {
      console.log('\n🚨 Components needing immediate attention:');
      results
        .filter(r => r.issues.length > 0)
        .forEach(result => {
          console.log(`   ❌ ${result.fileName}: ${result.issues.length} critical issues`);
        });
    }
  }
}

// Main execution
async function main() {
  console.log('🎯 UI Field Validation Script');
  console.log('Comprehensive Dark/Light Mode & Manual Testing Tool');
  console.log('='.repeat(80));

  if (!options.component && !options.directory) {
    console.log('❌ Please specify a component file or directory to validate');
    console.log('Use --help for usage information');
    process.exit(1);
  }

  // Load custom config if provided
  let config = validationConfig;
  if (options.config) {
    try {
      const customConfig = JSON.parse(fs.readFileSync(options.config, 'utf8'));
      config = { ...validationConfig, ...customConfig };
    } catch (error) {
      console.log(`⚠️ Could not load custom config: ${error.message}`);
    }
  }

  const validator = new UIFieldValidator(config);

  try {
    if (options.component) {
      // Single component validation
      const result = await validator.validateComponent(options.component);
      if (result) {
        validator.displayResults(result);
        
        if (options.interactive) {
          await validator.runInteractiveTests(result);
        } else if (options.checklist) {
          validator.generateChecklist(result);
        }
      }
    } else if (options.directory) {
      // Directory batch validation
      await validator.validateDirectory(options.directory);
    }

    // Save results if output specified
    if (options.output && validator.results.length > 0) {
      fs.writeFileSync(options.output, JSON.stringify(validator.results, null, 2));
      console.log(`\n💾 Results saved to: ${options.output}`);
    }

  } catch (error) {
    console.error(`❌ Validation failed: ${error.message}`);
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);