"use client";

import { useState } from "react";
import {
  Activity,
  AlertCircle,
  BarChart3,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Download,
  Filter,
  Home,
  PieChart,
  RefreshCw,
  Target,
  TrendingDown,
  TrendingUp,
  UserMinus,
  UserPlus,
  Users,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  ComposedChart,
  Legend,
  Line,
  LineChart,
  Pie,
  <PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";

interface OccupancyAnalyticsProps {
  className?: string;
}

export function OccupancyAnalytics({ className }: OccupancyAnalyticsProps) {
  const { t } = useTranslation();
  const [selectedProperty, setSelectedProperty] = useState<string>("all");
  const [timeRange, setTimeRange] = useState<"month" | "quarter" | "year">("year");
  const [selectedMetric, setSelectedMetric] = useState<"occupancy" | "turnover" | "retention">(
    "occupancy"
  );

  // Mock occupancy analytics data
  const occupancyData = {
    summary: {
      totalUnits: 125,
      occupiedUnits: 115,
      vacantUnits: 10,
      currentOccupancyRate: 92.0,
      averageOccupancyRate: 89.5,
      occupancyTrend: 2.8,
      avgLeaseDuration: 14.2,
      avgTimeToLease: 18,
      monthlyTurnover: 3.2,
      retentionRate: 78.5,
    },

    monthlyTrends: [
      {
        month: "Jan 2023",
        occupancyRate: 88,
        vacantUnits: 15,
        newLeases: 8,
        terminations: 5,
        avgRent: 2180,
      },
      {
        month: "Feb 2023",
        occupancyRate: 89,
        vacantUnits: 14,
        newLeases: 6,
        terminations: 4,
        avgRent: 2195,
      },
      {
        month: "Mar 2023",
        occupancyRate: 91,
        vacantUnits: 11,
        newLeases: 9,
        terminations: 6,
        avgRent: 2210,
      },
      {
        month: "Apr 2023",
        occupancyRate: 90,
        vacantUnits: 13,
        newLeases: 7,
        terminations: 5,
        avgRent: 2225,
      },
      {
        month: "May 2023",
        occupancyRate: 92,
        vacantUnits: 10,
        newLeases: 8,
        terminations: 4,
        avgRent: 2240,
      },
      {
        month: "Jun 2023",
        occupancyRate: 93,
        vacantUnits: 9,
        newLeases: 6,
        terminations: 3,
        avgRent: 2255,
      },
      {
        month: "Jul 2023",
        occupancyRate: 91,
        vacantUnits: 11,
        newLeases: 7,
        terminations: 5,
        avgRent: 2270,
      },
      {
        month: "Aug 2023",
        occupancyRate: 89,
        vacantUnits: 14,
        newLeases: 8,
        terminations: 7,
        avgRent: 2285,
      },
      {
        month: "Sep 2023",
        occupancyRate: 90,
        vacantUnits: 13,
        newLeases: 9,
        terminations: 6,
        avgRent: 2300,
      },
      {
        month: "Oct 2023",
        occupancyRate: 92,
        vacantUnits: 10,
        newLeases: 7,
        terminations: 4,
        avgRent: 2315,
      },
      {
        month: "Nov 2023",
        occupancyRate: 94,
        vacantUnits: 8,
        newLeases: 5,
        terminations: 3,
        avgRent: 2330,
      },
      {
        month: "Dec 2023",
        occupancyRate: 92,
        vacantUnits: 10,
        newLeases: 6,
        terminations: 4,
        avgRent: 2345,
      },
    ],

    turnoverAnalysis: [
      { quarter: "Q1 2023", turnoverRate: 12.5, avgNotice: 45, earlyTerminations: 2, renewals: 85 },
      { quarter: "Q2 2023", turnoverRate: 10.8, avgNotice: 52, earlyTerminations: 1, renewals: 88 },
      { quarter: "Q3 2023", turnoverRate: 14.2, avgNotice: 38, earlyTerminations: 4, renewals: 82 },
      { quarter: "Q4 2023", turnoverRate: 9.5, avgNotice: 58, earlyTerminations: 1, renewals: 91 },
    ],

    retentionMetrics: [
      { tenure: "< 6 months", count: 8, percentage: 7 },
      { tenure: "6-12 months", count: 15, percentage: 13 },
      { tenure: "1-2 years", count: 32, percentage: 28 },
      { tenure: "2-3 years", count: 28, percentage: 24 },
      { tenure: "3-5 years", count: 22, percentage: 19 },
      { tenure: "5+ years", count: 10, percentage: 9 },
    ],

    propertyComparison: [
      {
        property: "Sunset Apartments",
        units: 48,
        occupancy: 95.8,
        avgLease: 16.2,
        turnover: 8.5,
        retention: 84.2,
        avgRent: 2280,
      },
      {
        property: "Downtown Lofts",
        units: 36,
        occupancy: 91.7,
        avgLease: 13.8,
        turnover: 11.2,
        retention: 76.8,
        avgRent: 3150,
      },
      {
        property: "Garden View Complex",
        units: 41,
        occupancy: 89.0,
        avgLease: 12.4,
        turnover: 15.6,
        retention: 74.1,
        avgRent: 1950,
      },
    ],

    seasonalPatterns: [
      { month: "Jan", moveIns: 12, moveOuts: 8, netChange: 4 },
      { month: "Feb", moveIns: 10, moveOuts: 6, netChange: 4 },
      { month: "Mar", moveIns: 18, moveOuts: 12, netChange: 6 },
      { month: "Apr", moveIns: 15, moveOuts: 9, netChange: 6 },
      { month: "May", moveIns: 22, moveOuts: 14, netChange: 8 },
      { month: "Jun", moveIns: 28, moveOuts: 18, netChange: 10 },
      { month: "Jul", moveIns: 25, moveOuts: 20, netChange: 5 },
      { month: "Aug", moveIns: 30, moveOuts: 25, netChange: 5 },
      { month: "Sep", moveIns: 20, moveOuts: 15, netChange: 5 },
      { month: "Oct", moveIns: 14, moveOuts: 10, netChange: 4 },
      { month: "Nov", moveIns: 8, moveOuts: 5, netChange: 3 },
      { month: "Dec", moveIns: 6, moveOuts: 4, netChange: 2 },
    ],
  };

  const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#8dd1e1", "#d084d0"];

  const getMetricColor = (current: number, target: number, higherIsBetter = true) => {
    const ratio = current / target;
    if (higherIsBetter) {
      if (ratio >= 1) return "text-success";
      if (ratio >= 0.9) return "text-warning";
      return "text-destructive";
    } else {
      if (ratio <= 1) return "text-success";
      if (ratio <= 1.1) return "text-warning";
      return "text-destructive";
    }
  };

  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;
  const formatDays = (value: number) => `${value.toFixed(0)} days`;
  const formatMonths = (value: number) => `${value.toFixed(1)} months`;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Occupancy Analytics</h2>
          <p className="text-sm text-muted-foreground">
            Comprehensive occupancy metrics, turnover analysis, and retention insights
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedProperty} onValueChange={setSelectedProperty}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All Properties" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              <SelectItem value="sunset">Sunset Apartments</SelectItem>
              <SelectItem value="downtown">Downtown Lofts</SelectItem>
              <SelectItem value="garden">Garden View Complex</SelectItem>
            </SelectContent>
          </Select>
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="quarter">Quarter</SelectItem>
              <SelectItem value="year">Year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 size-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Occupancy Rate</p>
                <p className="text-2xl font-bold">
                  {formatPercentage(occupancyData.summary.currentOccupancyRate)}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">
                    +{occupancyData.summary.occupancyTrend}%
                  </span>
                </div>
              </div>
              <div className="rounded-full bg-success/10 p-3">
                <Home className="size-6 text-success" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Time to Lease</p>
                <p className="text-2xl font-bold">
                  {formatDays(occupancyData.summary.avgTimeToLease)}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingDown className="size-3 text-success" />
                  <span className="text-xs text-success">-2.5 days</span>
                </div>
              </div>
              <div className="rounded-full bg-primary/10 p-3">
                <Clock className="size-6 text-primary" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Retention Rate</p>
                <p className="text-2xl font-bold">
                  {formatPercentage(occupancyData.summary.retentionRate)}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">+1.8%</span>
                </div>
              </div>
              <div className="rounded-full bg-secondary/10 p-3">
                <Users className="size-6 text-secondary-foreground" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Monthly Turnover</p>
                <p className="text-2xl font-bold">
                  {formatPercentage(occupancyData.summary.monthlyTurnover)}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingDown className="size-3 text-success" />
                  <span className="text-xs text-success">-0.8%</span>
                </div>
              </div>
              <div className="rounded-full bg-warning/10 p-3">
                <Activity className="size-6 text-warning" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Content */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="trends">Occupancy Trends</TabsTrigger>
          <TabsTrigger value="turnover">Turnover Analysis</TabsTrigger>
          <TabsTrigger value="retention">Retention Metrics</TabsTrigger>
          <TabsTrigger value="comparison">Property Comparison</TabsTrigger>
          <TabsTrigger value="seasonal">Seasonal Patterns</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* Main Occupancy Chart */}
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="size-5" />
                  <span>Occupancy Rate Trends</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <ComposedChart data={occupancyData.monthlyTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <Tooltip />
                    <Legend />
                    <Area
                      yAxisId="left"
                      type="monotone"
                      dataKey="occupancyRate"
                      stroke="#8884d8"
                      fill="#8884d8"
                      fillOpacity={0.6}
                      name="Occupancy Rate (%)"
                    />
                    <Bar yAxisId="right" dataKey="vacantUnits" fill="#82ca9d" name="Vacant Units" />
                  </ComposedChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Current Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="size-5" />
                  <span>Current Status</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-success">
                    {occupancyData.summary.occupiedUnits}
                  </div>
                  <p className="text-sm text-muted-foreground">Occupied Units</p>
                  <div className="text-lg text-muted-foreground">
                    out of {occupancyData.summary.totalUnits} total
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="mb-1 flex justify-between text-sm">
                      <span>Occupancy Rate</span>
                      <span className="font-medium">
                        {formatPercentage(occupancyData.summary.currentOccupancyRate)}
                      </span>
                    </div>
                    <Progress value={occupancyData.summary.currentOccupancyRate} className="h-2" />
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="rounded-lg bg-success/10 p-3 text-center">
                      <div className="font-semibold text-success">
                        {occupancyData.summary.occupiedUnits}
                      </div>
                      <div className="text-success">Occupied</div>
                    </div>
                    <div className="rounded-lg bg-warning/10 p-3 text-center">
                      <div className="font-semibold text-warning">
                        {occupancyData.summary.vacantUnits}
                      </div>
                      <div className="text-warning">Vacant</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Leasing Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Monthly Leasing Activity</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={occupancyData.monthlyTrends}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="newLeases" fill="#8884d8" name="New Leases" />
                  <Bar dataKey="terminations" fill="#ff7300" name="Terminations" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="turnover" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Turnover Rate Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Quarterly Turnover Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={occupancyData.turnoverAnalysis}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="quarter" />
                    <YAxis />
                    <Tooltip formatter={(value) => [`${value}%`, ""]} />
                    <Line
                      type="monotone"
                      dataKey="turnoverRate"
                      stroke="#8884d8"
                      strokeWidth={3}
                      name="Turnover Rate (%)"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Turnover Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Turnover Insights</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {occupancyData.turnoverAnalysis.map((quarter, index) => (
                  <div key={index} className="rounded-lg border p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <h4 className="font-medium">{quarter.quarter}</h4>
                      <Badge variant="outline">{quarter.turnoverRate}% turnover</Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Avg Notice: </span>
                        <span className="font-medium">{quarter.avgNotice} days</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Renewals: </span>
                        <span className="font-medium">{quarter.renewals}%</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Early Terms: </span>
                        <span className="font-medium">{quarter.earlyTerminations}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Turnover Reasons */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Top Turnover Reasons</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { reason: "Job Relocation", percentage: 35 },
                    { reason: "Rent Increase", percentage: 22 },
                    { reason: "Lifestyle Change", percentage: 18 },
                    { reason: "Property Issues", percentage: 15 },
                    { reason: "Other", percentage: 10 },
                  ].map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm">{item.reason}</span>
                      <div className="flex items-center space-x-2">
                        <Progress value={item.percentage} className="h-2 w-16" />
                        <span className="text-sm font-medium">{item.percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Exit Indicators</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span>Late Payments</span>
                    <Badge variant="outline" className="text-destructive">
                      High Risk
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Maintenance Complaints</span>
                    <Badge variant="outline" className="text-warning">
                      Medium Risk
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Noise Complaints</span>
                    <Badge variant="outline" className="text-warning">
                      Medium Risk
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span>Lease Inquiries</span>
                    <Badge variant="outline" className="text-warning">
                      Watch
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Retention Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="rounded border-l-4 border-success bg-success/10 p-2">
                    <div className="font-medium">Lease Renewal Program</div>
                    <div className="text-muted-foreground">85% success rate</div>
                  </div>
                  <div className="rounded border-l-4 border-primary bg-primary/10 p-2">
                    <div className="font-medium">Early Intervention</div>
                    <div className="text-muted-foreground">Issue resolution</div>
                  </div>
                  <div className="rounded border-l-4 border-secondary bg-secondary/10 p-2">
                    <div className="font-medium">Incentive Programs</div>
                    <div className="text-muted-foreground">Rent discounts</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="retention" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Retention by Tenure */}
            <Card>
              <CardHeader>
                <CardTitle>Tenant Tenure Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={occupancyData.retentionMetrics}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      dataKey="count"
                      label={({ tenure, percentage }) => `${tenure}: ${percentage}%`}>
                      {occupancyData.retentionMetrics.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Retention Strategies */}
            <Card>
              <CardHeader>
                <CardTitle>Retention Strategy Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {[
                  { strategy: "Lease Renewal Incentives", success: 87, cost: 150 },
                  { strategy: "Maintenance Response", success: 82, cost: 75 },
                  { strategy: "Community Events", success: 78, cost: 200 },
                  { strategy: "Rent Stabilization", success: 92, cost: 300 },
                  { strategy: "Upgrade Programs", success: 85, cost: 500 },
                ].map((strategy, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">{strategy.strategy}</span>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{strategy.success}% success</Badge>
                        <span className="text-xs text-muted-foreground">
                          ${strategy.cost}/tenant
                        </span>
                      </div>
                    </div>
                    <Progress value={strategy.success} className="h-2" />
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>

          {/* Detailed Retention Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Retention Analysis by Tenure</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {occupancyData.retentionMetrics.map((metric, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="text-center">
                        <h4 className="mb-2 font-medium">{metric.tenure}</h4>
                        <div
                          className="text-2xl font-bold"
                          style={{ color: COLORS[index % COLORS.length] }}>
                          {metric.count}
                        </div>
                        <p className="text-sm text-muted-foreground">tenants</p>
                        <div className="mt-2">
                          <Progress value={metric.percentage * 3} className="h-2" />
                          <p className="mt-1 text-xs text-muted-foreground">
                            {metric.percentage}% of portfolio
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Property Performance Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {occupancyData.propertyComparison.map((property, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="mb-4 flex items-center justify-between">
                        <h4 className="text-lg font-medium">{property.property}</h4>
                        <Badge variant="outline">{property.units} units</Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-4 md:grid-cols-5">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-success">
                            {formatPercentage(property.occupancy)}
                          </div>
                          <p className="text-xs text-muted-foreground">Occupancy</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-primary">
                            {formatMonths(property.avgLease)}
                          </div>
                          <p className="text-xs text-muted-foreground">Avg Lease</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-warning">
                            {formatPercentage(property.turnover)}
                          </div>
                          <p className="text-xs text-muted-foreground">Turnover</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-secondary-foreground">
                            {formatPercentage(property.retention)}
                          </div>
                          <p className="text-xs text-muted-foreground">Retention</p>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-accent-foreground">
                            ${property.avgRent.toLocaleString()}
                          </div>
                          <p className="text-xs text-muted-foreground">Avg Rent</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="seasonal" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Seasonal Move-in/Move-out Patterns</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={350}>
                <BarChart data={occupancyData.seasonalPatterns}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="moveIns" fill="#8884d8" name="Move-ins" />
                  <Bar dataKey="moveOuts" fill="#ff7300" name="Move-outs" />
                  <Bar dataKey="netChange" fill="#82ca9d" name="Net Change" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Peak Season</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-success">Summer</div>
                  <p className="mb-4 text-sm text-muted-foreground">June - August</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Move-ins:</span>
                      <span className="font-medium">83</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Move-outs:</span>
                      <span className="font-medium">63</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Net gain:</span>
                      <span className="font-medium text-success">+20</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Low Season</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">Winter</div>
                  <p className="mb-4 text-sm text-muted-foreground">December - February</p>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Move-ins:</span>
                      <span className="font-medium">28</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Move-outs:</span>
                      <span className="font-medium">18</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Net gain:</span>
                      <span className="font-medium text-primary">+10</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-base">Planning Insights</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="rounded border-l-4 border-primary bg-primary/10 p-2">
                    <div className="font-medium">Marketing Focus</div>
                    <div className="text-muted-foreground">Increase efforts in Q2</div>
                  </div>
                  <div className="rounded border-l-4 border-success bg-success/10 p-2">
                    <div className="font-medium">Maintenance Window</div>
                    <div className="text-muted-foreground">Winter months optimal</div>
                  </div>
                  <div className="rounded border-l-4 border-secondary bg-secondary/10 p-2">
                    <div className="font-medium">Staffing Adjustment</div>
                    <div className="text-muted-foreground">Summer peak preparation</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
