import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import { DateColumn, TextColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { authProtectedPaths } from "@/constants/paths";

import { Payment } from "../../types";

const ActionGroupPayment = ({
  useDeletePaymentMutation,
  row,
  isDeleting,
}: {
  useDeletePaymentMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Payment>;
}) => {
  const router = useRouter();
  const payment = row.original;
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(authProtectedPaths.PAYMENTS_ID.replace(":id", payment.id) as any);
    });
  }, [router, payment.id]);

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.PAYMENTS_ID_EDIT.replace(":id", payment.id) as any);
  }, [router, payment.id]);

  const handleDelete = useCallback(async () => {
    return useDeletePaymentMutation.mutateAsync(payment.id);
  }, [useDeletePaymentMutation, payment.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};

export const columns = (
  useDeletePaymentMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<Payment>[] => [
  {
    id: "payment",
    accessorKey: "payment",
    header: t("pages.payments.headers.paymentInfo"),
    sorter: true,
    isMainColumn: true,
    sortKey: "reference_number",
    cell: ({ row }: { row: Row<Payment> }) => {
      const payment = row.original;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn
            text={payment?.reference_number || `Payment #${payment?.id?.slice(-8)}`}
            className="font-medium"
          />
          <TextColumn
            text={payment?.payment_type || "rent"}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "amount",
    accessorKey: "amount",
    header: t("pages.payments.headers.amount"),
    sorter: true,
    sortKey: "amount",
    cell: ({ row }: { row: Row<Payment> }) => {
      const payment = row.original;
      const amount = payment?.amount ? `$${payment.amount.toLocaleString()}` : "N/A";
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={amount} className="font-medium" />
          <TextColumn
            text={payment?.payment_method || ""}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "contract",
    accessorKey: "contract",
    header: t("pages.payments.headers.contract"),
    sorter: true,
    sortKey: "contract_id",
    cell: ({ row }: { row: Row<Payment> }) => {
      const payment = row.original;
      const contractInfo = payment?.contract ? `Contract #${payment.contract.id.slice(-8)}` : "N/A";
      const tenantName = payment?.contract?.tenant
        ? `${payment.contract.tenant.first_name} ${payment.contract.tenant.last_name}`
        : "";
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={contractInfo} className="font-medium" />
          <TextColumn text={tenantName} className="text-xs text-muted-foreground opacity-60" />
        </div>
      );
    },
  },
  {
    id: "property",
    accessorKey: "property",
    header: t("pages.payments.headers.property"),
    sorter: false,
    cell: ({ row }: { row: Row<Payment> }) => {
      const payment = row.original;
      const propertyName = payment?.contract?.property?.name || "N/A";
      const unitNumber = payment?.contract?.unit?.unit_number;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={propertyName} className="font-medium" />
          <TextColumn
            text={unitNumber ? `Unit: ${unitNumber}` : ""}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("pages.payments.headers.status"),
    sorter: true,
    sortKey: "status",
    cell: ({ row }: { row: Row<Payment> }) => {
      const payment = row.original;
      const status = payment?.status || "pending";
      return <TextColumn text={t(`pages.payments.status.${status}`)} className="capitalize" />;
    },
  },
  {
    id: "payment_date",
    accessorKey: "payment_date",
    header: t("pages.payments.headers.paymentDate"),
    sorter: true,
    sortKey: "payment_date",
    cell: ({ row }: { row: Row<Payment> }) => <DateColumn date={row.original.payment_date} />,
  },
  {
    id: "created_at",
    accessorKey: "created_at",
    header: t("pages.payments.headers.createdAt"),
    sorter: true,
    sortKey: "created_at",
    cell: ({ row }: { row: Row<Payment> }) => <DateColumn date={row.original.created_at} />,
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.payments.headers.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Payment> }) => <DateColumn date={row.original.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.payments.headers.actions"),
    cell: ({ row }: { row: Row<Payment> }) => (
      <ActionGroupPayment
        useDeletePaymentMutation={useDeletePaymentMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];
