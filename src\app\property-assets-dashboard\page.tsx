import { Suspense } from "react";
import { Metada<PERSON> } from "next";
import Loading from "@/app/loading";

import { PropertyAssetsDashboard } from "@/features/property-assets/components/Dashboard";

export const metadata: Metadata = {
  title: "Property Assets Dashboard | OneX ERP",
  description: "Property Assets management dashboard with metrics, analytics, and overview",
};

export default function PropertyAssetsDashboardPage() {
  return (
    <div className="p-6">
      <Suspense
        fallback={
          <div className="flex h-64 items-center justify-center">
            <Loading />
          </div>
        }>
        <PropertyAssetsDashboard />
      </Suspense>
    </div>
  );
}
