#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { performLogin } from './utils/auth.mjs';

const BASE_URL = 'http://localhost:3000';

async function testPropertyUIRefactor() {
  let browser;
  
  try {
    console.log('🚀 Starting Property UI Refactor Test...');
    
    // Launch browser
    browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 }
    });
    
    const page = await browser.newPage();
    
    // Login
    console.log('🔐 Logging in...');
    await performLogin(page);
    
    // Navigate to properties page
    console.log('📍 Navigating to Properties page...');
    await page.goto(`${BASE_URL}/property-assets/properties`, {
      waitUntil: 'networkidle0'
    });
    
    // Wait for table to load
    await page.waitForSelector('[data-testid="table-container"]', { timeout: 10000 });
    
    // Take screenshot of properties list
    console.log('📸 Taking screenshot of Properties List...');
    await page.screenshot({
      path: 'screenshots/properties-list-refactored.png',
      fullPage: true
    });
    
    // Click on create property button
    console.log('➕ Navigating to Create Property form...');
    await page.click('button:has-text("Properties")');
    await page.waitForTimeout(1000);
    await page.click('a:has-text("Add Manually")');
    
    // Wait for form to load
    await page.waitForSelector('form#property-form', { timeout: 10000 });
    
    // Take screenshot of create form
    console.log('📸 Taking screenshot of Create Property form...');
    await page.screenshot({
      path: 'screenshots/property-create-form-refactored.png',
      fullPage: true
    });
    
    // Test dark mode
    console.log('🌙 Testing dark mode...');
    // Toggle dark mode if available
    const darkModeToggle = await page.$('[data-testid="theme-toggle"]');
    if (darkModeToggle) {
      await darkModeToggle.click();
      await page.waitForTimeout(1000);
      
      // Take dark mode screenshots
      await page.screenshot({
        path: 'screenshots/property-create-form-dark-mode.png',
        fullPage: true
      });
    }
    
    // Verify semantic colors are applied
    console.log('🎨 Verifying semantic theme colors...');
    
    // Check for hardcoded colors that should not exist
    const badColors = await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      const issues = [];
      
      elements.forEach(el => {
        const classes = el.className.toString();
        if (classes.includes('bg-blue-500') || 
            classes.includes('text-green-600') || 
            classes.includes('bg-red-500') ||
            classes.includes('text-gray-500') ||
            classes.includes('bg-orange-500') ||
            classes.includes('bg-purple-500') ||
            classes.includes('bg-pink-500')) {
          issues.push({
            element: el.tagName,
            classes: classes,
            text: el.textContent?.substring(0, 50)
          });
        }
      });
      
      return issues;
    });
    
    if (badColors.length > 0) {
      console.error('❌ Found hardcoded colors that should use semantic theme colors:');
      badColors.forEach(issue => {
        console.error(`  - ${issue.element}: ${issue.classes}`);
      });
    } else {
      console.log('✅ No hardcoded colors found - all using semantic theme colors!');
    }
    
    // Check for console errors
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });
    
    console.log('✅ Property UI Refactor Test completed!');
    console.log('📁 Screenshots saved to screenshots/ directory');
    
    if (consoleErrors.length > 0) {
      console.warn('⚠️  Console errors detected:');
      consoleErrors.forEach(err => console.warn(`  - ${err}`));
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    process.exit(1);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
testPropertyUIRefactor();