#!/usr/bin/env node
import puppeteer from 'puppeteer';

async function validateTranslations() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('🔍 Testing translation system...');
    
    // Navigate to login first to see the form
    await page.goto('http://localhost:3000/login', { waitUntil: 'networkidle2' });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Take screenshot of login
    await page.screenshot({ 
      path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/login-page.png',
      fullPage: true 
    });
    
    // Check if we can see the login form
    const loginFormText = await page.evaluate(() => {
      return document.body.innerText;
    });
    
    console.log('Login page content sample:', loginFormText.substring(0, 200));
    
    // Try to find and fill login form manually
    try {
      // Look for common input patterns
      const emailSelectors = [
        'input[type="email"]',
        'input[name="email"]', 
        'input[placeholder*="email" i]',
        'input[placeholder*="Email"]',
        'input[id*="email"]'
      ];
      
      let emailInput = null;
      for (const selector of emailSelectors) {
        try {
          emailInput = await page.$(selector);
          if (emailInput) {
            console.log(`Found email input with selector: ${selector}`);
            break;
          }
        } catch (e) {
          // Continue to next selector
        }
      }
      
      if (emailInput) {
        await emailInput.type('onexapis_admin', { delay: 100 });
        
        const passwordInput = await page.$('input[type="password"]') || await page.$('input[name="password"]');
        if (passwordInput) {
          await passwordInput.type('Admin@123', { delay: 100 });
          
          // Try to find submit button
          const submitButton = await page.$('button[type="submit"]') || 
                              await page.$('form button') ||
                              await page.$('button');
          
          if (submitButton) {
            await submitButton.click();
            await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 });
            
            // Now navigate to properties
            await page.goto('http://localhost:3000/property-assets/properties', { waitUntil: 'networkidle2' });
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Check translations on properties page
            const propertiesPageContent = await page.evaluate(() => {
              const content = document.body.innerText;
              return {
                fullContent: content,
                hasTranslationKeys: content.includes('pages.properties.') || content.includes('common.'),
                hasProperText: content.includes('Properties') || content.includes('Add Property'),
                textSample: content.substring(0, 500)
              };
            });
            
            console.log('🏠 Properties Page Analysis:');
            console.log('Has translation keys:', propertiesPageContent.hasTranslationKeys);
            console.log('Has proper text:', propertiesPageContent.hasProperText);
            console.log('Content sample:', propertiesPageContent.textSample);
            
            // Take screenshot
            await page.screenshot({ 
              path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-after-login.png',
              fullPage: true 
            });
            
            return {
              success: true,
              loginSuccessful: true,
              propertiesData: propertiesPageContent
            };
          }
        }
      }
      
      console.log('❌ Could not complete login form');
      return { success: false, loginSuccessful: false, error: 'Login form elements not found' };
      
    } catch (loginError) {
      console.log('❌ Login error:', loginError.message);
      return { success: false, loginSuccessful: false, error: loginError.message };
    }
    
  } catch (error) {
    console.error('❌ Validation error:', error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run validation
validateTranslations().then(result => {
  if (result.success && result.loginSuccessful) {
    console.log('\\n🎯 TRANSLATION VALIDATION COMPLETE');
    console.log('Properties page has translation keys:', result.propertiesData.hasTranslationKeys);
    console.log('Properties page has proper text:', result.propertiesData.hasProperText);
    
    if (result.propertiesData.hasTranslationKeys) {
      console.log('⚠️  Translation keys detected - translations not loading properly');
    } else if (result.propertiesData.hasProperText) {
      console.log('✅ Translations appear to be working');
    } else {
      console.log('❓ Unable to determine translation status');
    }
  } else {
    console.log('\\n❌ Validation failed:', result.error);
  }
}).catch(console.error);