"use client";

import { useUnit } from "../../hooks/useUnits";
import { UnitForm } from "./UnitForm";

interface EditUnitFormProps {
  unitId: string;
}

export function EditUnitForm({ unitId }: EditUnitFormProps) {
  const { data: unit, isLoading, error } = useUnit(unitId);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto size-8 animate-spin rounded-full border-b-2 border-primary"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading unit...</p>
        </div>
      </div>
    );
  }

  if (error || !unit) {
    return (
      <div className="flex h-screen items-center justify-center">
        <div className="text-center">
          <p className="text-sm text-destructive">Failed to load unit</p>
        </div>
      </div>
    );
  }

  return <UnitForm initialData={unit} isEditing={true} />;
}
