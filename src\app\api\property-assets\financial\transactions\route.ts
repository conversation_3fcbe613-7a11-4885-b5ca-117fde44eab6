import { NextRequest, NextResponse } from "next/server";

import type { CreateTransaction, Transaction } from "@/features/property-assets/types";

import { transactionsMockData } from "../../mock-data";

// GET /api/property-assets/financial/transactions
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const type = searchParams.get("type") as Transaction["type"] | null;
    const category = searchParams.get("category") || "";
    const property_id = searchParams.get("property_id") || "";
    const start_date = searchParams.get("start_date") || "";
    const end_date = searchParams.get("end_date") || "";

    let filteredTransactions = [...transactionsMockData];

    // Apply property filter
    if (property_id) {
      filteredTransactions = filteredTransactions.filter(
        (transaction) => transaction.property_id === property_id
      );
    }

    // Apply date range filter
    if (start_date) {
      filteredTransactions = filteredTransactions.filter(
        (transaction) => new Date(transaction.date) >= new Date(start_date)
      );
    }
    if (end_date) {
      filteredTransactions = filteredTransactions.filter(
        (transaction) => new Date(transaction.date) <= new Date(end_date)
      );
    }

    // Apply search filter
    if (search) {
      filteredTransactions = filteredTransactions.filter(
        (transaction) =>
          transaction.description.toLowerCase().includes(search.toLowerCase()) ||
          transaction.category.toLowerCase().includes(search.toLowerCase()) ||
          (transaction.reference_number &&
            transaction.reference_number.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply type filter
    if (type) {
      filteredTransactions = filteredTransactions.filter(
        (transaction) => transaction.type === type
      );
    }

    // Apply category filter
    if (category) {
      filteredTransactions = filteredTransactions.filter((transaction) =>
        transaction.category.toLowerCase().includes(category.toLowerCase())
      );
    }

    // Sort by date (newest first)
    filteredTransactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTransactions = filteredTransactions.slice(startIndex, endIndex);

    // Calculate totals for the filtered results
    const totalIncome = filteredTransactions
      .filter((t) => t.type === "income")
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = Math.abs(
      filteredTransactions.filter((t) => t.type === "expense").reduce((sum, t) => sum + t.amount, 0)
    );

    const netAmount = totalIncome - totalExpenses;

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 380));

    return NextResponse.json({
      items: paginatedTransactions,
      total: filteredTransactions.length,
      page,
      limit,
      totalPages: Math.ceil(filteredTransactions.length / limit),
      summary: {
        total_income: totalIncome,
        total_expenses: totalExpenses,
        net_amount: netAmount,
      },
    });
  } catch (error) {
    console.error("Error fetching transactions:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/property-assets/financial/transactions
export async function POST(request: NextRequest) {
  try {
    const body: CreateTransaction = await request.json();

    // Validate required fields
    if (
      !body.property_id ||
      !body.date ||
      !body.amount ||
      !body.type ||
      !body.category ||
      !body.description
    ) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Validate amount based on type
    if (body.type === "income" && body.amount <= 0) {
      return NextResponse.json({ error: "Income amount must be positive" }, { status: 400 });
    }

    if (body.type === "expense" && body.amount >= 0) {
      return NextResponse.json({ error: "Expense amount must be negative" }, { status: 400 });
    }

    // Validate date is not in the future
    const transactionDate = new Date(body.date);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today

    if (transactionDate > today) {
      return NextResponse.json(
        { error: "Transaction date cannot be in the future" },
        { status: 400 }
      );
    }

    // Create new transaction
    const newTransaction: Transaction = {
      id: `trans_${Date.now()}`,
      ...body,
      reference_number: body.reference_number || `TXN${Date.now()}`,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Add to mock data
    transactionsMockData.push(newTransaction);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 500));

    return NextResponse.json({
      data: newTransaction,
      success: true,
      message: "Transaction created successfully",
    });
  } catch (error) {
    console.error("Error creating transaction:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
