import { useEffect, useRef, useState } from "react";

import { cn } from "@/lib/utils";

interface EditableContentProps {
  value: number;
  onValueChange: (value: number) => void;
  className?: string;
  disabled?: boolean;
  placeholder?: string;
  suffix?: string;
  min?: number;
  max?: number;
  step?: number;
  onEnterPress?: () => void;
}

export function EditableContent({
  value,
  onValueChange,
  className = "",
  disabled = false,
  placeholder = "0.00",
  suffix = "",
  min,
  max,
  step = 1,
  onEnterPress,
}: EditableContentProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [tempValue, setTempValue] = useState(value.toString());
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setTempValue(value.toString());
  }, [value]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  const handleClick = () => {
    if (!disabled) {
      setIsEditing(true);
    }
  };

  const handleBlur = () => {
    setIsEditing(false);
    const numValue = parseFloat(tempValue) || 0;
    if (numValue !== value) {
      onValueChange(numValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      setIsEditing(false);
      const numValue = parseFloat(tempValue) || 0;
      if (numValue !== value) {
        onValueChange(numValue);
      }
      if (onEnterPress) {
        onEnterPress();
      }
    } else if (e.key === "Escape") {
      setIsEditing(false);
      setTempValue(value.toString());
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    // Only allow numbers and decimal point
    if (/^\d*\.?\d*$/.test(newValue) || newValue === "") {
      setTempValue(newValue);
    }
  };

  const formatDisplayValue = (val: number): string => {
    return val === 0 ? "0.00" : val.toFixed(2);
  };

  if (isEditing) {
    return (
      <input
        ref={inputRef}
        type="text"
        value={tempValue}
        onChange={handleInputChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className={cn(
          "max-w-[50px] border-none bg-transparent outline-none focus:outline-none focus:ring-0",
          className
        )}
        min={min}
        max={max}
        step={step}
        disabled={disabled}
      />
    );
  }

  return (
    <span
      onClick={handleClick}
      className={cn(
        "flex max-w-[100px] cursor-pointer rounded px-1 py-0.5 transition-colors hover:bg-neutral-100",
        disabled && "cursor-default hover:bg-transparent",
        className
      )}>
      <span className="truncate">
        {formatDisplayValue(value)}
        {suffix}
      </span>
    </span>
  );
}
