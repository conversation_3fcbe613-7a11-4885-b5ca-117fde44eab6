"use client";

import { useCallback, useState } from "react";
import Loading from "@/app/loading";
import {
  Calendar,
  DollarSign,
  Download,
  FileBarChart,
  Filter,
  Home,
  PieChart,
  Plus,
  RefreshCw,
  TrendingUp,
  <PERSON>,
  Wrench,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ReportCategory {
  id: string;
  icon: React.ComponentType<{ className?: string }>;
  titleKey: string;
  descriptionKey: string;
  actionKey: string;
  color: string;
}

export function ReportsComponent() {
  const { t } = useTranslation();
  const [selectedProperty, setSelectedProperty] = useState<string>("all");
  const [timeRange, setTimeRange] = useState<"month" | "quarter" | "year">("month");
  const [generatingReports, setGeneratingReports] = useState<Set<string>>(new Set());

  const reportCategories: ReportCategory[] = [
    {
      id: "financial",
      icon: DollarSign,
      titleKey: "pages.reports.categories.financial.title",
      descriptionKey: "pages.reports.categories.financial.description",
      actionKey: "pages.reports.categories.financial.generate",
      color: "bg-success/10 text-success",
    },
    {
      id: "occupancy",
      icon: Home,
      titleKey: "pages.reports.categories.occupancy.title",
      descriptionKey: "pages.reports.categories.occupancy.description",
      actionKey: "pages.reports.categories.occupancy.generate",
      color: "bg-primary/10 text-primary",
    },
    {
      id: "performance",
      icon: TrendingUp,
      titleKey: "pages.reports.categories.performance.title",
      descriptionKey: "pages.reports.categories.performance.description",
      actionKey: "pages.reports.categories.performance.generate",
      color: "bg-accent/10 text-accent-foreground",
    },
    {
      id: "maintenance",
      icon: Wrench,
      titleKey: "pages.reports.categories.maintenance.title",
      descriptionKey: "pages.reports.categories.maintenance.description",
      actionKey: "pages.reports.categories.maintenance.generate",
      color: "bg-warning/10 text-warning",
    },
    {
      id: "tenant",
      icon: Users,
      titleKey: "pages.reports.categories.tenant.title",
      descriptionKey: "pages.reports.categories.tenant.description",
      actionKey: "pages.reports.categories.tenant.generate",
      color: "bg-destructive/10 text-destructive",
    },
    {
      id: "custom",
      icon: PieChart,
      titleKey: "pages.reports.categories.custom.title",
      descriptionKey: "pages.reports.categories.custom.description",
      actionKey: "pages.reports.categories.custom.create",
      color: "bg-secondary/10 text-secondary-foreground",
    },
  ];

  const handleGenerateReport = useCallback(
    async (reportId: string) => {
      setGeneratingReports((prev) => new Set(prev).add(reportId));

      try {
        // Simulate report generation
        await new Promise((resolve) => setTimeout(resolve, 2000));

        // TODO: Implement actual API call for report generation
        // For now, we'll simulate downloading a report
        const reportName = `${reportId}-report-${timeRange}-${Date.now()}.pdf`;
        // TODO: Implement actual report download logic
      } catch (error) {
        // TODO: Implement proper error logging and user notification
      } finally {
        setGeneratingReports((prev) => {
          const newSet = new Set(prev);
          newSet.delete(reportId);
          return newSet;
        });
      }
    },
    [selectedProperty, timeRange]
  );

  return (
    <div className="space-y-8">
      {/* Header with Controls - Always Visible */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-foreground">{t("pages.reports.title")}</h1>
          <p className="text-sm text-muted-foreground">{t("pages.reports.description")}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedProperty} onValueChange={setSelectedProperty}>
            <SelectTrigger className="w-40 border-input bg-background text-foreground">
              <SelectValue placeholder="All Properties" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Properties</SelectItem>
              <SelectItem value="property1">Sunset Apartments</SelectItem>
              <SelectItem value="property2">Downtown Lofts</SelectItem>
              <SelectItem value="property3">Garden View Complex</SelectItem>
            </SelectContent>
          </Select>

          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32 border-input bg-background text-foreground">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">Month</SelectItem>
              <SelectItem value="quarter">Quarter</SelectItem>
              <SelectItem value="year">Year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Report Categories Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {reportCategories.map((category) => {
          const IconComponent = category.icon;
          const isGenerating = generatingReports.has(category.id);

          return (
            <Card key={category.id} className="transition-shadow hover:shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center space-x-3">
                  <div className={`rounded-lg p-2 ${category.color}`}>
                    <IconComponent className="size-5" />
                  </div>
                  <span className="text-lg font-semibold">{t(category.titleKey)}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-sm text-muted-foreground">{t(category.descriptionKey)}</p>

                <div className="flex items-center justify-between">
                  <Button
                    onClick={() => handleGenerateReport(category.id)}
                    disabled={isGenerating}
                    variant={category.id === "custom" ? "outline" : "default"}
                    size="sm"
                    className="w-full">
                    {isGenerating ? (
                      <>
                        <RefreshCw className="mr-2 size-4 animate-spin" />
                        {t("pages.reports.generating")}
                      </>
                    ) : (
                      <>
                        {category.id === "custom" ? (
                          <Plus className="mr-2 size-4" />
                        ) : (
                          <Download className="mr-2 size-4" />
                        )}
                        {t(category.actionKey)}
                      </>
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Recent Reports Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileBarChart className="size-5" />
            <span>Recent Reports</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[
              {
                name: "Financial Report - Q4 2024",
                type: "Financial",
                date: "2024-01-15",
                status: "Completed",
              },
              {
                name: "Occupancy Analysis - December 2024",
                type: "Occupancy",
                date: "2024-01-10",
                status: "Completed",
              },
              {
                name: "Maintenance Costs Report - 2024",
                type: "Maintenance",
                date: "2024-01-05",
                status: "Completed",
              },
            ].map((report, index) => (
              <div
                key={index}
                className="flex items-center justify-between rounded-lg bg-muted/50 p-3">
                <div className="flex items-center space-x-3">
                  <FileBarChart className="size-4 text-muted-foreground" />
                  <div>
                    <div className="text-sm font-medium">{report.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {report.type} • Generated on {report.date}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary" className="text-xs">
                    {report.status}
                  </Badge>
                  <Button variant="ghost" size="sm">
                    <Download className="size-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Coming Soon Section */}
      <Card className="border-dashed">
        <CardContent className="py-12 text-center">
          <div className="space-y-2 text-muted-foreground">
            <h3 className="text-lg font-medium">{t("pages.reports.comingSoon.title")}</h3>
            <p className="mx-auto max-w-md text-sm">{t("pages.reports.comingSoon.description")}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
