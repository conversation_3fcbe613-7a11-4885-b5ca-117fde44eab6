"use client";

import { use<PERSON><PERSON>back, useState } from "react";
import { useRouter } from "next/navigation";
import {
  AlertCircle,
  AlertTriangle,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Edit,
  Home,
  Image,
  MapPin,
  Phone,
  Settings,
  Trash2,
  User,
  Wrench,
  XCircle,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { authProtectedPaths } from "@/constants/paths";

import {
  useDeleteMaintenanceRequest,
  useMaintenanceRequest,
  useUpdateMaintenanceRequest,
} from "../../hooks/useMaintenance";
import type { MaintenanceRequest } from "../../types";

interface MaintenanceDetailsComponentProps {
  maintenanceId: string;
}

export function MaintenanceDetailsComponent({ maintenanceId }: MaintenanceDetailsComponentProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false);

  const { data: maintenance, isLoading, error } = useMaintenanceRequest(maintenanceId);
  const deleteMaintenanceMutation = useDeleteMaintenanceRequest();
  const updateMaintenanceMutation = useUpdateMaintenanceRequest();

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.MAINTENANCE_ID_EDIT.replace(":id", maintenanceId) as any);
  }, [router, maintenanceId]);

  const handleDelete = useCallback(async () => {
    try {
      await deleteMaintenanceMutation.mutateAsync(maintenanceId);
      toast.success(t("maintenance.messages.deleteSuccess"));
      router.push(authProtectedPaths.MAINTENANCE as any);
    } catch (error) {
      // TODO: Implement proper error logging
      toast.error(t("maintenance.messages.deleteError"));
    }
    setShowDeleteDialog(false);
  }, [deleteMaintenanceMutation, maintenanceId, t, router]);

  const handleStatusChange = useCallback(
    async (newStatus: MaintenanceRequest["status"]) => {
      if (!maintenance || isUpdatingStatus) return;

      setIsUpdatingStatus(true);
      try {
        await updateMaintenanceMutation.mutateAsync({
          id: maintenanceId,
          data: {
            status: newStatus,
            completed_date: newStatus === "completed" ? new Date().toISOString() : undefined,
          },
        } as any);
        toast.success(t("maintenance.messages.statusUpdateSuccess"));
      } catch (error) {
        // TODO: Implement proper error logging
        toast.error(t("maintenance.messages.statusUpdateError"));
      } finally {
        setIsUpdatingStatus(false);
      }
    },
    [maintenance, maintenanceId, updateMaintenanceMutation, t, isUpdatingStatus]
  );

  const handleViewProperty = useCallback(() => {
    if (maintenance?.property_id) {
      router.push(authProtectedPaths.PROPERTIES_ID.replace(":id", maintenance.property_id) as any);
    }
  }, [maintenance, router]);

  const handleViewUnit = useCallback(() => {
    if (maintenance?.unit_id) {
      router.push(authProtectedPaths.UNITS_ID.replace(":id", maintenance.unit_id) as any);
    }
  }, [maintenance, router]);

  const handleViewTenant = useCallback(() => {
    if (maintenance?.tenant_id) {
      router.push(authProtectedPaths.TENANTS_ID.replace(":id", maintenance.tenant_id) as any);
    }
  }, [maintenance, router]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-muted-foreground">{t("common.loading")}</div>
      </div>
    );
  }

  if (error || !maintenance) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 size-12 text-muted-foreground" />
          <h3 className="mb-2 text-lg font-medium text-foreground">
            {t("maintenance.errors.notFound")}
          </h3>
          <p className="mb-4 text-muted-foreground">
            {t("maintenance.errors.notFoundDescription")}
          </p>
          <Button onClick={() => router.push(authProtectedPaths.MAINTENANCE as any)}>
            {t("common.goBack")}
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: MaintenanceRequest["status"]) => {
    switch (status) {
      case "open":
        return "bg-warning/10 text-warning border-warning/20";
      case "in_progress":
        return "bg-primary/10 text-primary border-primary/20";
      case "completed":
        return "bg-success/10 text-success border-success/20";
      case "cancelled":
        return "bg-destructive/10 text-destructive border-destructive/20";
      default:
        return "bg-muted text-muted-foreground border-muted";
    }
  };

  const getStatusIcon = (status: MaintenanceRequest["status"]) => {
    switch (status) {
      case "open":
        return <AlertCircle className="size-4" />;
      case "in_progress":
        return <Clock className="size-4" />;
      case "completed":
        return <CheckCircle className="size-4" />;
      case "cancelled":
        return <XCircle className="size-4" />;
      default:
        return null;
    }
  };

  const getPriorityColor = (priority: MaintenanceRequest["priority"]) => {
    switch (priority) {
      case "low":
        return "bg-success/10 text-success border-success/20";
      case "medium":
        return "bg-warning/10 text-warning border-warning/20";
      case "high":
        return "bg-secondary/10 text-secondary-foreground border-secondary/20";
      case "urgent":
        return "bg-destructive/10 text-destructive border-destructive/20";
      default:
        return "bg-muted text-muted-foreground border-muted";
    }
  };

  const getCategoryIcon = (category: MaintenanceRequest["category"]) => {
    switch (category) {
      case "plumbing":
        return <Wrench className="size-4" />;
      case "electrical":
        return <AlertTriangle className="size-4" />;
      case "hvac":
        return <Settings className="size-4" />;
      case "appliance":
        return <Settings className="size-4" />;
      case "structural":
        return <Home className="size-4" />;
      case "cosmetic":
        return <Image className="size-4" />;
      default:
        return <Settings className="size-4" />;
    }
  };

  // Calculate timeline progress
  const createdDate = new Date(maintenance.reported_date);
  const today = new Date();
  const daysSinceCreated = Math.floor(
    (today.getTime() - createdDate.getTime()) / (1000 * 60 * 60 * 24)
  );

  return (
    <div className="mx-auto max-w-6xl space-y-6">
      {/* Header Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-foreground">{maintenance.title}</h1>
          <p className="text-sm text-muted-foreground">
            {t("maintenance.form.requestId")}: {maintenance.id}
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleEdit}
            className="flex items-center gap-2">
            <Edit className="size-4" />
            {t("common.edit")}
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setShowDeleteDialog(true)}
            className="flex items-center gap-2">
            <Trash2 className="size-4" />
            {t("common.delete")}
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Details */}
        <div className="space-y-6 lg:col-span-2">
          {/* Overview Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="size-5" />
                {t("maintenance.details.overview")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("maintenance.form.status")}
                  </label>
                  <div className="mt-1 flex items-center gap-2">
                    <Badge className={getStatusColor(maintenance.status)}>
                      {getStatusIcon(maintenance.status)}
                      <span className="ml-1">{t(`maintenance.status.${maintenance.status}`)}</span>
                    </Badge>
                    {maintenance.status !== "completed" && maintenance.status !== "cancelled" && (
                      <Select
                        value={maintenance.status}
                        onValueChange={handleStatusChange}
                        disabled={isUpdatingStatus}>
                        <SelectTrigger className="w-auto">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="open">{t("maintenance.status.open")}</SelectItem>
                          <SelectItem value="in_progress">
                            {t("maintenance.status.in_progress")}
                          </SelectItem>
                          <SelectItem value="completed">
                            {t("maintenance.status.completed")}
                          </SelectItem>
                          <SelectItem value="cancelled">
                            {t("maintenance.status.cancelled")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("maintenance.form.priority")}
                  </label>
                  <div className="mt-1">
                    <Badge className={getPriorityColor(maintenance.priority)}>
                      <AlertTriangle className="mr-1 size-3" />
                      {t(`maintenance.priority.${maintenance.priority}`)}
                    </Badge>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("maintenance.form.category")}
                  </label>
                  <div className="mt-1 flex items-center gap-2">
                    {getCategoryIcon(maintenance.category)}
                    <span className="text-sm">
                      {t(`maintenance.category.${maintenance.category}`)}
                    </span>
                  </div>
                </div>

                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("maintenance.form.reportedDate")}
                  </label>
                  <div className="mt-1 flex items-center gap-2">
                    <Calendar className="size-4 text-muted-foreground" />
                    <span className="text-sm">
                      {new Date(maintenance.reported_date).toLocaleDateString()}
                    </span>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("maintenance.form.description")}
                </label>
                <p className="mt-1 text-sm text-foreground">{maintenance.description}</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Timeline Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="size-5" />
                {t("maintenance.details.timeline")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3 text-sm">
                <div className="text-muted-foreground">
                  {t("maintenance.details.daysSinceReported", { days: daysSinceCreated })}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Cost Information */}
          {(maintenance.estimated_cost || maintenance.actual_cost) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="size-5" />
                  {t("maintenance.details.cost")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {maintenance.estimated_cost && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("maintenance.form.estimatedCost")}
                    </span>
                    <span className="text-sm font-medium">
                      {maintenance.estimated_cost.toLocaleString("vi-VN")} VND
                    </span>
                  </div>
                )}
                {maintenance.actual_cost && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">
                      {t("maintenance.form.actualCost")}
                    </span>
                    <span className="text-sm font-medium">
                      {maintenance.actual_cost.toLocaleString("vi-VN")} VND
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Related Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Home className="size-5" />
                {t("maintenance.details.related")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {maintenance.property && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleViewProperty}
                  className="h-auto w-full justify-start p-2">
                  <div className="flex items-center gap-2">
                    <Home className="size-4" />
                    <div className="text-left">
                      <div className="text-sm font-medium">{maintenance.property.name}</div>
                      <div className="text-xs text-muted-foreground">
                        {maintenance.property.address.street}
                      </div>
                    </div>
                  </div>
                </Button>
              )}

              {maintenance.unit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleViewUnit}
                  className="h-auto w-full justify-start p-2">
                  <div className="flex items-center gap-2">
                    <MapPin className="size-4" />
                    <div className="text-left">
                      <div className="text-sm font-medium">
                        {t("unit.title")} {maintenance.unit.unit_number}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {t("unit.type." + maintenance.unit.unit_type)}
                      </div>
                    </div>
                  </div>
                </Button>
              )}

              {maintenance.tenant && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleViewTenant}
                  className="h-auto w-full justify-start p-2">
                  <div className="flex items-center gap-2">
                    <User className="size-4" />
                    <div className="text-left">
                      <div className="text-sm font-medium">
                        {maintenance.tenant.first_name} {maintenance.tenant.last_name}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {maintenance.tenant.email}
                      </div>
                    </div>
                  </div>
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Contractor Information */}
          {(maintenance.contractor_name || maintenance.contractor_phone) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wrench className="size-5" />
                  {t("maintenance.details.contractor")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {maintenance.contractor_name && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("maintenance.form.contractorName")}
                    </label>
                    <p className="text-sm">{maintenance.contractor_name}</p>
                  </div>
                )}
                {maintenance.contractor_phone && (
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">
                      {t("maintenance.form.contractorPhone")}
                    </label>
                    <div className="flex items-center gap-2">
                      <Phone className="size-4 text-muted-foreground" />
                      <span className="text-sm">{maintenance.contractor_phone}</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Notes Section */}
      {maintenance.notes && (
        <Card>
          <CardHeader>
            <CardTitle>{t("maintenance.form.notes")}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="whitespace-pre-wrap text-sm text-foreground">{maintenance.notes}</p>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("maintenance.dialog.deleteTitle")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("maintenance.dialog.deleteDescription")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground">
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
