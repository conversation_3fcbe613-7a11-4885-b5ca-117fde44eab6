"use client";

import { useState } from "react";
import {
  BarChart3,
  Calendar,
  DollarSign,
  Filter,
  LineChart as <PERSON><PERSON>hart<PERSON><PERSON>,
  Pie<PERSON><PERSON>,
  TrendingDown,
  TrendingUp,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  CartesianGrid,
  ComposedChart,
  Legend,
  Line,
  LineChart,
  ReferenceLine,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

import { useRevenueAnalytics } from "../../hooks/useFinancial";

interface RevenueChartsProps {
  propertyId?: string;
  className?: string;
}

export function RevenueCharts({ propertyId, className }: RevenueChartsProps) {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState<"month" | "quarter" | "year">("month");
  const [chartType, setChartType] = useState<"line" | "area" | "bar" | "combined">("line");
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");

  const { data: revenueData, isLoading } = useRevenueAnalytics({
    property_id: selectedProperty === "all" ? undefined : selectedProperty,
    time_range: timeRange,
  });

  // Enhanced mock data for revenue analytics
  const monthlyRevenueData = [
    {
      month: "Jan",
      revenue: 45000,
      expenses: 12000,
      netIncome: 33000,
      occupancyRate: 92,
      avgRentPerUnit: 1250,
      collectionRate: 98,
    },
    {
      month: "Feb",
      revenue: 47000,
      expenses: 13000,
      netIncome: 34000,
      occupancyRate: 89,
      avgRentPerUnit: 1280,
      collectionRate: 97,
    },
    {
      month: "Mar",
      revenue: 46000,
      expenses: 15000,
      netIncome: 31000,
      occupancyRate: 94,
      avgRentPerUnit: 1220,
      collectionRate: 99,
    },
    {
      month: "Apr",
      revenue: 48000,
      expenses: 11000,
      netIncome: 37000,
      occupancyRate: 91,
      avgRentPerUnit: 1300,
      collectionRate: 98,
    },
    {
      month: "May",
      revenue: 50000,
      expenses: 14000,
      netIncome: 36000,
      occupancyRate: 96,
      avgRentPerUnit: 1350,
      collectionRate: 99,
    },
    {
      month: "Jun",
      revenue: 52000,
      expenses: 16000,
      netIncome: 36000,
      occupancyRate: 93,
      avgRentPerUnit: 1380,
      collectionRate: 97,
    },
  ];

  const revenueSourceData = [
    { source: "Rental Income", amount: 285000, percentage: 78, color: "#8884d8" },
    { source: "Parking Fees", amount: 35000, percentage: 10, color: "#82ca9d" },
    { source: "Laundry Income", amount: 18000, percentage: 5, color: "#ffc658" },
    { source: "Pet Fees", amount: 15000, percentage: 4, color: "#ff7300" },
    { source: "Late Fees", amount: 12000, percentage: 3, color: "#8dd1e1" },
  ];

  const yearOverYearData = [
    { period: "2022 Q1", currentYear: 42000, previousYear: 38000 },
    { period: "2022 Q2", currentYear: 45000, previousYear: 41000 },
    { period: "2022 Q3", currentYear: 47000, previousYear: 43000 },
    { period: "2022 Q4", currentYear: 49000, previousYear: 45000 },
    { period: "2023 Q1", currentYear: 51000, previousYear: 42000 },
    { period: "2023 Q2", currentYear: 53000, previousYear: 45000 },
  ];

  const renderChart = () => {
    switch (chartType) {
      case "area":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={monthlyRevenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip
                formatter={(value, name) => [
                  `$${value.toLocaleString()}`,
                  name === "revenue"
                    ? t("financial.revenue")
                    : name === "netIncome"
                      ? t("financial.netIncome")
                      : name,
                ]}
              />
              <Legend />
              <Area
                type="monotone"
                dataKey="revenue"
                stackId="1"
                stroke="#8884d8"
                fill="#8884d8"
                fillOpacity={0.6}
                name={t("financial.revenue")}
              />
              <Area
                type="monotone"
                dataKey="netIncome"
                stackId="2"
                stroke="#82ca9d"
                fill="#82ca9d"
                fillOpacity={0.6}
                name={t("financial.netIncome")}
              />
            </AreaChart>
          </ResponsiveContainer>
        );

      case "bar":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={monthlyRevenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, ""]} />
              <Legend />
              <Bar dataKey="revenue" fill="#8884d8" name={t("financial.revenue")} />
              <Bar dataKey="expenses" fill="#ff7300" name={t("financial.expenses")} />
              <Bar dataKey="netIncome" fill="#82ca9d" name={t("financial.netIncome")} />
            </BarChart>
          </ResponsiveContainer>
        );

      case "combined":
        return (
          <ResponsiveContainer width="100%" height={400}>
            <ComposedChart data={monthlyRevenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Legend />
              <Bar yAxisId="left" dataKey="revenue" fill="#8884d8" name={t("financial.revenue")} />
              <Bar
                yAxisId="left"
                dataKey="expenses"
                fill="#ff7300"
                name={t("financial.expenses")}
              />
              <Line
                yAxisId="right"
                type="monotone"
                dataKey="occupancyRate"
                stroke="#82ca9d"
                strokeWidth={3}
                name="Occupancy Rate (%)"
              />
            </ComposedChart>
          </ResponsiveContainer>
        );

      default: // line
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={monthlyRevenueData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, ""]} />
              <Legend />
              <Line
                type="monotone"
                dataKey="revenue"
                stroke="#8884d8"
                strokeWidth={3}
                dot={{ fill: "#8884d8", strokeWidth: 2, r: 5 }}
                name={t("financial.revenue")}
              />
              <Line
                type="monotone"
                dataKey="netIncome"
                stroke="#82ca9d"
                strokeWidth={3}
                dot={{ fill: "#82ca9d", strokeWidth: 2, r: 5 }}
                name={t("financial.netIncome")}
              />
              <ReferenceLine y={45000} stroke="red" strokeDasharray="5 5" label="Target" />
            </LineChart>
          </ResponsiveContainer>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex h-96 items-center justify-center">
        <div className="size-8 animate-spin rounded-full border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Revenue Analytics Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">
            {t("financial.revenueAnalytics.title")}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t("financial.revenueAnalytics.description")}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="month">{t("financial.timeRange.month")}</SelectItem>
              <SelectItem value="quarter">{t("financial.timeRange.quarter")}</SelectItem>
              <SelectItem value="year">{t("financial.timeRange.year")}</SelectItem>
            </SelectContent>
          </Select>
          <Select value={chartType} onValueChange={(value: any) => setChartType(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="line">Line Chart</SelectItem>
              <SelectItem value="area">Area Chart</SelectItem>
              <SelectItem value="bar">Bar Chart</SelectItem>
              <SelectItem value="combined">Combined</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Revenue KPI Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Revenue</p>
                <p className="text-2xl font-bold">
                  ${revenueData?.total_revenue?.toLocaleString() || "285,000"}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">
                    +{revenueData?.revenue_growth || 12.5}%
                  </span>
                </div>
              </div>
              <DollarSign className="size-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Monthly</p>
                <p className="text-2xl font-bold">
                  ${revenueData?.average_monthly_revenue?.toLocaleString() || "47,500"}
                </p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">+8.2%</span>
                </div>
              </div>
              <BarChart3 className="size-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Collection Rate</p>
                <p className="text-2xl font-bold">98.2%</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">+0.5%</span>
                </div>
              </div>
              <PieChart className="size-8 text-secondary-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Rent/Unit</p>
                <p className="text-2xl font-bold">$1,280</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-success" />
                  <span className="text-xs text-success">+4.1%</span>
                </div>
              </div>
              <LineChartIcon className="size-8 text-warning" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Revenue Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="size-5" />
            <span>{t("financial.charts.revenueAnalysis")}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>{renderChart()}</CardContent>
      </Card>

      {/* Revenue Analytics Tabs */}
      <Tabs defaultValue="sources" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sources">Revenue Sources</TabsTrigger>
          <TabsTrigger value="comparison">Year Comparison</TabsTrigger>
          <TabsTrigger value="forecasting">Forecasting</TabsTrigger>
        </TabsList>

        <TabsContent value="sources" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue by Source</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {revenueSourceData.map((source, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-3">
                    <div className="flex items-center space-x-3">
                      <div className="size-4 rounded" style={{ backgroundColor: source.color }} />
                      <div>
                        <p className="font-medium">{source.source}</p>
                        <p className="text-sm text-muted-foreground">
                          {source.percentage}% of total
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold">${source.amount.toLocaleString()}</p>
                      <Badge variant="outline">{source.percentage}%</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="comparison" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Year-over-Year Comparison</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={yearOverYearData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value.toLocaleString()}`, ""]} />
                  <Legend />
                  <Bar dataKey="currentYear" fill="#8884d8" name="2023" />
                  <Bar dataKey="previousYear" fill="#82ca9d" name="2022" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="forecasting" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Revenue Forecasting</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                <div className="rounded-lg bg-primary/10 p-4 text-center">
                  <h4 className="font-semibold text-primary">Next Month</h4>
                  <p className="text-2xl font-bold text-primary">$54,200</p>
                  <p className="text-sm text-primary/90">+4.2% projected</p>
                </div>
                <div className="rounded-lg bg-success/10 p-4 text-center">
                  <h4 className="font-semibold text-success">Next Quarter</h4>
                  <p className="text-2xl font-bold text-success">$158,500</p>
                  <p className="text-sm text-success/90">+6.8% projected</p>
                </div>
                <div className="rounded-lg bg-secondary/10 p-4 text-center">
                  <h4 className="font-semibold text-secondary-foreground">Annual Target</h4>
                  <p className="text-2xl font-bold text-secondary-foreground">$625,000</p>
                  <p className="text-sm text-secondary-foreground/90">On track: 91%</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
