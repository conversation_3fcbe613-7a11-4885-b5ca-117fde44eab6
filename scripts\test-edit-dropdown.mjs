#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

const TEST_URL = 'http://localhost:3000';

async function testEditDropdown() {
  console.log('🧪 Testing Edit Mode via Dropdown...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  const page = await browser.newPage();

  try {
    // Navigate and open mapping interface
    console.log('🗺️ Navigating to Unit Mapping Interface...');
    const authSuccess = await navigateWithAuth(page, `${TEST_URL}/property-assets/layout`);
    if (!authSuccess) throw new Error('Authentication failed');
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Click Open Mapping
    const openMappingFound = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const openMappingBtn = buttons.find(btn => btn.textContent.includes('Open Mapping'));
      if (openMappingBtn) {
        openMappingBtn.click();
        return true;
      }
      return false;
    });
    
    if (!openMappingFound) throw new Error('Open Mapping button not found');
    console.log('✅ Navigated to Unit Mapping Interface\n');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Look for the grid icon dropdown trigger
    console.log('🔍 Looking for layout selector dropdown (grid icon)...');
    
    const dropdownFound = await page.evaluate(() => {
      // Find the dropdown trigger button with grid icon near "Select Layout" text
      const allButtons = Array.from(document.querySelectorAll('button'));
      const gridButton = allButtons.find(btn => {
        const hasGridIcon = btn.querySelector('svg[data-lucide="grid-3-x-3"]');
        return hasGridIcon;
      });
      
      if (gridButton) {
        console.log('Found grid button, clicking...');
        gridButton.click();
        return true;
      }
      
      return false;
    });
    
    console.log(`   - Grid dropdown button found: ${dropdownFound}`);
    
    if (dropdownFound) {
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Look for edit menu item
      console.log('📝 Looking for Edit option in dropdown...');
      
      const editItemFound = await page.evaluate(() => {
        const menuItems = Array.from(document.querySelectorAll('[role="menuitem"]'));
        const editItem = menuItems.find(item => {
          const hasEditIcon = item.querySelector('svg[data-lucide="edit"]');
          const hasEditText = item.textContent.includes('Edit');
          return hasEditIcon || hasEditText;
        });
        
        if (editItem) {
          console.log('Found edit menu item, clicking...');
          editItem.click();
          return true;
        }
        
        return false;
      });
      
      console.log(`   - Edit menu item found: ${editItemFound}`);
      
      if (editItemFound) {
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check if edit mode is active
        const editModeActive = await page.evaluate(() => {
          const pageText = document.body.textContent;
          return pageText.includes('Edit Mode') || pageText.includes('editMode');
        });
        
        console.log(`   - Edit mode appears active: ${editModeActive}`);
        
        // Check for runtime errors
        const hasErrors = await page.evaluate(() => {
          const errorElements = document.querySelectorAll('[data-nextjs-dialog-overlay]');
          const errorText = document.body.textContent;
          return errorElements.length > 0 || errorText.includes('Maximum update depth exceeded');
        });
        
        if (hasErrors) {
          console.log('❌ Runtime error still detected');
          await page.screenshot({ path: 'screenshots/edit-dropdown-error.png', fullPage: true });
        } else {
          console.log('✅ SUCCESS: Edit mode activated without errors!');
          await page.screenshot({ path: 'screenshots/edit-dropdown-success.png', fullPage: true });
        }
      }
    }
    
    console.log('📸 Test completed\n');

  } catch (error) {
    console.error('❌ Error during dropdown test:', error.message);
    await page.screenshot({ path: 'screenshots/edit-dropdown-test-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

// Run the test
testEditDropdown().catch(console.error);