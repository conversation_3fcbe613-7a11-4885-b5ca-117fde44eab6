#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';

const SCREENSHOTS_DIR = './screenshots';

// Ensure screenshots directory exists
if (!fs.existsSync(SCREENSHOTS_DIR)) {
  fs.mkdirSync(SCREENSHOTS_DIR, { recursive: true });
}

async function validatePropertyAssetsButtons() {
  console.log('🚀 Starting Property Assets UI Button Validation...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Wait for dev server to be ready
    console.log('⏳ Waiting for dev server...');
    await page.goto('http://localhost:3000', { waitUntil: 'networkidle0', timeout: 10000 });
    
    const pages = [
      { name: 'Properties', url: '/property-assets/properties', buttonText: 'Add Property' },
      { name: 'Units', url: '/property-assets/units', buttonText: 'Add Unit' },
      { name: 'Contracts', url: '/property-assets/contracts', buttonText: 'Add Contract' },
      { name: 'Tenants', url: '/property-assets/tenants', buttonText: 'Add Tenant' },
      { name: 'Maintenance', url: '/property-assets/maintenance', buttonText: 'Add Maintenance' }
    ];
    
    for (const pageInfo of pages) {
      console.log(`\n📋 Testing ${pageInfo.name} page...`);
      
      try {
        // Navigate to the page
        await page.goto(`http://localhost:3000${pageInfo.url}`, { 
          waitUntil: 'networkidle0', 
          timeout: 10000 
        });
        
        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Take screenshot of the page
        const pageScreenshot = path.join(SCREENSHOTS_DIR, `${pageInfo.name.toLowerCase()}-page.png`);
        await page.screenshot({ 
          path: pageScreenshot, 
          fullPage: true 
        });
        console.log(`📸 Page screenshot saved: ${pageScreenshot}`);
        
        // Look for the add button (could be dropdown or regular button)
        const buttonExists = await page.evaluate((buttonText) => {
          // Look for button with specific text
          const buttons = Array.from(document.querySelectorAll('button'));
          return buttons.some(btn => 
            btn.textContent?.includes('Add') || 
            btn.textContent?.includes('Property') ||
            btn.textContent?.includes('Unit') ||
            btn.textContent?.includes('Contract') ||
            btn.textContent?.includes('Tenant') ||
            btn.textContent?.includes('Maintenance')
          );
        }, pageInfo.buttonText);
        
        if (buttonExists) {
          console.log(`✅ Add button found on ${pageInfo.name} page`);
          
          // Try to click the button
          try {
            // Look for any button containing "Add" text
            const addButton = await page.$('button');
            if (addButton) {
              await addButton.click();
              console.log(`✅ Add button clicked successfully on ${pageInfo.name} page`);
            } else {
              console.log(`⚠️  No clickable button found on ${pageInfo.name} page`);
            }
            
            // Wait for any dropdown or navigation
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            // Take screenshot after clicking
            const clickScreenshot = path.join(SCREENSHOTS_DIR, `${pageInfo.name.toLowerCase()}-button-clicked.png`);
            await page.screenshot({ 
              path: clickScreenshot, 
              fullPage: true 
            });
            console.log(`📸 After-click screenshot saved: ${clickScreenshot}`);
            
          } catch (clickError) {
            console.log(`⚠️  Could not click button on ${pageInfo.name} page: ${clickError.message}`);
          }
        } else {
          console.log(`❌ Add button not found on ${pageInfo.name} page`);
        }
        
      } catch (error) {
        console.log(`❌ Error testing ${pageInfo.name} page: ${error.message}`);
        
        // Take error screenshot
        const errorScreenshot = path.join(SCREENSHOTS_DIR, `${pageInfo.name.toLowerCase()}-error.png`);
        await page.screenshot({ 
          path: errorScreenshot, 
          fullPage: true 
        });
        console.log(`📸 Error screenshot saved: ${errorScreenshot}`);
      }
    }
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
  } finally {
    await browser.close();
    console.log('\n✨ Validation completed. Check screenshots directory for results.');
  }
}

// Run validation
validatePropertyAssetsButtons().catch(console.error);