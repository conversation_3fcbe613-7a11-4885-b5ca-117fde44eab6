#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { navigateWithAuth, AUTH_CONFIG } from './utils/auth.mjs';

const SCREENSHOTS_DIR = './screenshots';

// Ensure screenshots directory exists
if (!fs.existsSync(SCREENSHOTS_DIR)) {
  fs.mkdirSync(SCREENSHOTS_DIR, { recursive: true });
}

async function verifyContractFormTranslations() {
  console.log('🌍 Contract Form Translation & Visual Verification\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  const issues = [];
  
  try {
    // Navigate to form
    const formUrl = `${AUTH_CONFIG.BASE_URL}/property-assets/contracts/new`;
    const navSuccess = await navigateWithAuth(page, formUrl);
    
    if (!navSuccess) {
      throw new Error('Failed to navigate to contract form');
    }
    
    // Wait for form to load
    await page.waitForSelector('form', { timeout: 10000 });
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('✅ Successfully navigated to contract form');
    
    // Step 1: Check for untranslated text
    console.log('\n🔍 STEP 1: Translation Analysis');
    
    const translationAnalysis = await page.evaluate(() => {
      const allText = [];
      const untranslatedKeys = [];
      const missingTranslations = [];
      
      // Find all text content
      const textElements = document.querySelectorAll('*');
      textElements.forEach(element => {
        const text = element.textContent?.trim();
        if (text && text.length > 0 && element.children.length === 0) {
          allText.push({
            text: text,
            tag: element.tagName.toLowerCase(),
            classList: Array.from(element.classList),
            isVisible: element.offsetParent !== null,
            isTranslationKey: text.includes('.') && !text.includes(' ')
          });
        }
      });
      
      // Check for translation keys that might not be translated
      allText.forEach(item => {
        if (item.isTranslationKey) {
          untranslatedKeys.push(item.text);
        }
        
        // Check for common missing translation patterns
        if (item.text.includes('placeholder') || 
            item.text.includes('validation.') || 
            item.text.includes('contracts.') ||
            item.text.includes('common.')) {
          missingTranslations.push(item.text);
        }
      });
      
      return {
        totalTextElements: allText.length,
        visibleText: allText.filter(item => item.isVisible),
        untranslatedKeys: [...new Set(untranslatedKeys)],
        missingTranslations: [...new Set(missingTranslations)],
        allUniqueText: [...new Set(allText.map(item => item.text))].slice(0, 20) // Sample
      };
    });
    
    console.log(`   📝 Total text elements: ${translationAnalysis.totalTextElements}`);
    console.log(`   👁️ Visible text elements: ${translationAnalysis.visibleText.length}`);
    console.log(`   🔤 Untranslated keys found: ${translationAnalysis.untranslatedKeys.length}`);
    console.log(`   🚨 Missing translations: ${translationAnalysis.missingTranslations.length}`);
    
    if (translationAnalysis.untranslatedKeys.length > 0) {
      console.log('\\n   ⚠️ UNTRANSLATED KEYS:');
      translationAnalysis.untranslatedKeys.forEach((key, index) => {
        console.log(`      ${index + 1}. "${key}"`);
        issues.push(`Untranslated key: "${key}"`);
      });
    }
    
    if (translationAnalysis.missingTranslations.length > 0) {
      console.log('\\n   🚨 MISSING TRANSLATIONS:');
      translationAnalysis.missingTranslations.forEach((text, index) => {
        console.log(`      ${index + 1}. "${text}"`);
        issues.push(`Missing translation: "${text}"`);
      });
    }
    
    // Step 2: Visual Element Positioning Check
    console.log('\\n📐 STEP 2: Visual Positioning Analysis');
    
    const positionAnalysis = await page.evaluate(() => {
      const formElements = document.querySelectorAll('input, select, textarea, button, label');
      const dislocatedElements = [];
      const overlappingElements = [];
      
      const elements = Array.from(formElements).map(el => {
        const rect = el.getBoundingClientRect();
        const styles = window.getComputedStyle(el);
        
        return {
          tag: el.tagName.toLowerCase(),
          text: el.textContent?.trim().substring(0, 30) || el.placeholder || el.value || 'no-text',
          position: {
            x: rect.x,
            y: rect.y,
            width: rect.width,
            height: rect.height
          },
          styles: {
            position: styles.position,
            display: styles.display,
            visibility: styles.visibility,
            zIndex: styles.zIndex
          },
          isVisible: el.offsetParent !== null,
          isDislocated: rect.x < 0 || rect.y < 0 || rect.width <= 0 || rect.height <= 0
        };
      });
      
      // Check for dislocated elements
      elements.forEach(el => {
        if (el.isDislocated && el.isVisible) {
          dislocatedElements.push(el);
        }
      });
      
      // Check for overlapping elements (simplified)
      for (let i = 0; i < elements.length; i++) {
        for (let j = i + 1; j < elements.length; j++) {
          const el1 = elements[i];
          const el2 = elements[j];
          
          if (el1.isVisible && el2.isVisible &&
              Math.abs(el1.position.x - el2.position.x) < 10 &&
              Math.abs(el1.position.y - el2.position.y) < 10) {
            overlappingElements.push({ el1: el1.text, el2: el2.text });
          }
        }
      }
      
      return {
        totalElements: elements.length,
        visibleElements: elements.filter(el => el.isVisible).length,
        dislocatedElements,
        overlappingElements: overlappingElements.slice(0, 5), // Limit results
        samplePositions: elements.filter(el => el.isVisible).slice(0, 5)
      };
    });
    
    console.log(`   📊 Total form elements: ${positionAnalysis.totalElements}`);
    console.log(`   👁️ Visible elements: ${positionAnalysis.visibleElements}`);
    console.log(`   📐 Dislocated elements: ${positionAnalysis.dislocatedElements.length}`);
    console.log(`   ⚠️ Overlapping elements: ${positionAnalysis.overlappingElements.length}`);
    
    if (positionAnalysis.dislocatedElements.length > 0) {
      console.log('\\n   🚨 DISLOCATED ELEMENTS:');
      positionAnalysis.dislocatedElements.forEach((el, index) => {
        console.log(`      ${index + 1}. ${el.tag}: "${el.text}" at (${el.position.x}, ${el.position.y})`);
        issues.push(`Dislocated element: ${el.tag} "${el.text}"`);
      });
    }
    
    if (positionAnalysis.overlappingElements.length > 0) {
      console.log('\\n   ⚠️ OVERLAPPING ELEMENTS:');
      positionAnalysis.overlappingElements.forEach((pair, index) => {
        console.log(`      ${index + 1}. "${pair.el1}" overlaps with "${pair.el2}"`);
        issues.push(`Overlapping elements: "${pair.el1}" and "${pair.el2}"`);
      });
    }
    
    // Step 3: Form Field Validation
    console.log('\\n📋 STEP 3: Form Field Validation');
    
    const fieldValidation = await page.evaluate(() => {
      const inputs = Array.from(document.querySelectorAll('input, select, textarea'));
      const labels = Array.from(document.querySelectorAll('label'));
      const fieldIssues = [];
      
      const fieldAnalysis = inputs.map(input => {
        const id = input.id || input.name || 'no-id';
        const label = labels.find(l => l.getAttribute('for') === id || 
                                     l.textContent?.includes(input.placeholder));
        
        return {
          id,
          type: input.type || input.tagName.toLowerCase(),
          hasLabel: !!label,
          labelText: label?.textContent?.trim() || '',
          placeholder: input.placeholder || '',
          required: input.required,
          visible: input.offsetParent !== null,
          hasValue: !!(input.value || input.textContent?.trim())
        };
      });
      
      // Check for fields without labels
      fieldAnalysis.forEach(field => {
        if (field.visible && !field.hasLabel && !field.placeholder) {
          fieldIssues.push(`Field "${field.id}" has no label or placeholder`);
        }
        
        if (field.required && field.visible && !field.hasValue) {
          // This is normal for empty form, not an issue
        }
      });
      
      return {
        totalFields: fieldAnalysis.length,
        fieldsWithLabels: fieldAnalysis.filter(f => f.hasLabel).length,
        fieldsWithPlaceholders: fieldAnalysis.filter(f => f.placeholder).length,
        requiredFields: fieldAnalysis.filter(f => f.required).length,
        fieldIssues,
        sampleFields: fieldAnalysis.slice(0, 8)
      };
    });
    
    console.log(`   📝 Total form fields: ${fieldValidation.totalFields}`);
    console.log(`   🏷️ Fields with labels: ${fieldValidation.fieldsWithLabels}`);
    console.log(`   📝 Fields with placeholders: ${fieldValidation.fieldsWithPlaceholders}`);
    console.log(`   ⚠️ Required fields: ${fieldValidation.requiredFields}`);
    console.log(`   🚨 Field issues: ${fieldValidation.fieldIssues.length}`);
    
    if (fieldValidation.fieldIssues.length > 0) {
      console.log('\\n   🚨 FIELD ISSUES:');
      fieldValidation.fieldIssues.forEach((issue, index) => {
        console.log(`      ${index + 1}. ${issue}`);
        issues.push(issue);
      });
    }
    
    // Step 4: Screenshots for visual verification
    console.log('\\n📸 STEP 4: Visual Screenshots');
    
    // Initial screenshot
    const initialScreenshot = path.join(SCREENSHOTS_DIR, 'contract-form-translation-check.png');
    await page.screenshot({ 
      path: initialScreenshot, 
      fullPage: true 
    });
    console.log(`   📸 Full form screenshot: ${initialScreenshot}`);
    
    // Test form in different states
    try {
      // Click on first input to show focus state
      const firstInput = await page.$('input:not([type="hidden"])');
      if (firstInput) {
        await firstInput.click();
        
        const focusScreenshot = path.join(SCREENSHOTS_DIR, 'contract-form-focus-state.png');
        await page.screenshot({ 
          path: focusScreenshot, 
          fullPage: true 
        });
        console.log(`   📸 Focus state screenshot: ${focusScreenshot}`);
      }
      
      // Try to trigger validation
      const submitButton = await page.$('button[type="submit"], button:last-of-type');
      if (submitButton) {
        await submitButton.click();
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        const validationScreenshot = path.join(SCREENSHOTS_DIR, 'contract-form-validation-state.png');
        await page.screenshot({ 
          path: validationScreenshot, 
          fullPage: true 
        });
        console.log(`   📸 Validation state screenshot: ${validationScreenshot}`);
      }
      
    } catch (screenshotError) {
      console.log(`   ⚠️ Screenshot interaction failed: ${screenshotError.message}`);
    }
    
    // Final summary
    console.log('\\n📊 VERIFICATION SUMMARY');
    console.log('========================');
    
    if (issues.length === 0) {
      console.log('🎉 NO ISSUES FOUND!');
      console.log('✅ All translations are properly displayed');
      console.log('✅ All elements are correctly positioned');
      console.log('✅ All form fields have proper labels/placeholders');
      console.log('✅ Form is ready for production');
    } else {
      console.log(`🚨 ${issues.length} ISSUES FOUND:`);
      issues.forEach((issue, index) => {
        console.log(`   ${index + 1}. ${issue}`);
      });
    }
    
    console.log('\\n📈 VERIFICATION METRICS:');
    console.log(`   🌍 Translation Coverage: ${translationAnalysis.untranslatedKeys.length === 0 ? '100%' : '< 100%'}`);
    console.log(`   📐 Element Positioning: ${positionAnalysis.dislocatedElements.length === 0 ? 'Perfect' : 'Needs Fix'}`);
    console.log(`   📋 Field Labeling: ${fieldValidation.fieldIssues.length === 0 ? 'Complete' : 'Needs Review'}`);
    
    return {
      issues,
      score: Math.max(0, 100 - (issues.length * 10)), // Deduct 10 points per issue
      translationAnalysis,
      positionAnalysis,
      fieldValidation
    };
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    
    // Error screenshot
    await page.screenshot({ 
      path: path.join(SCREENSHOTS_DIR, 'contract-form-verification-error.png'), 
      fullPage: true 
    });
    
    return { issues: [error.message], score: 0 };
  } finally {
    await browser.close();
    console.log('\\n✨ Translation & visual verification completed');
  }
}

// Run verification
verifyContractFormTranslations().catch(console.error);