import { Suspense } from "react";
import { Metadata } from "next";

import { MaintenanceDetailsComponent } from "@/features/property-assets/components/MaintenanceDetails";

export const metadata: Metadata = {
  title: "Maintenance Request Details | OneX ERP",
  description: "View detailed maintenance request information, status, and work history",
};

interface MaintenanceRequestDetailPageProps {
  params: {
    id: string;
  };
}

export default function MaintenanceRequestDetailPage({
  params,
}: MaintenanceRequestDetailPageProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <MaintenanceDetailsComponent maintenanceId={params.id} />
    </Suspense>
  );
}
