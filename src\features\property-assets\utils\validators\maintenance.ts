import { z } from "zod";

export const createMaintenanceRequestSchema = z.object({
  property_id: z.string().min(1, "validation.propertyRequired"),
  unit_id: z.string().optional(),
  tenant_id: z.string().optional(),
  title: z.string().min(1, "validation.titleRequired"),
  description: z.string().min(1, "validation.descriptionRequired"),
  priority: z.enum(["low", "medium", "high", "urgent"], {
    errorMap: () => ({ message: "validation.priorityRequired" }),
  }),
  category: z.enum(
    ["plumbing", "electrical", "hvac", "appliance", "structural", "cosmetic", "other"],
    {
      errorMap: () => ({ message: "validation.categoryRequired" }),
    }
  ),
  scheduled_date: z.string().optional(),
  estimated_cost: z.number().positive("validation.estimatedCostMustBePositive").optional(),
  contractor_name: z.string().optional(),
  contractor_phone: z.string().optional(),
  notes: z.string().optional(),
  images: z
    .array(
      z.object({
        name: z.string(),
        image: z.string(),
      })
    )
    .optional(),
});

export const updateMaintenanceRequestSchema = createMaintenanceRequestSchema.partial().extend({
  id: z.string().min(1, "validation.idRequired"),
  status: z.enum(["open", "in_progress", "completed", "cancelled"]).optional(),
  completed_date: z.string().optional(),
  actual_cost: z.number().positive("validation.actualCostMustBePositive").optional(),
});

export const maintenanceFilterSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  query: z.string().optional(),
  status: z.enum(["open", "in_progress", "completed", "cancelled"]).optional(),
  priority: z.enum(["low", "medium", "high", "urgent"]).optional(),
  category: z
    .enum(["plumbing", "electrical", "hvac", "appliance", "structural", "cosmetic", "other"])
    .optional(),
  property_id: z.string().optional(),
  unit_id: z.string().optional(),
});

export type CreateMaintenanceRequestFormValues = z.infer<typeof createMaintenanceRequestSchema>;
export type UpdateMaintenanceRequestFormValues = z.infer<typeof updateMaintenanceRequestSchema>;
export type MaintenanceFilterValues = z.infer<typeof maintenanceFilterSchema>;
