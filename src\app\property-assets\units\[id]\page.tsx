import { Metadata } from "next";

import { UnitDetailsComponent } from "@/features/property-assets/components/UnitDetails";

export const metadata: Metadata = {
  title: "Unit Details | OneX ERP",
  description: "View detailed unit information, specifications, contracts, and occupancy history",
};

interface UnitDetailPageProps {
  params: {
    id: string;
  };
}

export default function UnitDetailPage({ params }: UnitDetailPageProps) {
  return (
    <div className="container mx-auto px-4 py-6 lg:px-6">
      <UnitDetailsComponent unitId={params.id} />
    </div>
  );
}
