"use client";

import { useState } from "react";
import {
  Activity,
  AlertTriangle,
  ArrowUpDown,
  BarChart3,
  Calculator,
  Calendar,
  CheckCircle,
  Clock,
  DollarSign,
  Home,
  Percent,
  PieChart,
  Target,
  TrendingDown,
  TrendingUp,
  Users,
} from "lucide-react";
import { useTranslation } from "react-i18next";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { useFinancialSummary } from "../../hooks/useFinancial";

interface FinancialMetricsCardsProps {
  propertyId?: string;
  className?: string;
  compact?: boolean;
}

export function FinancialMetricsCards({
  propertyId,
  className,
  compact = false,
}: FinancialMetricsCardsProps) {
  const { t } = useTranslation();
  const [timeRange, setTimeRange] = useState<"month" | "quarter" | "year">("month");
  const [selectedProperty, setSelectedProperty] = useState<string>(propertyId || "all");

  const { data: financialSummary, isLoading } = useFinancialSummary({
    property_id: selectedProperty === "all" ? undefined : selectedProperty,
  });

  // Enhanced financial metrics with mock data
  const financialMetrics = [
    {
      id: "total_revenue",
      title: "Total Revenue",
      value: financialSummary?.monthly_rental_income || 285000,
      format: "currency",
      trend: "up",
      trendValue: 12.5,
      icon: DollarSign,
      color: "blue",
      target: 300000,
      description: "Monthly rental income and fees",
      category: "revenue",
    },
    {
      id: "net_income",
      title: "Net Income",
      value: financialSummary?.net_monthly_income || 195000,
      format: "currency",
      trend: "up",
      trendValue: 8.3,
      icon: TrendingUp,
      color: "green",
      target: 200000,
      description: "Revenue minus all expenses",
      category: "profit",
    },
    {
      id: "occupancy_rate",
      title: "Occupancy Rate",
      value: (1 - (financialSummary?.vacancy_rate || 0.07)) * 100,
      format: "percentage",
      trend: "up",
      trendValue: 2.1,
      icon: Home,
      color: "purple",
      target: 95,
      description: "Percentage of occupied units",
      category: "operations",
    },
    {
      id: "profit_margin",
      title: "Profit Margin",
      value: 68.4,
      format: "percentage",
      trend: "up",
      trendValue: 1.8,
      icon: PieChart,
      color: "orange",
      target: 70,
      description: "Net income as % of revenue",
      category: "profit",
    },
    {
      id: "avg_rent_unit",
      title: "Avg Rent/Unit",
      value: 1285,
      format: "currency",
      trend: "up",
      trendValue: 4.2,
      icon: Calculator,
      color: "indigo",
      target: 1350,
      description: "Average monthly rent per unit",
      category: "revenue",
    },
    {
      id: "collection_rate",
      title: "Collection Rate",
      value: 98.2,
      format: "percentage",
      trend: "up",
      trendValue: 0.5,
      icon: CheckCircle,
      color: "green",
      target: 99,
      description: "Percentage of rent collected on time",
      category: "operations",
    },
    {
      id: "total_expenses",
      title: "Total Expenses",
      value: financialSummary?.monthly_expenses || 90000,
      format: "currency",
      trend: "up",
      trendValue: 3.1,
      icon: ArrowUpDown,
      color: "red",
      target: 85000,
      description: "Monthly operating expenses",
      category: "expenses",
    },
    {
      id: "maintenance_costs",
      title: "Maintenance Costs",
      value: 15400,
      format: "currency",
      trend: "up",
      trendValue: 10.2,
      icon: Activity,
      color: "yellow",
      target: 14000,
      description: "Monthly maintenance and repairs",
      category: "expenses",
    },
    {
      id: "vacancy_rate",
      title: "Vacancy Rate",
      value: (financialSummary?.vacancy_rate || 0.07) * 100,
      format: "percentage",
      trend: "down",
      trendValue: -1.2,
      icon: AlertTriangle,
      color: "red",
      target: 5,
      description: "Percentage of vacant units",
      category: "operations",
      inverted: true, // Lower is better
    },
    {
      id: "roi",
      title: "ROI",
      value: 9.2,
      format: "percentage",
      trend: "up",
      trendValue: 0.8,
      icon: Target,
      color: "purple",
      target: 10,
      description: "Return on investment",
      category: "profit",
    },
    {
      id: "cash_flow",
      title: "Cash Flow",
      value: 185000,
      format: "currency",
      trend: "up",
      trendValue: 6.8,
      icon: BarChart3,
      color: "blue",
      target: 190000,
      description: "Monthly net cash flow",
      category: "profit",
    },
    {
      id: "days_to_lease",
      title: "Avg Days to Lease",
      value: 18,
      format: "number",
      trend: "down",
      trendValue: -12.5,
      icon: Clock,
      color: "green",
      target: 15,
      description: "Average days to find new tenant",
      category: "operations",
      inverted: true,
    },
  ];

  const formatValue = (value: number, format: string) => {
    switch (format) {
      case "currency":
        return `$${value.toLocaleString()}`;
      case "percentage":
        return `${value.toFixed(1)}%`;
      case "number":
        return value.toString();
      default:
        return value.toString();
    }
  };

  const getColorClasses = (color: string) => {
    const colorMap = {
      blue: { bg: "bg-primary/10", text: "text-primary", icon: "text-primary" },
      green: { bg: "bg-success/10", text: "text-success", icon: "text-success" },
      purple: {
        bg: "bg-secondary/10",
        text: "text-secondary-foreground",
        icon: "text-secondary-foreground",
      },
      orange: { bg: "bg-warning/10", text: "text-warning", icon: "text-warning" },
      red: { bg: "bg-destructive/10", text: "text-destructive", icon: "text-destructive" },
      yellow: { bg: "bg-warning/10", text: "text-warning", icon: "text-warning" },
      indigo: { bg: "bg-primary/10", text: "text-primary", icon: "text-primary" },
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  const getTrendIcon = (trend: string, inverted = false) => {
    const isPositive = inverted ? trend === "down" : trend === "up";
    return isPositive ? (
      <TrendingUp className="size-3 text-success" />
    ) : (
      <TrendingDown className="size-3 text-destructive" />
    );
  };

  const getTrendColor = (trend: string, inverted = false) => {
    const isPositive = inverted ? trend === "down" : trend === "up";
    return isPositive ? "text-success" : "text-destructive";
  };

  const getProgressValue = (value: number, target: number, inverted = false) => {
    if (inverted) {
      return Math.max(0, Math.min(100, (target / value) * 100));
    }
    return Math.max(0, Math.min(100, (value / target) * 100));
  };

  const getPerformanceStatus = (value: number, target: number, inverted = false) => {
    const percentage = getProgressValue(value, target, inverted);
    if (percentage >= 90) return { status: "excellent", color: "text-success" };
    if (percentage >= 75) return { status: "good", color: "text-primary" };
    if (percentage >= 60) return { status: "average", color: "text-warning" };
    return { status: "poor", color: "text-destructive" };
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {Array.from({ length: compact ? 6 : 12 }).map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-4">
              <div className="h-20 rounded bg-muted"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  const displayMetrics = compact ? financialMetrics.slice(0, 6) : financialMetrics;

  return (
    <div className={`space-y-6 ${className}`}>
      {!compact && (
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h3 className="text-lg font-semibold text-foreground">Key Financial Metrics</h3>
            <p className="text-sm text-muted-foreground">
              Real-time performance indicators for your properties
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Select value={timeRange} onValueChange={(value: any) => setTimeRange(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">This Quarter</SelectItem>
                <SelectItem value="year">This Year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      )}

      <div
        className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 ${compact ? "xl:grid-cols-3" : "xl:grid-cols-4"} gap-4`}>
        {displayMetrics.map((metric) => {
          const colors = getColorClasses(metric.color);
          const performance = getPerformanceStatus(metric.value, metric.target, metric.inverted);
          const progressValue = getProgressValue(metric.value, metric.target, metric.inverted);

          return (
            <TooltipProvider key={metric.id}>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Card className="cursor-pointer transition-shadow hover:shadow-md">
                    <CardContent className={compact ? "p-4" : "p-6"}>
                      <div className="flex items-center justify-between">
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center justify-between">
                            <p className="text-sm font-medium text-muted-foreground">
                              {metric.title}
                            </p>
                            <div className={`p-2 ${colors.bg} rounded-full`}>
                              <metric.icon className={`h-4 w-4 ${colors.icon}`} />
                            </div>
                          </div>

                          <div className="space-y-1">
                            <p className="text-2xl font-bold text-foreground">
                              {formatValue(metric.value, metric.format)}
                            </p>

                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-1">
                                {getTrendIcon(metric.trend, metric.inverted)}
                                <span
                                  className={`text-xs ${getTrendColor(metric.trend, metric.inverted)}`}>
                                  {metric.trend === "up" ? "+" : metric.trend === "down" ? "-" : ""}
                                  {Math.abs(metric.trendValue).toFixed(1)}%
                                </span>
                              </div>

                              <Badge variant="outline" className={`text-xs ${performance.color}`}>
                                {performance.status}
                              </Badge>
                            </div>
                          </div>

                          {!compact && (
                            <div className="space-y-1">
                              <div className="flex items-center justify-between text-xs">
                                <span className="text-muted-foreground">Target:</span>
                                <span className="font-medium">
                                  {formatValue(metric.target, metric.format)}
                                </span>
                              </div>
                              <Progress value={progressValue} className="h-1" />
                            </div>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TooltipTrigger>
                <TooltipContent side="bottom" className="max-w-xs">
                  <div className="space-y-1">
                    <p className="font-medium">{metric.title}</p>
                    <p className="text-xs">{metric.description}</p>
                    <div className="text-xs text-muted-foreground">
                      Target: {formatValue(metric.target, metric.format)} • Category:{" "}
                      {metric.category}
                    </div>
                  </div>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          );
        })}
      </div>

      {!compact && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          {/* Performance Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Performance Summary</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {["excellent", "good", "average", "poor"].map((status) => {
                const count = financialMetrics.filter(
                  (metric) =>
                    getPerformanceStatus(metric.value, metric.target, metric.inverted).status ===
                    status
                ).length;

                const colors = {
                  excellent: "text-success bg-success/10",
                  good: "text-primary bg-primary/10",
                  average: "text-warning bg-warning/10",
                  poor: "text-destructive bg-destructive/10",
                };

                return (
                  <div key={status} className="flex items-center justify-between">
                    <span className="text-sm capitalize">{status}</span>
                    <Badge className={colors[status as keyof typeof colors]}>{count}</Badge>
                  </div>
                );
              })}
            </CardContent>
          </Card>

          {/* Trend Summary */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Trend Analysis</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Improving</span>
                <Badge className="bg-success/10 text-success">
                  {
                    financialMetrics.filter(
                      (m) => (m.trend === "up" && !m.inverted) || (m.trend === "down" && m.inverted)
                    ).length
                  }
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Declining</span>
                <Badge className="bg-destructive/10 text-destructive">
                  {
                    financialMetrics.filter(
                      (m) => (m.trend === "down" && !m.inverted) || (m.trend === "up" && m.inverted)
                    ).length
                  }
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Stable</span>
                <Badge className="bg-muted/50 text-muted-foreground">
                  {financialMetrics.filter((m) => m.trend === "flat").length}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Category Performance */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-base">Category Performance</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {["revenue", "profit", "operations", "expenses"].map((category) => {
                const categoryMetrics = financialMetrics.filter((m) => m.category === category);
                const avgPerformance =
                  categoryMetrics.reduce((acc, metric) => {
                    return acc + getProgressValue(metric.value, metric.target, metric.inverted);
                  }, 0) / categoryMetrics.length;

                return (
                  <div key={category} className="space-y-1">
                    <div className="flex items-center justify-between">
                      <span className="text-sm capitalize">{category}</span>
                      <span className="text-sm font-medium">{avgPerformance.toFixed(0)}%</span>
                    </div>
                    <Progress value={avgPerformance} className="h-1" />
                  </div>
                );
              })}
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
