// Plan types
export interface Plan {
  id?: string;
  name: string;
  description?: string;
  price?: number;
  salePrice?: number;

  duration?: number;
  features?: string[];
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreatePlanRequest {
  name: string;
  description?: string;
  price?: number;
  salePrice?: number;
  duration?: number;
  features?: string[];
  is_active?: boolean;
}

export type UpdatePlanRequest = Partial<CreatePlanRequest>;

// Service types
export interface Service {
  id?: string;
  name: string;
  description?: string;
  price?: number;
  duration?: number;
  category?: string;
  is_active?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreateServiceRequest {
  name: string;
  description?: string;
  price?: number;
  duration?: number;
  category?: string;
  is_active?: boolean;
}

export type UpdateServiceRequest = Partial<CreateServiceRequest>;
