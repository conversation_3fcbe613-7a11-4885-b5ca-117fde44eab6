import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

import { maintenanceApi } from "@/lib/apis/property-assets";

import type { CreateMaintenanceRequest, MaintenanceRequest } from "../types";
import { maintenanceKeys, type IGetMaintenanceParams } from "./keys";

// List Maintenance Requests Hook
export const useMaintenanceRequests = (params?: IGetMaintenanceParams) => {
  return useQuery({
    queryKey: maintenanceKeys.list(params || {}),
    queryFn: () => maintenanceApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Maintenance Request by ID Hook
export const useMaintenanceRequest = (id: string) => {
  return useQuery({
    queryKey: maintenanceKeys.detail(id),
    queryFn: () => maintenanceApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create Maintenance Request Hook
export const useCreateMaintenanceRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateMaintenanceRequest) => maintenanceApi.create(data),
    onSuccess: (data) => {
      // Invalidate and refetch maintenance requests list
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() });

      // Add the new maintenance request to the cache
      queryClient.setQueryData(maintenanceKeys.detail(data.data.data.id), data);

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};

// Update Maintenance Request Hook
export const useUpdateMaintenanceRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateMaintenanceRequest> }) =>
      maintenanceApi.update(id, data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch maintenance requests list
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() });

      // Update the specific maintenance request in cache
      queryClient.setQueryData(maintenanceKeys.detail(variables.id), data);

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};

// Delete Maintenance Request Hook
export const useDeleteMaintenanceRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => maintenanceApi.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: maintenanceKeys.detail(id) });

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: maintenanceKeys.lists() });

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};
