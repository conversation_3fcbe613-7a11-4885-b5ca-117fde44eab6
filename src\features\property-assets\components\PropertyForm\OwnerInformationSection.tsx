"use client";

import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface OwnerInformationSectionProps {
  form: any;
}

export function OwnerInformationSection({ form }: OwnerInformationSectionProps) {
  const { t } = useTranslation();

  return (
    <Card className="border-border shadow-sm">
      <CardHeader className="border-b border-border bg-background py-3">
        <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
          <div className="size-2 rounded-full bg-secondary"></div>
          {t("pages.properties.ownerInformation")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 bg-card p-4">
        {/* Owner Name */}
        <FormField
          control={form.control}
          name="owner_name"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm font-medium text-foreground">
                {t("pages.properties.owner.name")} <span className="text-destructive">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder={t("pages.properties.placeholders.ownerName")}
                  className="h-9 border-input focus:border-primary focus:ring-ring"
                  {...field}
                />
              </FormControl>
              <FormMessage className="text-destructive" />
            </FormItem>
          )}
        />

        {/* Owner Email and Phone Row */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="owner_email"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.owner.email")} <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    type="email"
                    placeholder={t("pages.properties.placeholders.ownerEmail")}
                    className="h-9 border-input focus:border-primary focus:ring-ring"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="owner_phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.owner.phone")} <span className="text-destructive">*</span>
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder={t("pages.properties.placeholders.ownerPhone")}
                    className="h-9 border-input focus:border-primary focus:ring-ring"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
}
