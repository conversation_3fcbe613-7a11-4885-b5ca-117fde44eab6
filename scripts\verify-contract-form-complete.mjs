#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { navigateWithAuth, AUTH_CONFIG } from './utils/auth.mjs';

const SCREENSHOTS_DIR = './screenshots';

// Ensure screenshots directory exists
if (!fs.existsSync(SCREENSHOTS_DIR)) {
  fs.mkdirSync(SCREENSHOTS_DIR, { recursive: true });
}

async function verifyContractFormComplete() {
  console.log('🔍 Complete Contract Form Verification at /property-assets/contracts/new\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Step 1: Navigate to form
    console.log('📍 STEP 1: Navigation');
    const formUrl = `${AUTH_CONFIG.BASE_URL}/property-assets/contracts/new`;
    const navSuccess = await navigateWithAuth(page, formUrl);
    
    if (!navSuccess) {
      throw new Error('Failed to navigate to contract form');
    }
    console.log('   ✅ Successfully navigated to contract form');
    
    // Wait for form to fully load
    await page.waitForSelector('form', { timeout: 10000 });
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 2: Field Analysis
    console.log('\n📋 STEP 2: Field Structure Analysis');
    
    const fieldAnalysis = await page.evaluate(() => {
      const analysis = {
        // Form structure
        hasForm: !!document.querySelector('form'),
        formMethod: document.querySelector('form')?.method || 'not specified',
        
        // Input fields
        inputs: Array.from(document.querySelectorAll('input')).map((input, index) => ({
          index: index + 1,
          type: input.type,
          name: input.name || input.id || `input-${index}`,
          placeholder: input.placeholder || '',
          required: input.required,
          value: input.value || '',
          disabled: input.disabled,
          visible: input.offsetParent !== null
        })),
        
        // Select fields  
        selects: Array.from(document.querySelectorAll('select')).map((select, index) => ({
          index: index + 1,
          name: select.name || select.id || `select-${index}`,
          optionsCount: select.options.length,
          selectedValue: select.value || '',
          disabled: select.disabled,
          visible: select.offsetParent !== null,
          options: Array.from(select.options).slice(0, 5).map(opt => opt.text) // First 5 options
        })),
        
        // Textarea fields
        textareas: Array.from(document.querySelectorAll('textarea')).map((textarea, index) => ({
          index: index + 1,
          name: textarea.name || textarea.id || `textarea-${index}`,
          placeholder: textarea.placeholder || '',
          required: textarea.required,
          value: textarea.value || '',
          disabled: textarea.disabled,
          visible: textarea.offsetParent !== null
        })),
        
        // Buttons
        buttons: Array.from(document.querySelectorAll('button')).map((button, index) => ({
          index: index + 1,
          text: button.textContent?.trim() || '',
          type: button.type,
          disabled: button.disabled,
          visible: button.offsetParent !== null,
          className: button.className
        })).filter(btn => btn.text && btn.visible && btn.text.length > 0),
        
        // Labels and sections
        labels: Array.from(document.querySelectorAll('label')).map(label => ({
          text: label.textContent?.trim() || '',
          for: label.getAttribute('for') || ''
        })).filter(label => label.text),
        
        // Form sections/cards
        sections: Array.from(document.querySelectorAll('[class*="card"], .form-section, h2, h3')).map(section => ({
          tag: section.tagName.toLowerCase(),
          text: section.textContent?.trim().substring(0, 100) || '',
          className: section.className
        })).filter(section => section.text && section.text.length > 5),
        
        // Page metadata
        title: document.title,
        url: window.location.href,
        theme: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
      };
      
      return analysis;
    });
    
    // Display field analysis
    console.log(`   📄 Page Title: ${fieldAnalysis.title}`);
    console.log(`   🌓 Theme: ${fieldAnalysis.theme}`);
    console.log(`   📝 Form Present: ${fieldAnalysis.hasForm ? '✅' : '❌'}`);
    console.log(`   📊 Total Elements: ${fieldAnalysis.inputs.length} inputs, ${fieldAnalysis.selects.length} selects, ${fieldAnalysis.textareas.length} textareas, ${fieldAnalysis.buttons.length} buttons`);
    
    console.log('\n   📝 INPUT FIELDS:');
    fieldAnalysis.inputs.forEach(input => {
      const status = input.visible ? '✅' : '❌';
      console.log(`      ${status} ${input.index}. [${input.type}] ${input.name} ${input.required ? '(required)' : ''} ${input.placeholder ? `"${input.placeholder}"` : ''}`);
    });
    
    console.log('\n   📋 SELECT FIELDS:');
    fieldAnalysis.selects.forEach(select => {
      const status = select.visible ? '✅' : '❌';
      console.log(`      ${status} ${select.index}. ${select.name} (${select.optionsCount} options) ${select.options.length > 0 ? `[${select.options.join(', ')}...]` : ''}`);
    });
    
    console.log('\n   📄 TEXTAREA FIELDS:');
    fieldAnalysis.textareas.forEach(textarea => {
      const status = textarea.visible ? '✅' : '❌';
      console.log(`      ${status} ${textarea.index}. ${textarea.name} ${textarea.required ? '(required)' : ''} ${textarea.placeholder ? `"${textarea.placeholder}"` : ''}`);
    });
    
    console.log('\n   🔘 FORM BUTTONS:');
    fieldAnalysis.buttons.forEach(button => {
      const status = button.visible && !button.disabled ? '✅' : button.disabled ? '⚠️' : '❌';
      console.log(`      ${status} ${button.index}. "${button.text}" [${button.type}] ${button.disabled ? '(disabled)' : ''}`);
    });
    
    console.log('\n   🏷️ FORM SECTIONS:');
    fieldAnalysis.sections.forEach((section, index) => {
      console.log(`      📂 ${index + 1}. ${section.text}`);
    });
    
    // Step 3: Visual Validation with Screenshots
    console.log('\n📸 STEP 3: Visual Validation');
    
    // Initial form screenshot
    const initialScreenshot = path.join(SCREENSHOTS_DIR, 'contract-form-verification-initial.png');
    await page.screenshot({ 
      path: initialScreenshot, 
      fullPage: true 
    });
    console.log(`   📸 Initial form screenshot: ${initialScreenshot}`);
    
    // Test form interactions
    console.log('\n🧪 STEP 4: Interactive Testing');
    
    try {
      // Test property selection
      const propertySelect = await page.$('select[name*="property"], select:first-of-type');
      if (propertySelect) {
        await propertySelect.click();
        console.log('   ✅ Property select interactive');
        
        // Screenshot with dropdown open
        const dropdownScreenshot = path.join(SCREENSHOTS_DIR, 'contract-form-verification-dropdown.png');
        await page.screenshot({ 
          path: dropdownScreenshot, 
          fullPage: true 
        });
        console.log(`   📸 Dropdown interaction screenshot: ${dropdownScreenshot}`);
        
        // Close dropdown
        await page.keyboard.press('Escape');
      }
      
      // Test text input
      const textInput = await page.$('input[type="text"], input[type="number"]:first-of-type');
      if (textInput) {
        await textInput.click();
        await textInput.type('123');
        console.log('   ✅ Text input interactive');
      }
      
      // Test date input
      const dateInput = await page.$('input[type="date"]');
      if (dateInput) {
        await dateInput.click();
        await dateInput.type('2024-01-15');
        console.log('   ✅ Date input interactive');
      }
      
      // Final screenshot with interactions
      const interactionScreenshot = path.join(SCREENSHOTS_DIR, 'contract-form-verification-interactions.png');
      await page.screenshot({ 
        path: interactionScreenshot, 
        fullPage: true 
      });
      console.log(`   📸 After interactions screenshot: ${interactionScreenshot}`);
      
    } catch (interactionError) {
      console.log(`   ⚠️ Some interactions failed: ${interactionError.message}`);
    }
    
    // Step 5: Accessibility & UX Validation
    console.log('\n♿ STEP 5: Accessibility & UX Validation');
    
    const accessibilityCheck = await page.evaluate(() => {
      return {
        hasAriaLabels: document.querySelectorAll('[aria-label]').length,
        hasFieldsets: document.querySelectorAll('fieldset').length,
        hasRequiredIndicators: document.querySelectorAll('[required], .required, [aria-required="true"]').length,
        hasErrorStates: document.querySelectorAll('.error, [aria-invalid="true"], .invalid').length,
        focusableElements: document.querySelectorAll('input:not([disabled]), select:not([disabled]), textarea:not([disabled]), button:not([disabled])').length,
        hasFormValidation: !!document.querySelector('form[novalidate="false"], form:not([novalidate])'),
        contrastElements: Array.from(document.querySelectorAll('input, select, textarea, button')).map(el => {
          const styles = window.getComputedStyle(el);
          return {
            backgroundColor: styles.backgroundColor,
            color: styles.color,
            borderColor: styles.borderColor
          };
        }).slice(0, 5) // Sample first 5 elements
      };
    });
    
    console.log(`   🏷️ ARIA Labels: ${accessibilityCheck.hasAriaLabels} elements`);
    console.log(`   📋 Fieldsets: ${accessibilityCheck.hasFieldsets} elements`);
    console.log(`   ⚠️ Required Indicators: ${accessibilityCheck.hasRequiredIndicators} elements`);
    console.log(`   🎯 Focusable Elements: ${accessibilityCheck.focusableElements} elements`);
    console.log(`   ✅ Form Validation Enabled: ${accessibilityCheck.hasFormValidation ? 'Yes' : 'No'}`);
    
    // Step 6: Theme Compatibility Check
    console.log('\n🎨 STEP 6: Theme Compatibility');
    
    const themeCheck = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('input, select, textarea, button, .card, .form-item'));
      
      const colorAnalysis = elements.map(el => {
        const styles = window.getComputedStyle(el);
        return {
          tag: el.tagName.toLowerCase(),
          backgroundColor: styles.backgroundColor,
          color: styles.color,
          borderColor: styles.borderColor,
          hasValidColors: styles.backgroundColor !== 'rgba(0, 0, 0, 0)' || styles.color !== 'rgba(0, 0, 0, 0)'
        };
      }).filter(item => item.hasValidColors);
      
      return {
        totalStyledElements: colorAnalysis.length,
        uniqueBackgrounds: [...new Set(colorAnalysis.map(item => item.backgroundColor))].length,
        uniqueTextColors: [...new Set(colorAnalysis.map(item => item.color))].length,
        sampleColors: colorAnalysis.slice(0, 3)
      };
    });
    
    console.log(`   🎨 Styled Elements: ${themeCheck.totalStyledElements}`);
    console.log(`   🎨 Unique Backgrounds: ${themeCheck.uniqueBackgrounds}`);
    console.log(`   📝 Unique Text Colors: ${themeCheck.uniqueTextColors}`);
    console.log(`   🌙 Dark Mode Compatible: ${fieldAnalysis.theme === 'dark' ? '✅' : '⚠️ (currently in light mode)'}`);
    
    // Step 7: Final Validation Summary
    console.log('\n📊 STEP 7: COMPREHENSIVE VALIDATION REPORT');
    console.log('=====================================');
    
    const validationScore = [
      fieldAnalysis.hasForm, // Has form
      fieldAnalysis.inputs.length >= 5, // Has sufficient inputs
      fieldAnalysis.selects.length >= 3, // Has sufficient selects  
      fieldAnalysis.buttons.length >= 2, // Has submit/cancel buttons
      accessibilityCheck.focusableElements >= 8, // Accessibility
      fieldAnalysis.title.includes('Contract'), // Proper title
      themeCheck.totalStyledElements >= 10 // Proper styling
    ].filter(Boolean).length;
    
    const scorePercentage = Math.round((validationScore / 7) * 100);
    const scoreEmoji = scorePercentage >= 90 ? '🏆' : scorePercentage >= 80 ? '✅' : scorePercentage >= 70 ? '⚠️' : '❌';
    
    console.log(`\n${scoreEmoji} OVERALL VALIDATION SCORE: ${validationScore}/7 (${scorePercentage}%)`);
    console.log('\n📋 DETAILED RESULTS:');
    console.log(`   ✅ Form Structure: ${fieldAnalysis.hasForm ? 'PASS' : 'FAIL'}`);
    console.log(`   ✅ Input Fields: ${fieldAnalysis.inputs.length} (${fieldAnalysis.inputs.length >= 5 ? 'PASS' : 'FAIL'})`);
    console.log(`   ✅ Select Fields: ${fieldAnalysis.selects.length} (${fieldAnalysis.selects.length >= 3 ? 'PASS' : 'FAIL'})`);
    console.log(`   ✅ Form Buttons: ${fieldAnalysis.buttons.length} (${fieldAnalysis.buttons.length >= 2 ? 'PASS' : 'FAIL'})`);
    console.log(`   ✅ Accessibility: ${accessibilityCheck.focusableElements} focusable elements (${accessibilityCheck.focusableElements >= 8 ? 'PASS' : 'FAIL'})`);
    console.log(`   ✅ Page Title: "${fieldAnalysis.title}" (${fieldAnalysis.title.includes('Contract') ? 'PASS' : 'FAIL'})`);
    console.log(`   ✅ Styling: ${themeCheck.totalStyledElements} styled elements (${themeCheck.totalStyledElements >= 10 ? 'PASS' : 'FAIL'})`);
    
    console.log('\n📸 SCREENSHOTS GENERATED:');
    console.log(`   📄 Initial Form: contract-form-verification-initial.png`);
    console.log(`   📋 Dropdown Test: contract-form-verification-dropdown.png`);
    console.log(`   🧪 Interactions: contract-form-verification-interactions.png`);
    
    console.log('\n🎯 RECOMMENDATIONS:');
    if (scorePercentage >= 90) {
      console.log('   🏆 Excellent! Form is fully functional and well-designed.');
    } else if (scorePercentage >= 80) {
      console.log('   ✅ Good! Form is functional with minor improvements possible.');
    } else if (scorePercentage >= 70) {
      console.log('   ⚠️ Acceptable but needs improvements in accessibility or styling.');
    } else {
      console.log('   ❌ Needs significant improvements before production use.');
    }
    
    return {
      score: validationScore,
      percentage: scorePercentage,
      fieldAnalysis,
      accessibilityCheck,
      themeCheck
    };
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
    
    // Error screenshot
    await page.screenshot({ 
      path: path.join(SCREENSHOTS_DIR, 'contract-form-verification-error.png'), 
      fullPage: true 
    });
    console.log('📸 Error screenshot saved');
    
    return null;
  } finally {
    await browser.close();
    console.log('\n✨ Contract form verification completed');
  }
}

// Run verification
verifyContractFormComplete().catch(console.error);