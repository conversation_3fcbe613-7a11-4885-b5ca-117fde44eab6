"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  AlertCircle,
  Building2,
  Calendar,
  DollarSign,
  Edit,
  Home,
  ImageIcon,
  Mail,
  MapPin,
  Phone,
  Plus,
  Trash2,
  TrendingUp,
  User,
  Users,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
// UI Components
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import { authProtectedPaths } from "@/constants/paths";

// Property Assets imports
import { useDeleteProperty, useProperty } from "../../hooks/useProperties";
import { useUnits } from "../../hooks/useUnits";
import type { Property } from "../../types";

interface PropertyDetailsComponentProps {
  propertyId: string;
}

export function PropertyDetailsComponent({ propertyId }: PropertyDetailsComponentProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  // Data fetching
  const {
    data: property,
    isLoading: propertyLoading,
    error: propertyError,
  } = useProperty(propertyId);
  const { data: unitsData, isLoading: unitsLoading } = useUnits(propertyId, { limit: 1000 });
  const units = unitsData?.items || [];

  // Mutations
  const deletePropertyMutation = useDeleteProperty();

  const handleEdit = () => {
    router.push(`${authProtectedPaths.PROPERTIES}/${propertyId}/edit` as any);
  };

  const handleDelete = async () => {
    try {
      await deletePropertyMutation.mutateAsync(propertyId);
      toast.success(t("pages.properties.deleteSuccess"));
      router.push(authProtectedPaths.PROPERTIES as any);
    } catch (error) {
      toast.error(t("pages.properties.deleteError"));
    }
  };

  const handleAddUnit = () => {
    router.push(`${authProtectedPaths.UNITS}/new?property=${propertyId}` as any);
  };

  // Loading state
  if (propertyLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <div className="mx-auto size-8 animate-spin rounded-full border-b-2 border-primary"></div>
          <p className="mt-2 text-sm text-muted-foreground">{t("common.loading")}</p>
        </div>
      </div>
    );
  }

  // Error state
  if (propertyError || !property) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertCircle className="mx-auto mb-4 size-12 text-destructive" />
          <p className="text-sm text-destructive">{t("pages.properties.loadError")}</p>
          <Button
            variant="outline"
            onClick={() => router.push(authProtectedPaths.PROPERTIES as any)}
            className="mt-4">
            {t("common.goBack")}
          </Button>
        </div>
      </div>
    );
  }

  // Calculate stats
  const totalUnits = units.length;
  const occupiedUnits = units.filter((unit) => unit.status === "occupied").length;
  const availableUnits = units.filter((unit) => unit.status === "available").length;
  const maintenanceUnits = units.filter((unit) => unit.status === "maintenance").length;
  const occupancyRate = totalUnits > 0 ? ((occupiedUnits / totalUnits) * 100).toFixed(1) : "0";

  const getPropertyTypeLabel = (type: Property["property_type"]) => {
    const types = {
      residential: t("pages.properties.types.residential"),
      commercial: t("pages.properties.types.commercial"),
      mixed: t("pages.properties.types.mixed"),
    };
    return types[type] || type;
  };

  const getStatusBadge = (status: Property["status"]) => {
    const variants = {
      active: "default" as const,
      inactive: "secondary" as const,
    };

    const labels = {
      active: t("common.status.active"),
      inactive: t("common.status.inactive"),
    };

    return <Badge variant={variants[status]}>{labels[status]}</Badge>;
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-3">
            <h1 className="text-3xl font-bold text-foreground">{property.name}</h1>
            {getStatusBadge(property.status)}
          </div>
          <div className="flex items-center text-muted-foreground">
            <MapPin className="mr-2 size-4" />
            <span className="text-sm">
              {property.address.street}, {property.address.city}, {property.address.state}{" "}
              {property.address.zip_code}
            </span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleEdit}>
            <Edit className="mr-2 size-4" />
            {t("common.edit")}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowDeleteDialog(true)}
            className="text-destructive hover:text-destructive">
            <Trash2 className="mr-2 size-4" />
            {t("common.delete")}
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t("pages.properties.stats.totalUnits")}
                </p>
                <p className="text-2xl font-bold text-foreground">{totalUnits}</p>
              </div>
              <Home className="size-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t("pages.properties.stats.occupancyRate")}
                </p>
                <p className="text-2xl font-bold text-foreground">{occupancyRate}%</p>
              </div>
              <TrendingUp className="size-8 text-success" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t("pages.properties.stats.occupiedUnits")}
                </p>
                <p className="text-2xl font-bold text-foreground">{occupiedUnits}</p>
              </div>
              <Users className="size-8 text-secondary-foreground" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  {t("pages.properties.stats.availableUnits")}
                </p>
                <p className="text-2xl font-bold text-foreground">{availableUnits}</p>
              </div>
              <Home className="size-8 text-warning" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
        {/* Property Information */}
        <div className="space-y-8 lg:col-span-2">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="size-5" />
                {t("pages.properties.basicInformation")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("pages.properties.headers.type")}
                  </label>
                  <p className="text-foreground">{getPropertyTypeLabel(property.property_type)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("pages.properties.headers.totalUnits")}
                  </label>
                  <p className="text-foreground">{property.total_units}</p>
                </div>
              </div>

              {property.description && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">
                    {t("pages.properties.headers.description")}
                  </label>
                  <p className="mt-1 text-foreground">{property.description}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Owner Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="size-5" />
                {t("pages.properties.ownerInformation")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <label className="text-sm font-medium text-muted-foreground">
                  {t("pages.properties.owner.name")}
                </label>
                <p className="text-foreground">{property.owner_name}</p>
              </div>

              <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                <div>
                  <label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Mail className="size-4" />
                    {t("pages.properties.owner.email")}
                  </label>
                  <p className="text-foreground">{property.owner_email}</p>
                </div>
                <div>
                  <label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                    <Phone className="size-4" />
                    {t("pages.properties.owner.phone")}
                  </label>
                  <p className="text-foreground">{property.owner_phone}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Purchase Information */}
          {(property.purchase_price || property.purchase_date) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="size-5" />
                  {t("pages.properties.purchaseInformation")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                  {property.purchase_price && (
                    <div>
                      <label className="text-sm font-medium text-muted-foreground">
                        {t("pages.properties.purchase.price")}
                      </label>
                      <p className="font-semibold text-foreground">
                        ${property.purchase_price.toLocaleString()}
                      </p>
                    </div>
                  )}
                  {property.purchase_date && (
                    <div>
                      <label className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                        <Calendar className="size-4" />
                        {t("pages.properties.purchase.date")}
                      </label>
                      <p className="text-foreground">
                        {new Date(property.purchase_date).toLocaleDateString()}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* Property Images */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ImageIcon className="size-5" />
                {t("pages.properties.images")}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {property.images && property.images.length > 0 ? (
                <div className="grid grid-cols-2 gap-2">
                  {property.images.map((image) => (
                    <div key={image.id} className="relative">
                      <img
                        src={image.url}
                        alt={image.name}
                        className="h-24 w-full rounded-lg border border-border object-cover"
                      />
                      {image.is_primary && (
                        <Badge className="absolute left-1 top-1 text-xs">
                          {t("common.primary")}
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-8 text-center text-muted-foreground">
                  <ImageIcon className="mx-auto mb-2 size-12 opacity-50" />
                  <p className="text-sm">{t("pages.properties.noImages")}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>{t("common.quickActions")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button variant="outline" className="w-full justify-start" onClick={handleAddUnit}>
                <Plus className="mr-2 size-4" />
                {t("pages.units.addUnit")}
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start"
                onClick={() =>
                  router.push(`${authProtectedPaths.UNITS}?property=${propertyId}` as any)
                }>
                <Home className="mr-2 size-4" />
                {t("pages.units.viewUnits")}
              </Button>
            </CardContent>
          </Card>

          {/* Unit Status Summary */}
          <Card>
            <CardHeader>
              <CardTitle>{t("pages.properties.unitStatus")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {t("common.status.available")}
                </span>
                <Badge variant="secondary">{availableUnits}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{t("common.status.occupied")}</span>
                <Badge variant="default">{occupiedUnits}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {t("common.status.maintenance")}
                </span>
                <Badge variant="destructive">{maintenanceUnits}</Badge>
              </div>
              <Separator />
              <div className="flex items-center justify-between font-medium">
                <span className="text-sm text-foreground">{t("common.total")}</span>
                <Badge variant="outline">{totalUnits}</Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t("pages.properties.deleteProperty")}</DialogTitle>
            <DialogDescription>
              {t("pages.properties.deleteConfirmation", { name: property.name })}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowDeleteDialog(false)}>
              {t("common.cancel")}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deletePropertyMutation.isPending}>
              {deletePropertyMutation.isPending ? t("common.deleting") : t("common.delete")}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
