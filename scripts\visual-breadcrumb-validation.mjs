#!/usr/bin/env node

/**
 * Visual validation of breadcrumbs for property assets pages
 * Takes screenshots and validates breadcrumb presence and content
 */

import puppeteer from 'puppeteer';
import { mkdirSync, existsSync } from 'fs';
import path from 'path';

const AUTH_CONFIG = {
  BASE_URL: 'http://localhost:3000',
  LOGIN_URL: 'http://localhost:3000/login',
  CREDENTIALS: {
    username: 'onexapis_admin',
    password: 'Admin@123'
  },
  TIMEOUT: 30000
};

const SCREENSHOTS_DIR = '/Users/<USER>/Desktop/Projects/onex-erp/screenshots';

// Ensure screenshots directory exists
if (!existsSync(SCREENSHOTS_DIR)) {
  mkdirSync(SCREENSHOTS_DIR, { recursive: true });
}

// Property asset pages to test
const TEST_PAGES = [
  {
    path: '/property-assets-dashboard',
    name: 'Property Assets Dashboard',
    expectedBreadcrumbs: ['Property Assets', 'Property Assets Dashboard']
  },
  {
    path: '/property-assets/properties',
    name: 'Properties List',
    expectedBreadcrumbs: ['Property Assets', 'Properties']
  },
  {
    path: '/property-assets/units',
    name: 'Units List',
    expectedBreadcrumbs: ['Property Assets', 'Units']
  },
  {
    path: '/property-assets/contracts',
    name: 'Contracts List',
    expectedBreadcrumbs: ['Property Assets', 'Contracts']
  },
  {
    path: '/property-assets/tenants',
    name: 'Tenants List',
    expectedBreadcrumbs: ['Property Assets', 'Tenants']
  },
  {
    path: '/property-assets/financial',
    name: 'Financial Dashboard',
    expectedBreadcrumbs: ['Property Assets', 'Financial']
  },
  {
    path: '/property-assets/maintenance',
    name: 'Maintenance List',
    expectedBreadcrumbs: ['Property Assets', 'Maintenance']
  }
];

async function performLogin(page) {
  console.log('🔐 Performing authentication...');
  
  try {
    await page.goto(AUTH_CONFIG.LOGIN_URL, { 
      waitUntil: 'networkidle2', 
      timeout: AUTH_CONFIG.TIMEOUT 
    });
    
    // Wait for login form to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Find and fill username/email
    const usernameSelector = 'input[name="email"], input[type="email"], input[name="username"]';
    await page.waitForSelector(usernameSelector, { timeout: 10000 });
    await page.click(usernameSelector);
    await page.type(usernameSelector, AUTH_CONFIG.CREDENTIALS.username);
    
    // Find and fill password
    const passwordSelector = 'input[name="password"], input[type="password"]';
    await page.waitForSelector(passwordSelector, { timeout: 10000 });
    await page.click(passwordSelector);
    await page.type(passwordSelector, AUTH_CONFIG.CREDENTIALS.password);
    
    // Find and click login button
    const loginButton = 'button[type="submit"], button:has-text("Login"), button:has-text("Sign in")';
    await page.click(loginButton);
    
    // Wait for navigation after login
    await page.waitForNavigation({ 
      waitUntil: 'networkidle2', 
      timeout: 30000 
    });
    
    console.log('   ✅ Authentication successful');
    return true;
  } catch (error) {
    console.log(`   ❌ Authentication failed: ${error.message}`);
    return false;
  }
}

async function captureBreadcrumbScreenshot(page, testPage) {
  const screenshotPath = path.join(SCREENSHOTS_DIR, `breadcrumb-${testPage.path.replace(/[\/]/g, '-')}.png`);
  
  // Take full page screenshot first
  await page.screenshot({ 
    path: screenshotPath,
    fullPage: false,
    clip: { x: 0, y: 0, width: 1280, height: 300 }
  });
  
  console.log(`   📸 Screenshot saved: ${screenshotPath}`);
  return screenshotPath;
}

async function validateBreadcrumbs(page, expectedBreadcrumbs) {
  try {
    // Look for breadcrumb navigation
    const breadcrumbSelectors = [
      'nav[aria-label="breadcrumb"]',
      'nav:has(a)',
      '.breadcrumb',
      '[class*="breadcrumb"]'
    ];
    
    let breadcrumbElement = null;
    for (const selector of breadcrumbSelectors) {
      try {
        breadcrumbElement = await page.$(selector);
        if (breadcrumbElement) break;
      } catch (e) {
        continue;
      }
    }
    
    if (!breadcrumbElement) {
      console.log('   ❌ No breadcrumb element found');
      return false;
    }
    
    // Get breadcrumb text content
    const breadcrumbText = await page.evaluate((element) => {
      return element.textContent.trim();
    }, breadcrumbElement);
    
    console.log(`   📍 Breadcrumb content: "${breadcrumbText}"`);
    
    // Check if expected breadcrumbs are present
    const containsExpected = expectedBreadcrumbs.every(expected => 
      breadcrumbText.includes(expected)
    );
    
    if (containsExpected) {
      console.log(`   ✅ All expected breadcrumbs found: ${expectedBreadcrumbs.join(' > ')}`);
      return true;
    } else {
      console.log(`   ❌ Expected breadcrumbs not found. Expected: ${expectedBreadcrumbs.join(' > ')}`);
      return false;
    }
    
  } catch (error) {
    console.log(`   ❌ Error validating breadcrumbs: ${error.message}`);
    return false;
  }
}

async function testBreadcrumbsVisually() {
  console.log('🎯 Starting Visual Breadcrumb Validation for Property Assets\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox'],
    defaultViewport: { width: 1280, height: 720 }
  });
  
  const results = {
    total: TEST_PAGES.length,
    passed: 0,
    failed: 0,
    details: []
  };
  
  try {
    const page = await browser.newPage();
    
    // Perform login first
    const loginSuccess = await performLogin(page);
    if (!loginSuccess) {
      console.log('❌ Failed to login, cannot proceed with tests');
      return;
    }
    
    // Test each page
    for (const testPage of TEST_PAGES) {
      const fullUrl = `${AUTH_CONFIG.BASE_URL}${testPage.path}`;
      
      console.log(`\n📄 Testing: ${testPage.name}`);
      console.log(`   🔗 URL: ${testPage.path}`);
      
      try {
        // Navigate to the page
        await page.goto(fullUrl, { 
          waitUntil: 'networkidle2', 
          timeout: AUTH_CONFIG.TIMEOUT 
        });
        
        // Wait for page to load
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Validate breadcrumbs
        const breadcrumbsValid = await validateBreadcrumbs(page, testPage.expectedBreadcrumbs);
        
        // Take screenshot
        const screenshotPath = await captureBreadcrumbScreenshot(page, testPage);
        
        // Record result
        const result = {
          page: testPage.name,
          path: testPage.path,
          success: breadcrumbsValid,
          screenshot: screenshotPath,
          expected: testPage.expectedBreadcrumbs
        };
        
        results.details.push(result);
        
        if (breadcrumbsValid) {
          results.passed++;
          console.log('   ✅ PASSED');
        } else {
          results.failed++;
          console.log('   ❌ FAILED');
        }
        
      } catch (error) {
        console.log(`   ❌ Error testing page: ${error.message}`);
        results.failed++;
        results.details.push({
          page: testPage.name,
          path: testPage.path,
          success: false,
          error: error.message,
          expected: testPage.expectedBreadcrumbs
        });
      }
    }
    
    // Print summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 VISUAL VALIDATION SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total pages tested: ${results.total}`);
    console.log(`✅ Passed: ${results.passed}`);
    console.log(`❌ Failed: ${results.failed}`);
    console.log(`📸 Screenshots saved to: ${SCREENSHOTS_DIR}`);
    
    // Detailed results
    console.log('\n📋 Detailed Results:');
    results.details.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${index + 1}. ${status} ${result.page} (${result.path})`);
      if (result.expected) {
        console.log(`   Expected: ${result.expected.join(' > ')}`);
      }
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    if (results.failed === 0) {
      console.log('\n🎉 All breadcrumb tests passed! Breadcrumbs are working correctly.');
    } else {
      console.log(`\n⚠️  ${results.failed} test(s) failed. Please check the screenshots and fix any issues.`);
    }
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the visual validation
testBreadcrumbsVisually().catch(console.error);