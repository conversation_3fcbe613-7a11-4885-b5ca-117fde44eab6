import { NextRequest, NextResponse } from "next/server";

import type { Contract, CreateContract } from "@/features/property-assets/types";

import { contractsMockData } from "../mock-data";

// GET /api/property-assets/contracts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") as Contract["status"] | null;
    const contract_type = searchParams.get("contract_type") as Contract["contract_type"] | null;
    const property_id = searchParams.get("property_id") || "";
    const tenant_id = searchParams.get("tenant_id") || "";

    let filteredContracts = [...contractsMockData];

    // Apply property filter
    if (property_id) {
      filteredContracts = filteredContracts.filter(
        (contract) => contract.property_id === property_id
      );
    }

    // Apply tenant filter
    if (tenant_id) {
      filteredContracts = filteredContracts.filter((contract) => contract.tenant_id === tenant_id);
    }

    // Apply search filter
    if (search) {
      filteredContracts = filteredContracts.filter(
        (contract) =>
          contract.id.toLowerCase().includes(search.toLowerCase()) ||
          contract.unit_id.toLowerCase().includes(search.toLowerCase()) ||
          contract.tenant_id.toLowerCase().includes(search.toLowerCase()) ||
          (contract.terms_and_conditions &&
            contract.terms_and_conditions.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply status filter
    if (status) {
      filteredContracts = filteredContracts.filter((contract) => contract.status === status);
    }

    // Apply contract type filter
    if (contract_type) {
      filteredContracts = filteredContracts.filter(
        (contract) => contract.contract_type === contract_type
      );
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedContracts = filteredContracts.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 380));

    return NextResponse.json({
      items: paginatedContracts,
      total: filteredContracts.length,
      page,
      limit,
      totalPages: Math.ceil(filteredContracts.length / limit),
    });
  } catch (error) {
    console.error("Error fetching contracts:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/property-assets/contracts
export async function POST(request: NextRequest) {
  try {
    const body: CreateContract = await request.json();

    // Validate required fields
    if (
      !body.property_id ||
      !body.unit_id ||
      !body.tenant_id ||
      !body.contract_type ||
      !body.start_date ||
      !body.rent_amount
    ) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Check if unit is already under active contract
    const existingContract = contractsMockData.find(
      (contract) => contract.unit_id === body.unit_id && contract.status === "active"
    );

    if (existingContract) {
      return NextResponse.json(
        { error: "Unit is already under an active contract" },
        { status: 409 }
      );
    }

    // Validate date format and logic
    const startDate = new Date(body.start_date);
    const endDate = body.end_date ? new Date(body.end_date) : null;

    if (endDate && endDate <= startDate) {
      return NextResponse.json({ error: "End date must be after start date" }, { status: 400 });
    }

    // Create new contract
    const newContract: Contract = {
      id: `contract_${Date.now()}`,
      ...body,
      status: "active",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company1", // Mock company ID
    };

    // Add to mock data
    contractsMockData.push(newContract);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 650));

    return NextResponse.json({
      data: newContract,
      success: true,
      message: "Contract created successfully",
    });
  } catch (error) {
    console.error("Error creating contract:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
