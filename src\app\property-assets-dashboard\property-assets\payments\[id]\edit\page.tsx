"use client";

import { Suspense } from "react";
import { Metadata } from "next";

import { PaymentForm } from "@/features/property-assets/components/PaymentForm";
import { usePayment } from "@/features/property-assets/hooks/payment";

interface EditPaymentPageProps {
  params: {
    id: string;
  };
}

function EditPaymentContent({ paymentId }: { paymentId: string }) {
  const { data: payment, isLoading, error } = usePayment(paymentId);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading payment data...</div>
      </div>
    );
  }

  if (error || !payment) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center text-red-500">
          Error loading payment data. Please try again.
        </div>
      </div>
    );
  }

  return <PaymentForm initialData={payment} isEditing={true} />;
}

export default function EditPaymentPage({ params }: EditPaymentPageProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EditPaymentContent paymentId={params.id} />
    </Suspense>
  );
}
