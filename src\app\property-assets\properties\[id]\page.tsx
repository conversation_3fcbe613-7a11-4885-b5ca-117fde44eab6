import { Metadata } from "next";

import { PropertyDetailsComponent } from "@/features/property-assets/components/PropertyDetails";

export const metadata: Metadata = {
  title: "Property Details | OneX ERP",
  description: "View detailed property information, units, contracts, and performance metrics",
};

interface PropertyDetailPageProps {
  params: {
    id: string;
  };
}

export default function PropertyDetailPage({ params }: PropertyDetailPageProps) {
  return (
    <div className="container mx-auto px-4 py-6 lg:px-6">
      <PropertyDetailsComponent propertyId={params.id} />
    </div>
  );
}
