import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import { DateColumn, TextColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { authProtectedPaths } from "@/constants/paths";

import { Contract } from "../../types";

const ActionGroupContract = ({
  useDeleteContractMutation,
  row,
  isDeleting,
}: {
  useDeleteContractMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Contract>;
}) => {
  const router = useRouter();
  const contract = row.original;
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(authProtectedPaths.CONTRACTS_ID.replace(":id", contract.id) as any);
    });
  }, [router, contract.id]);

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.CONTRACTS_ID_EDIT.replace(":id", contract.id) as any);
  }, [router, contract.id]);

  const handleDelete = useCallback(async () => {
    return useDeleteContractMutation.mutateAsync(contract.id);
  }, [useDeleteContractMutation, contract.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};

export const columns = (
  useDeleteContractMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<Contract>[] => [
  {
    id: "contract",
    accessorKey: "contract",
    header: t("pages.contracts.headers.contractInfo"),
    sorter: true,
    isMainColumn: true,
    sortKey: "id",
    cell: ({ row }: { row: Row<Contract> }) => {
      const contract = row.original;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={`Contract #${contract?.id?.slice(-8)}`} className="font-medium" />
          <TextColumn
            text={contract?.contract_type || "monthly"}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "tenant",
    accessorKey: "tenant",
    header: t("pages.contracts.headers.tenant"),
    sorter: true,
    sortKey: "tenant_id",
    cell: ({ row }: { row: Row<Contract> }) => {
      const contract = row.original;
      const tenantName = contract?.tenant
        ? `${contract.tenant.first_name} ${contract.tenant.last_name}`
        : "N/A";
      const tenantEmail = contract?.tenant?.email || "";
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={tenantName} className="font-medium" />
          <TextColumn text={tenantEmail} className="text-xs text-muted-foreground opacity-60" />
        </div>
      );
    },
  },
  {
    id: "property",
    accessorKey: "property",
    header: t("pages.contracts.headers.property"),
    sorter: true,
    sortKey: "property_id",
    cell: ({ row }: { row: Row<Contract> }) => {
      const contract = row.original;
      const propertyName = contract?.property?.name || "N/A";
      const unitNumber = contract?.unit?.unit_number;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={propertyName} className="font-medium" />
          <TextColumn
            text={unitNumber ? `Unit: ${unitNumber}` : "N/A"}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "duration",
    accessorKey: "duration",
    header: t("pages.contracts.headers.duration"),
    sorter: true,
    sortKey: "start_date",
    cell: ({ row }: { row: Row<Contract> }) => {
      const contract = row.original;
      const startDate = contract?.start_date
        ? new Date(contract.start_date).toLocaleDateString()
        : "N/A";
      const endDate = contract?.end_date
        ? new Date(contract.end_date).toLocaleDateString()
        : "Open-ended";
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={`${startDate} - ${endDate}`} className="font-medium" />
          <TextColumn
            text={contract?.notice_period_days ? `${contract.notice_period_days} days notice` : ""}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "rentAmount",
    accessorKey: "rentAmount",
    header: t("pages.contracts.headers.rentAmount"),
    sorter: true,
    sortKey: "rent_amount",
    cell: ({ row }: { row: Row<Contract> }) => {
      const contract = row.original;
      const rentAmount = contract?.rent_amount
        ? `$${contract.rent_amount.toLocaleString()}`
        : "N/A";
      const depositAmount = contract?.deposit_amount
        ? `$${contract.deposit_amount.toLocaleString()}`
        : "";
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={rentAmount} className="font-medium" />
          <TextColumn
            text={depositAmount ? `Deposit: ${depositAmount}` : ""}
            className="text-xs text-muted-foreground opacity-60"
          />
        </div>
      );
    },
  },
  {
    id: "status",
    accessorKey: "status",
    header: t("pages.contracts.headers.status"),
    sorter: true,
    sortKey: "status",
    cell: ({ row }: { row: Row<Contract> }) => {
      const contract = row.original;
      const status = contract?.status || "draft";
      return <TextColumn text={t(`pages.contracts.status.${status}`)} className="capitalize" />;
    },
  },
  {
    id: "created_at",
    accessorKey: "created_at",
    header: t("pages.contracts.headers.createdAt"),
    sorter: true,
    sortKey: "created_at",
    cell: ({ row }: { row: Row<Contract> }) => <DateColumn date={row.original.created_at} />,
  },
  {
    id: "updated_at",
    accessorKey: "updated_at",
    header: t("pages.contracts.headers.updatedAt"),
    sorter: true,
    sortKey: "updated_at",
    cell: ({ row }: { row: Row<Contract> }) => <DateColumn date={row.original.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.contracts.headers.actions"),
    cell: ({ row }: { row: Row<Contract> }) => (
      <ActionGroupContract
        useDeleteContractMutation={useDeleteContractMutation}
        row={row}
        isDeleting={isDeleting}
      />
    ),
  },
];
