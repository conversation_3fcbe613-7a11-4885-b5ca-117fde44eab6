import { z } from "zod";

export const createPaymentSchema = z.object({
  contract_id: z.string().min(1, "validation.contractRequired"),
  amount: z.number().positive("validation.amountMustBePositive"),
  payment_date: z.string().min(1, "validation.paymentDateRequired"),
  payment_method: z.enum(["cash", "bank_transfer", "credit_card", "check"], {
    errorMap: () => ({ message: "validation.paymentMethodRequired" }),
  }),
  payment_type: z.enum(["rent", "deposit", "late_fee", "maintenance", "other"], {
    errorMap: () => ({ message: "validation.paymentTypeRequired" }),
  }),
  description: z.string().optional(),
  reference_number: z.string().optional(),
});

export const updatePaymentSchema = createPaymentSchema.partial().extend({
  id: z.string().min(1, "validation.idRequired"),
  status: z.enum(["pending", "completed", "failed", "refunded"]).optional(),
});

export const paymentFilterSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  query: z.string().optional(),
  status: z.enum(["pending", "completed", "failed", "refunded"]).optional(),
  payment_type: z.enum(["rent", "deposit", "late_fee", "maintenance", "other"]).optional(),
  payment_method: z.enum(["cash", "bank_transfer", "credit_card", "check"]).optional(),
  contract_id: z.string().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
});

export type CreatePaymentFormValues = z.infer<typeof createPaymentSchema>;
export type UpdatePaymentFormValues = z.infer<typeof updatePaymentSchema>;
export type PaymentFilterValues = z.infer<typeof paymentFilterSchema>;
