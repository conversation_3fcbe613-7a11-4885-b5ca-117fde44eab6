import { useCallback, useTransition } from "react";
import { useRouter } from "next/navigation";
import { UseMutationResult } from "@tanstack/react-query";
import { Row } from "@tanstack/react-table";

import { DateColumn, TextColumn } from "@/components/custom-table/container/common-column";
import ActionGroup from "@/components/data-table/action-group";
import { CustomColumn } from "@/components/data-table/data-table";
import { authProtectedPaths } from "@/constants/paths";

import { Unit } from "../../types";

export const columns = (
  useDeleteUnitMutation: UseMutationResult<void, Error, string, unknown>,
  isDeleting: boolean,
  t: any
): CustomColumn<Unit>[] => [
  {
    id: "unit",
    accessorKey: "unit",
    header: t("pages.units.headers.unitInfo"),
    sorter: true,
    isMainColumn: true,
    sortKey: "unit_number",
    cell: ({ row }: { row: Row<Unit> }) => {
      const unit = row.original;
      return (
        <div className="flex flex-col gap-1 truncate">
          <TextColumn text={unit?.unit_number} className="font-medium" />
          <TextColumn
            text={unit?.property?.name || `Property ID: ${unit?.property_id}`}
            className="text-xs text-muted-foreground"
          />
        </div>
      );
    },
  },
  {
    id: "type",
    accessorKey: "unit_type",
    sorter: true,
    sortKey: "unit_type",
    header: t("pages.units.headers.type"),
    cell: ({ row }: { row: Row<Unit> }) => {
      const type = row?.original?.unit_type;
      const typeLabel =
        type === "studio"
          ? "Studio"
          : type === "1br"
            ? "1 Bedroom"
            : type === "2br"
              ? "2 Bedroom"
              : type === "3br"
                ? "3 Bedroom"
                : type === "commercial"
                  ? "Commercial"
                  : type;
      return <TextColumn text={typeLabel} />;
    },
  },
  {
    id: "size",
    accessorKey: "size_sqft",
    sorter: true,
    sortKey: "size_sqft",
    header: t("pages.units.headers.size"),
    cell: ({ row }: { row: Row<Unit> }) => {
      const size = row?.original?.square_footage;
      return <TextColumn text={size ? `${size} sq ft` : "-"} />;
    },
  },
  {
    id: "rent",
    accessorKey: "rent_amount",
    sorter: true,
    sortKey: "rent_amount",
    header: t("pages.units.headers.rent"),
    cell: ({ row }: { row: Row<Unit> }) => {
      const rent = row?.original?.rent_amount;
      return <TextColumn text={rent ? `$${rent.toLocaleString()}` : "-"} className="font-medium" />;
    },
  },
  {
    id: "status",
    accessorKey: "status",
    sorter: true,
    sortKey: "status",
    header: t("pages.units.headers.status"),
    cell: ({ row }: { row: Row<Unit> }) => {
      const status = row?.original?.status;
      const statusConfig = {
        available: { label: "Available", class: "text-success" },
        occupied: { label: "Occupied", class: "text-primary" },
        maintenance: { label: "Maintenance", class: "text-warning" },
        unavailable: { label: "Unavailable", class: "text-muted-foreground" },
      };
      const config = statusConfig[status as keyof typeof statusConfig] || {
        label: status,
        class: "text-muted-foreground",
      };
      return <TextColumn text={config.label} className={config.class} />;
    },
  },
  {
    id: "tenant",
    accessorKey: "current_tenant",
    sorter: false,
    header: t("pages.units.headers.tenant"),
    cell: ({ row }: { row: Row<Unit> }) => {
      const tenant = (row?.original as any)?.current_tenant;
      return (
        <div className="flex flex-col gap-1 truncate">
          {tenant ? (
            <>
              <TextColumn text={tenant.name} className="font-medium" />
              <TextColumn text={tenant.email} className="text-xs text-muted-foreground" />
            </>
          ) : (
            <TextColumn text="-" className="text-muted-foreground" />
          )}
        </div>
      );
    },
  },
  {
    id: "lastUpdated",
    accessorKey: "lastUpdated",
    sorter: true,
    sortKey: "updated_at",
    header: t("pages.units.headers.updatedAt"),
    cell: ({ row }: { row: Row<Unit> }) => <DateColumn date={row?.original?.updated_at} />,
  },
  {
    id: "actions",
    header: t("pages.units.headers.actions"),
    cell: ({ row }: { row: Row<Unit> }) => (
      <ActionCell useDeleteUnitMutation={useDeleteUnitMutation} row={row} isDeleting={isDeleting} />
    ),
  },
];

const ActionCell = ({
  useDeleteUnitMutation,
  row,
  isDeleting,
}: {
  useDeleteUnitMutation: UseMutationResult<void, Error, string, unknown>;
  isDeleting: boolean;
  row: Row<Unit>;
}) => {
  const router = useRouter();
  const unit = row.original;
  const [isPending, startTransition] = useTransition();

  const handleView = useCallback(() => {
    startTransition(() => {
      router.push(authProtectedPaths.UNITS_ID.replace(":id", unit.id) as any);
    });
  }, [router, unit.id]);

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.UNITS_ID_EDIT.replace(":id", unit.id) as any);
  }, [router, unit.id]);

  const handleDelete = useCallback(async () => {
    return useDeleteUnitMutation.mutateAsync(unit.id);
  }, [useDeleteUnitMutation, unit.id]);

  return (
    <ActionGroup
      actions={[
        {
          type: "view",
          onClick: handleView,
        },
        {
          type: "edit",
          onClick: handleEdit,
        },
        {
          type: "delete",
          onClick: handleDelete,
          loading: isDeleting,
        },
      ]}
    />
  );
};
