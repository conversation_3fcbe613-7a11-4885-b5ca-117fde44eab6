#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

const TEST_URL = 'http://localhost:3000';

async function testUnitsLoading() {
  console.log('🧪 Testing Units Loading in Layout Management...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  const page = await browser.newPage();

  try {
    // Navigate to layout management with authentication
    console.log('🗺️ Navigating to Layout Management...');
    const authSuccess = await navigateWithAuth(page, `${TEST_URL}/property-assets/layout`);
    if (!authSuccess) {
      throw new Error('Authentication failed');
    }
    
    // Wait for content to load
    await new Promise(resolve => setTimeout(resolve, 5000));
    console.log('✅ Layout Management page loaded\n');

    // Click Open Mapping button
    console.log('🏢 Clicking Open Mapping button...');
    const openMappingFound = await page.evaluate(() => {
      const buttons = Array.from(document.querySelectorAll('button'));
      const openMappingBtn = buttons.find(btn => btn.textContent.includes('Open Mapping'));
      if (openMappingBtn) {
        openMappingBtn.click();
        return true;
      }
      return false;
    });
    
    if (openMappingFound) {
      console.log('✅ Open Mapping button clicked\n');
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Check for units in the unit list
      const unitsInfo = await page.evaluate(() => {
        const unitCards = document.querySelectorAll('[draggable="true"]');
        const unitListText = document.body.textContent;
        
        // Look for specific unit-related text patterns
        const hasUnitListTitle = unitListText.includes('Unit List');
        const hasNoUnitsMessage = unitListText.includes('No units found');
        const hasUnitNumbers = unitListText.includes('101') || unitListText.includes('102') || unitListText.includes('201');
        const hasMappingStats = unitListText.includes('Mapping Statistics');
        
        return {
          hasUnitListTitle,
          draggableUnits: unitCards.length,
          hasNoUnitsMessage,
          hasUnitNumbers,
          hasMappingStats,
          pageTitle: document.title,
          currentUrl: window.location.href
        };
      });
      
      console.log('📊 Units loading status:');
      console.log(`   - Page URL: ${unitsInfo.currentUrl}`);
      console.log(`   - Has Unit List title: ${unitsInfo.hasUnitListTitle}`);
      console.log(`   - Has Mapping Statistics: ${unitsInfo.hasMappingStats}`);
      console.log(`   - Draggable units found: ${unitsInfo.draggableUnits}`);
      console.log(`   - "No units found" message: ${unitsInfo.hasNoUnitsMessage}`);
      console.log(`   - Unit numbers present: ${unitsInfo.hasUnitNumbers}`);
      
      if (unitsInfo.draggableUnits > 0) {
        console.log('🎉 SUCCESS: Units are loading correctly and ready for drag-and-drop!');
      } else if (unitsInfo.hasNoUnitsMessage) {
        console.log('❌ ISSUE: "No units found" - units hook may not be working');
      } else if (unitsInfo.hasMappingStats && unitsInfo.hasUnitListTitle) {
        console.log('⚠️ Interface loaded but no draggable units found');
      } else {
        console.log('❓ Unable to determine units status - may not be on the right page');
      }
      
    } else {
      console.log('❌ Open Mapping button not found');
    }

    // Take final screenshot
    await page.screenshot({ path: 'screenshots/units-loading-test.png', fullPage: true });
    console.log('📸 Screenshot saved to screenshots/units-loading-test.png\n');

  } catch (error) {
    console.error('❌ Error during units loading test:', error.message);
    await page.screenshot({ path: 'screenshots/units-loading-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

// Run the test
testUnitsLoading().catch(console.error);