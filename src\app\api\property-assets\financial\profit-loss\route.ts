import { NextRequest, NextResponse } from "next/server";

import { profitLossReportMockData } from "../../mock-data";

export const dynamic = "force-dynamic";

// GET /api/property-assets/financial/profit-loss
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const property_id = searchParams.get("property_id") || "";
    const period_start = searchParams.get("period_start") || "";
    const period_end = searchParams.get("period_end") || "";

    // In a real implementation, you would:
    // 1. Filter by property_id if specified
    // 2. Filter by date range (period_start, period_end)
    // 3. Calculate income and expenses for the period
    // 4. Calculate profit margins

    const report = { ...profitLossReportMockData };

    // For mock data, adjust dates if provided
    if (period_start) {
      report.period_start = period_start;
    }
    if (period_end) {
      report.period_end = period_end;
    }

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 450));

    return NextResponse.json({
      data: report,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching profit & loss report:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
