import type {
  Contract,
  FinancialSummary,
  MaintenanceRequest,
  Payment,
  ProfitLossReport,
  Property,
  Tenant,
  Transaction,
  Unit,
} from "@/features/property-assets/types";

// Vietnamese Properties Mock Data
export const propertiesMockData: Property[] = [
  {
    id: "prop1",
    name: "<PERSON><PERSON> <PERSON>h<PERSON><PERSON> hợp PRIME COMPLEX",
    address: {
      street: "123 Đường Nguyễn Hu<PERSON>",
      city: "Quận 1",
      state: "TP.HCM",
      zip_code: "70000",
      country: "Vietnam",
    },
    property_type: "mixed",
    description:
      "<PERSON><PERSON> phức hợp cao cấp kết hợp giữa khu dân cư, văn phòng và trung tâm thương mại. Vị trí đắc địa tại trung tâm thành phố với đầy đủ tiện ích hiện đại.",
    total_units: 150,
    owner_name: "Công ty TNHH Đầu tư PRIME",
    owner_email: "<EMAIL>",
    owner_phone: "+84-28-3824-1234",
    purchase_price: 850000000000, // 850 tỷ VND
    purchase_date: "2020-03-15",
    status: "active",
    created_at: "2020-03-15T00:00:00Z",
    updated_at: "2024-01-15T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "prop2",
    name: "Trung tâm thương mại PARC",
    address: {
      street: "456 Đường Lê Lợi",
      city: "Quận 3",
      state: "TP.HCM",
      zip_code: "70000",
      country: "Vietnam",
    },
    property_type: "commercial",
    description:
      "Trung tâm mua sắm hiện đại với hệ thống cửa hàng đa dạng, từ thời trang, điện tử đến ẩm thực. Thiết kế mở tạo không gian thoáng đãng và thuận tiện mua sắm.",
    total_units: 80,
    owner_name: "PARC Development JSC",
    owner_email: "<EMAIL>",
    owner_phone: "+84-28-3932-5678",
    purchase_price: 420000000000, // 420 tỷ VND
    purchase_date: "2019-08-20",
    status: "active",
    created_at: "2019-08-20T00:00:00Z",
    updated_at: "2024-01-10T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "prop3",
    name: "Trung tâm thương mại AVIVA",
    address: {
      street: "789 Đường Đồng Khởi",
      city: "Quận 1",
      state: "TP.HCM",
      zip_code: "70000",
      country: "Vietnam",
    },
    property_type: "commercial",
    description:
      "Trung tâm thương mại cao cấp với các thương hiệu quốc tế hàng đầu. Khu vực ẩm thực đa dạng và không gian giải trí hiện đại cho gia đình.",
    total_units: 120,
    owner_name: "AVIVA Group Vietnam",
    owner_email: "<EMAIL>",
    owner_phone: "+84-28-3825-9999",
    purchase_price: 680000000000, // 680 tỷ VND
    purchase_date: "2021-01-10",
    status: "active",
    created_at: "2021-01-10T00:00:00Z",
    updated_at: "2024-01-12T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "prop4",
    name: "Trung tâm mua sắm GLAMOURISTA FASHION HUB",
    address: {
      street: "321 Đường Nguyễn Trãi",
      city: "Quận 5",
      state: "TP.HCM",
      zip_code: "70000",
      country: "Vietnam",
    },
    property_type: "commercial",
    description:
      "Thiên đường thời trang với các boutique cao cấp, thương hiệu địa phương và quốc tế. Chuyên về thời trang, phụ kiện và làm đẹp.",
    total_units: 95,
    owner_name: "Glamourista Retail Co., Ltd",
    owner_email: "<EMAIL>",
    owner_phone: "+84-28-3855-1111",
    purchase_price: 380000000000, // 380 tỷ VND
    purchase_date: "2022-06-01",
    status: "active",
    created_at: "2022-06-01T00:00:00Z",
    updated_at: "2024-01-08T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "prop5",
    name: "Văn phòng PRIME SPACE",
    address: {
      street: "654 Đường Nam Kỳ Khởi Nghĩa",
      city: "Quận 3",
      state: "TP.HCM",
      zip_code: "70000",
      country: "Vietnam",
    },
    property_type: "commercial",
    description:
      "Tòa nhà văn phòng hạng A với tiêu chuẩn quốc tế. Hệ thống điều hòa trung tâm, thang máy tốc độ cao và an ninh 24/7.",
    total_units: 60,
    owner_name: "Prime Space Development",
    owner_email: "<EMAIL>",
    owner_phone: "+84-28-3930-2222",
    purchase_price: *********000, // 450 tỷ VND
    purchase_date: "2021-09-15",
    status: "active",
    created_at: "2021-09-15T00:00:00Z",
    updated_at: "2024-01-05T00:00:00Z",
    company_id: "company1",
  },
];

// Vietnamese Units Mock Data
export const unitsMockData: Unit[] = [
  // PRIME COMPLEX Units (Mixed: Apartments + Offices + Retail)
  {
    id: "unit1",
    property_id: "prop1",
    unit_number: "A-101",
    unit_type: "1br",
    floor: 1,
    square_footage: 750,
    bedrooms: 1,
    bathrooms: 1,
    description: "Căn hộ 1 phòng ngủ hiện đại với view thành phố",
    rent_amount: 25000000, // 25 triệu VND/tháng
    deposit_amount: 50000000, // 2 tháng tiền thuê
    status: "available",
    amenities: ["Điều hòa", "Nóng lạnh", "Ban công", "Chỗ đậy xe"],
    created_at: "2020-03-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit2",
    property_id: "prop1",
    unit_number: "A-102",
    unit_type: "2br",
    floor: 1,
    square_footage: 1100,
    bedrooms: 2,
    bathrooms: 2,
    description: "Căn hộ 2 phòng ngủ cao cấp với nội thất cơ bản",
    rent_amount: ********, // 35 triệu VND/tháng
    deposit_amount: ********,
    status: "occupied",
    amenities: ["Điều hòa", "Nóng lạnh", "Ban công lớn", "Chỗ đậy xe", "Bếp mở"],
    created_at: "2020-03-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit3",
    property_id: "prop1",
    unit_number: "B-201",
    unit_type: "office",
    floor: 2,
    square_footage: 800,
    description: "Văn phòng nhỏ phù hợp cho startup 10-15 người",
    rent_amount: ********, // 45 triệu VND/tháng
    deposit_amount: *********, // 3 tháng tiền thuê
    status: "occupied",
    amenities: ["Điều hòa trung tâm", "Internet cao tốc", "Phòng họp chung", "Bảo vệ 24/7"],
    created_at: "2020-03-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit4",
    property_id: "prop1",
    unit_number: "R-G01",
    unit_type: "retail",
    floor: 0,
    square_footage: 500,
    description: "Cửa hàng tầng trệt vị trí đắc địa mặt đường lớn",
    rent_amount: 80000000, // 80 triệu VND/tháng
    deposit_amount: *********,
    status: "available",
    amenities: ["Mặt tiền rộng", "Biển hiệu", "Kho hàng", "Vỉa hè rộng"],
    created_at: "2020-03-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit5",
    property_id: "prop1",
    unit_number: "A-301",
    unit_type: "3br",
    floor: 3,
    square_footage: 1400,
    bedrooms: 3,
    bathrooms: 2,
    description: "Căn hộ penthouse 3 phòng ngủ với terrace riêng",
    rent_amount: 55000000, // 55 triệu VND/tháng
    deposit_amount: *********,
    status: "maintenance",
    amenities: ["Điều hòa", "Nóng lạnh", "Terrace", "BBQ area", "Chỗ đậy xe", "Thang máy riêng"],
    created_at: "2020-03-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // PARC Mall Units (Retail)
  {
    id: "unit6",
    property_id: "prop2",
    unit_number: "L1-001",
    unit_type: "retail",
    floor: 1,
    square_footage: 200,
    description: "Cửa hàng thời trang nữ tại tầng 1 vị trí đẹp",
    rent_amount: ********, // 45 triệu VND/tháng
    deposit_amount: *********,
    status: "occupied",
    amenities: ["Điều hòa", "Đèn led", "Kệ trưng bày", "Phòng thử đồ"],
    created_at: "2019-08-20T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit7",
    property_id: "prop2",
    unit_number: "L2-015",
    unit_type: "retail",
    floor: 2,
    square_footage: 150,
    description: "Quán cafe nhỏ xinh với không gian ấm cúng",
    rent_amount: 32000000, // 32 triệu VND/tháng
    deposit_amount: 96000000,
    status: "available",
    amenities: ["Hệ thống nước", "Điện 3 pha", "Thông gió", "Khu vực ngồi ngoài"],
    created_at: "2019-08-20T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // AVIVA Mall Units (Premium Retail)
  {
    id: "unit8",
    property_id: "prop3",
    unit_number: "G-A01",
    unit_type: "retail",
    floor: 0,
    square_footage: 300,
    description: "Flagship store thương hiệu quốc tế tại tầng trệt",
    rent_amount: ********, // 95 triệu VND/tháng
    deposit_amount: *********,
    status: "occupied",
    amenities: ["Mặt tiền kính lớn", "Điều hòa VRV", "Hệ thống âm thanh", "LED wall"],
    created_at: "2021-01-10T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit9",
    property_id: "prop3",
    unit_number: "L3-F12",
    unit_type: "retail",
    floor: 3,
    square_footage: 180,
    description: "Nhà hàng cao cấp với view panorama thành phố",
    rent_amount: 65000000, // 65 triệu VND/tháng
    deposit_amount: *********,
    status: "available",
    amenities: ["Bếp công nghiệp", "Hệ thống khói", "Sân thượng", "Bar counter"],
    created_at: "2021-01-10T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // GLAMOURISTA Fashion Hub Units
  {
    id: "unit10",
    property_id: "prop4",
    unit_number: "F-101",
    unit_type: "retail",
    floor: 1,
    square_footage: 120,
    description: "Boutique thời trang cao cấp cho phái đẹp",
    rent_amount: ********, // 28 triệu VND/tháng
    deposit_amount: 84000000,
    status: "occupied",
    amenities: ["Mirror wall", "Đèn spot", "Fitting room", "Storage"],
    created_at: "2022-06-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // PRIME SPACE Office Units
  {
    id: "unit11",
    property_id: "prop5",
    unit_number: "F5-501",
    unit_type: "office",
    floor: 5,
    square_footage: 1200,
    description: "Văn phòng hạng A cho 50-60 nhân viên với view sông",
    rent_amount: *********, // 180 triệu VND/tháng
    deposit_amount: *********,
    status: "occupied",
    amenities: ["Điều hòa VRV", "Raised floor", "Fiber internet", "Phòng họp", "Pantry"],
    created_at: "2021-09-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "unit12",
    property_id: "prop5",
    unit_number: "F8-801",
    unit_type: "office",
    floor: 8,
    square_footage: 800,
    description: "Văn phòng boutique cho công ty tư vấn 25-30 người",
    rent_amount: *********, // 125 triệu VND/tháng
    deposit_amount: *********,
    status: "available",
    amenities: ["Smart building", "Key card access", "Meeting rooms", "Reception area"],
    created_at: "2021-09-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
];

// Vietnamese Tenants Mock Data
export const tenantsMockData: Tenant[] = [
  {
    id: "tenant1",
    first_name: "Nguyễn Văn",
    last_name: "An",
    email: "<EMAIL>",
    phone: "+84-90-123-4567",
    emergency_contact_name: "Nguyễn Thị Bình",
    emergency_contact_phone: "+84-91-234-5678",
    identification_type: "passport",
    identification_number: "N1234567",
    date_of_birth: "1985-03-15",
    employment_status: "employed",
    employer_name: "Công ty TNHH Công nghệ ABC",
    monthly_income: ********, // 45 triệu VND
    status: "active",
    created_at: "2023-01-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "tenant2",
    first_name: "Trần Thị",
    last_name: "Bình",
    email: "<EMAIL>",
    phone: "+84-93-456-7890",
    emergency_contact_name: "Trần Văn Cường",
    emergency_contact_phone: "+84-94-567-8901",
    identification_type: "national_id",
    identification_number: "079185001234",
    date_of_birth: "1990-07-22",
    employment_status: "self_employed",
    employer_name: "Kinh doanh thời trang",
    monthly_income: 38000000, // 38 triệu VND
    status: "active",
    created_at: "2023-02-10T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "tenant3",
    first_name: "Lê Minh",
    last_name: "Cường",
    email: "<EMAIL>",
    phone: "+84-97-789-0123",
    emergency_contact_name: "Lê Thị Dung",
    emergency_contact_phone: "+84-98-890-1234",
    identification_type: "national_id",
    identification_number: "************",
    date_of_birth: "1988-11-08",
    employment_status: "employed",
    employer_name: "Ngân hàng Vietcombank",
    monthly_income: ********, // 52 triệu VND
    status: "active",
    created_at: "2023-03-05T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
];

// Vietnamese Contracts Mock Data - References existing properties, units, and tenants
export const contractsMockData: Contract[] = [
  // Contract for unit2 (A-102 - 2br apartment) with tenant1
  {
    id: "contract1",
    property_id: "prop1", // PRIME COMPLEX
    unit_id: "unit2", // A-102 (occupied)
    tenant_id: "tenant1", // Nguyễn Văn An
    contract_type: "monthly",
    start_date: "2023-06-01",
    end_date: "2024-06-01",
    rent_amount: ********, // Matches unit2 rent_amount
    deposit_amount: ********, // Matches unit2 deposit_amount
    late_fee_amount: 1000000,
    rent_due_day: 5,
    status: "active",
    terms_and_conditions:
      "Hợp đồng thuê căn hộ theo quy định pháp luật Việt Nam. Bên thuê có trách nhiệm bảo quản tài sản và thanh toán đúng hạn.",
    auto_renewal: false,
    notice_period_days: 30,
    created_at: "2023-06-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // Contract for unit3 (B-201 - startup office) with tenant2
  {
    id: "contract2",
    property_id: "prop1", // PRIME COMPLEX
    unit_id: "unit3", // B-201 (occupied)
    tenant_id: "tenant2", // Trần Thị Bình
    contract_type: "annual",
    start_date: "2023-08-01",
    end_date: "2024-08-01",
    rent_amount: ********, // Matches unit3 rent_amount
    deposit_amount: *********, // Matches unit3 deposit_amount
    late_fee_amount: 1500000,
    rent_due_day: 1,
    status: "active",
    terms_and_conditions:
      "Hợp đồng thuê văn phòng 1 năm với điều khoản gia hạn tự động. Bao gồm dịch vụ bảo vệ và vệ sinh.",
    auto_renewal: true,
    notice_period_days: 60,
    created_at: "2023-08-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // Contract for unit6 (L1-001 - fashion store) with tenant3
  {
    id: "contract3",
    property_id: "prop2", // PARC Mall
    unit_id: "unit6", // L1-001 (occupied)
    tenant_id: "tenant3", // Lê Minh Cường
    contract_type: "profit_sharing",
    start_date: "2023-09-15",
    end_date: "2024-09-15",
    rent_amount: 32000000, // Base rent (lower than unit6's 45M due to profit sharing)
    deposit_amount: 96000000, // Adjusted for profit sharing model
    late_fee_amount: 800000,
    rent_due_day: 10,
    status: "active",
    profit_sharing_percentage: 15, // 15% doanh thu
    terms_and_conditions:
      "Hợp đồng chia sẻ lợi nhuận với mức thuê cơ bản + 15% doanh thu hàng tháng. Báo cáo doanh thu hàng tháng.",
    auto_renewal: false,
    notice_period_days: 45,
    created_at: "2023-09-15T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // Additional contracts for other occupied units

  // Contract for unit8 (G-A01 - flagship store at AVIVA) with new tenant
  {
    id: "contract4",
    property_id: "prop3", // AVIVA Mall
    unit_id: "unit8", // G-A01 (occupied)
    tenant_id: "tenant1", // Nguyễn Văn An (can have multiple contracts)
    contract_type: "annual",
    start_date: "2023-10-01",
    end_date: "2024-10-01",
    rent_amount: ********, // Matches unit8 rent_amount
    deposit_amount: *********, // Matches unit8 deposit_amount
    late_fee_amount: 2000000,
    rent_due_day: 1,
    status: "active",
    terms_and_conditions:
      "Hợp đồng thuê flagship store cao cấp tại AVIVA Mall. Bao gồm chi phí marketing và quảng cáo chung.",
    auto_renewal: true,
    notice_period_days: 90,
    created_at: "2023-10-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // Contract for unit10 (F-101 - boutique) with tenant2
  {
    id: "contract5",
    property_id: "prop4", // GLAMOURISTA Fashion Hub
    unit_id: "unit10", // F-101 (occupied)
    tenant_id: "tenant2", // Trần Thị Bình (fashion business)
    contract_type: "monthly",
    start_date: "2023-07-01",
    end_date: "2024-07-01",
    rent_amount: ********, // Matches unit10 rent_amount
    deposit_amount: 84000000, // Matches unit10 deposit_amount
    late_fee_amount: 700000,
    rent_due_day: 5,
    status: "active",
    terms_and_conditions:
      "Hợp đồng thuê boutique thời trang với quyền sử dụng không gian trưng bày chung và tham gia các sự kiện thời trang.",
    auto_renewal: false,
    notice_period_days: 30,
    created_at: "2023-07-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // Contract for unit11 (F5-501 - premium office) with tenant3
  {
    id: "contract6",
    property_id: "prop5", // PRIME SPACE
    unit_id: "unit11", // F5-501 (occupied)
    tenant_id: "tenant3", // Lê Minh Cường (banking professional)
    contract_type: "annual",
    start_date: "2023-04-01",
    end_date: "2025-04-01", // 2-year contract
    rent_amount: *********, // Matches unit11 rent_amount
    deposit_amount: *********, // Matches unit11 deposit_amount
    late_fee_amount: 3000000,
    rent_due_day: 1,
    status: "active",
    terms_and_conditions:
      "Hợp đồng thuê văn phòng hạng A 2 năm. Bao gồm dịch vụ quản lý tòa nhà, bảo vệ 24/7, và quyền sử dụng phòng họp chung.",
    auto_renewal: true,
    notice_period_days: 90,
    created_at: "2023-04-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // Expired contract example
  {
    id: "contract7",
    property_id: "prop1", // PRIME COMPLEX
    unit_id: "unit1", // A-101 (available - previous tenant moved out)
    tenant_id: "tenant1", // Previous tenant
    contract_type: "monthly",
    start_date: "2022-12-01",
    end_date: "2023-12-01",
    rent_amount: 25000000, // Matches unit1 rent_amount
    deposit_amount: 50000000, // Matches unit1 deposit_amount
    late_fee_amount: 800000,
    rent_due_day: 5,
    status: "expired",
    terms_and_conditions: "Hợp đồng thuê căn hộ 1 phòng ngủ đã hết hạn. Tenant đã chuyển đi.",
    auto_renewal: false,
    notice_period_days: 30,
    created_at: "2022-12-01T00:00:00Z",
    updated_at: "2023-12-01T00:00:00Z",
    company_id: "company1",
  },
];

// Vietnamese Payments Mock Data - References existing contracts
export const paymentsMockData: Payment[] = [
  // Payments for contract1 (unit2 - A-102 apartment)
  {
    id: "payment1",
    contract_id: "contract1",
    amount: ********, // 35 triệu VND
    payment_date: "2024-01-05",
    payment_method: "bank_transfer",
    payment_type: "rent",
    description: "Tiền thuê tháng 1/2024 - Căn hộ A-102",
    status: "completed",
    reference_number: "VTB20240105001",
    created_at: "2024-01-05T00:00:00Z",
    updated_at: "2024-01-05T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "payment2",
    contract_id: "contract1",
    amount: ********, // 70 triệu VND - Tiền cọc
    payment_date: "2023-06-01",
    payment_method: "cash",
    payment_type: "deposit",
    description: "Tiền cọc căn hộ A-102 - 2 tháng tiền thuê",
    status: "completed",
    reference_number: "DEP20230601001",
    created_at: "2023-06-01T00:00:00Z",
    updated_at: "2023-06-01T00:00:00Z",
    company_id: "company1",
  },

  // Payments for contract2 (unit3 - B-201 office)
  {
    id: "payment3",
    contract_id: "contract2",
    amount: ********, // 45 triệu VND
    payment_date: "2024-01-01",
    payment_method: "bank_transfer",
    payment_type: "rent",
    description: "Tiền thuê tháng 1/2024 - Văn phòng B-201",
    status: "completed",
    reference_number: "VTB20240101001",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "payment4",
    contract_id: "contract2",
    amount: *********, // 135 triệu VND - Tiền cọc
    payment_date: "2023-08-01",
    payment_method: "bank_transfer",
    payment_type: "deposit",
    description: "Tiền cọc văn phòng B-201 - 3 tháng tiền thuê",
    status: "completed",
    reference_number: "DEP20230801001",
    created_at: "2023-08-01T00:00:00Z",
    updated_at: "2023-08-01T00:00:00Z",
    company_id: "company1",
  },

  // Payments for contract3 (unit6 - L1-001 fashion store with profit sharing)
  {
    id: "payment5",
    contract_id: "contract3",
    amount: ********, // 32M base + 5M profit sharing
    payment_date: "2024-01-10",
    payment_method: "bank_transfer",
    payment_type: "rent",
    description: "Thuê cửa hàng L1-001 + 15% doanh thu tháng 01/2024",
    status: "completed",
    reference_number: "VTB20240110002",
    created_at: "2024-01-10T00:00:00Z",
    updated_at: "2024-01-10T00:00:00Z",
    company_id: "company1",
  },

  // Payments for contract4 (unit8 - G-A01 flagship store)
  {
    id: "payment6",
    contract_id: "contract4",
    amount: ********, // 95 triệu VND
    payment_date: "2024-01-01",
    payment_method: "bank_transfer",
    payment_type: "rent",
    description: "Tiền thuê tháng 1/2024 - Flagship store G-A01",
    status: "completed",
    reference_number: "VTB20240101003",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // Payments for contract5 (unit10 - F-101 boutique)
  {
    id: "payment7",
    contract_id: "contract5",
    amount: ********, // 28 triệu VND
    payment_date: "2024-01-05",
    payment_method: "bank_transfer",
    payment_type: "rent",
    description: "Tiền thuê tháng 1/2024 - Boutique F-101",
    status: "completed",
    reference_number: "VTB20240105002",
    created_at: "2024-01-05T00:00:00Z",
    updated_at: "2024-01-05T00:00:00Z",
    company_id: "company1",
  },

  // Payments for contract6 (unit11 - F5-501 premium office)
  {
    id: "payment8",
    contract_id: "contract6",
    amount: *********, // 180 triệu VND
    payment_date: "2024-01-01",
    payment_method: "bank_transfer",
    payment_type: "rent",
    description: "Tiền thuê tháng 1/2024 - Văn phòng F5-501",
    status: "completed",
    reference_number: "VTB20240101004",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
    company_id: "company1",
  },

  // Pending payment example
  {
    id: "payment9",
    contract_id: "contract1",
    amount: ********, // 35 triệu VND
    payment_date: "2024-02-05",
    payment_method: "bank_transfer",
    payment_type: "rent",
    description: "Tiền thuê tháng 2/2024 - Căn hộ A-102",
    status: "pending",
    reference_number: "VTB20240205001",
    created_at: "2024-02-01T00:00:00Z",
    updated_at: "2024-02-01T00:00:00Z",
    company_id: "company1",
  },
];

// Vietnamese Maintenance Requests Mock Data
export const maintenanceRequestsMockData: MaintenanceRequest[] = [
  {
    id: "maint1",
    property_id: "prop1",
    unit_id: "unit5",
    tenant_id: "tenant1",
    title: "Sửa chữa hệ thống điều hòa",
    description:
      "Điều hòa phòng ngủ chính không hoạt động, cần kiểm tra và sửa chữa. Có thể do hết gas hoặc lỗi máy nén.",
    priority: "high",
    category: "hvac",
    status: "in_progress",
    reported_date: "2024-01-10",
    scheduled_date: "2024-01-12",
    estimated_cost: 5000000, // 5 triệu VND
    contractor_name: "Công ty TNHH Kỹ thuật lạnh Đại Phát",
    contractor_phone: "+84-28-3825-1111",
    notes: "Cần thay thế máy nén và bổ sung gas. Dự kiến hoàn thành trong 2 ngày.",
    created_at: "2024-01-10T00:00:00Z",
    updated_at: "2024-01-12T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "maint2",
    property_id: "prop2",
    unit_id: "unit7",
    title: "Thay thế hệ thống đèn LED",
    description:
      "Đèn LED trang trí bị hỏng nhiều bóng, cần thay thế toàn bộ để đảm bảo ánh sáng đều.",
    priority: "medium",
    category: "electrical",
    status: "open",
    reported_date: "2024-01-08",
    estimated_cost: 3500000, // 3.5 triệu VND
    contractor_name: "Điện lạnh Minh Phúc",
    contractor_phone: "+84-90-123-9999",
    notes: "Cần đặt hàng đèn LED mới, dự kiến 3-5 ngày làm việc.",
    created_at: "2024-01-08T00:00:00Z",
    updated_at: "2024-01-08T00:00:00Z",
    company_id: "company1",
  },
  {
    id: "maint3",
    property_id: "prop3",
    unit_id: "unit9",
    title: "Vệ sinh hệ thống thông gió bếp",
    description:
      "Hệ thống hút mùi và thông gió bếp nhà hàng cần được vệ sinh định kỳ theo quy định vệ sinh thực phẩm.",
    priority: "medium",
    category: "other",
    status: "completed",
    reported_date: "2024-01-05",
    scheduled_date: "2024-01-07",
    completed_date: "2024-01-07",
    estimated_cost: 2000000, // 2 triệu VND
    actual_cost: 1800000, // 1.8 triệu VND
    contractor_name: "Dịch vụ vệ sinh công nghiệp Sạch Nhà",
    contractor_phone: "+84-93-456-7777",
    notes: "Đã hoàn thành vệ sinh và kiểm tra hệ thống. Tất cả hoạt động bình thường.",
    created_at: "2024-01-05T00:00:00Z",
    updated_at: "2024-01-07T00:00:00Z",
    company_id: "company1",
  },
];

// Financial Summary Mock Data
export const financialSummaryMockData: FinancialSummary = {
  total_properties: 5,
  total_units: 505, // Tổng số unit của 5 properties
  occupied_units: 387, // ~77% occupancy rate
  vacancy_rate: 23.4, // 23.4% vacancy
  monthly_rental_income: *********0, // 2.85 tỷ VND/tháng
  monthly_expenses: *********0, // 1.2 tỷ VND/tháng
  net_monthly_income: 1650000000, // 1.65 tỷ VND/tháng
  ytd_rental_income: 34200000000, // 34.2 tỷ VND năm
  ytd_expenses: 14400000000, // 14.4 tỷ VND năm
  ytd_net_income: 19800000000, // 19.8 tỷ VND năm
};

// Profit Loss Report Mock Data
export const profitLossReportMockData: ProfitLossReport = {
  period_start: "2024-01-01",
  period_end: "2024-01-31",
  rental_income: *********0, // 2.85 tỷ VND
  other_income: *********, // 150 triệu VND (parking, ads, etc.)
  total_income: 3000000000, // 3 tỷ VND
  maintenance_expenses: *********, // 180 triệu VND
  utility_expenses: *********, // 320 triệu VND
  insurance_expenses: 85000000, // 85 triệu VND
  property_tax_expenses: *********, // 120 triệu VND
  management_expenses: *********, // 450 triệu VND
  other_expenses: ********, // 45 triệu VND
  total_expenses: *********0, // 1.2 tỷ VND
  net_income: *********0, // 1.8 tỷ VND
  profit_margin: 60.0, // 60% profit margin
};

// Transactions Mock Data - References existing properties, units, and contracts
export const transactionsMockData: Transaction[] = [
  // Income transactions from contracts
  {
    id: "trans1",
    property_id: "prop1", // PRIME COMPLEX
    unit_id: "unit2", // A-102
    contract_id: "contract1",
    date: "2024-01-05",
    amount: ********, // 35 triệu VND
    type: "income",
    category: "Tiền thuê",
    description: "Tiền thuê tháng 01/2024 - Căn hộ A-102",
    payment_method: "bank_transfer",
    reference_number: "VTB20240105001",
    created_at: "2024-01-05T00:00:00Z",
    updated_at: "2024-01-05T00:00:00Z",
  },
  {
    id: "trans2",
    property_id: "prop1", // PRIME COMPLEX
    unit_id: "unit3", // B-201
    contract_id: "contract2",
    date: "2024-01-01",
    amount: ********, // 45 triệu VND
    type: "income",
    category: "Tiền thuê",
    description: "Tiền thuê tháng 01/2024 - Văn phòng B-201",
    payment_method: "bank_transfer",
    reference_number: "VTB20240101001",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "trans3",
    property_id: "prop2", // PARC Mall
    unit_id: "unit6", // L1-001
    contract_id: "contract3",
    date: "2024-01-10",
    amount: ********, // 32M base + 5M profit sharing
    type: "income",
    category: "Tiền thuê + Chia sẻ lợi nhuận",
    description: "Thuê cửa hàng L1-001 + 15% doanh thu tháng 01/2024",
    payment_method: "bank_transfer",
    reference_number: "VTB20240110002",
    created_at: "2024-01-10T00:00:00Z",
    updated_at: "2024-01-10T00:00:00Z",
  },
  {
    id: "trans4",
    property_id: "prop3", // AVIVA Mall
    unit_id: "unit8", // G-A01
    contract_id: "contract4",
    date: "2024-01-01",
    amount: ********, // 95 triệu VND
    type: "income",
    category: "Tiền thuê",
    description: "Tiền thuê tháng 01/2024 - Flagship store G-A01",
    payment_method: "bank_transfer",
    reference_number: "VTB20240101003",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: "trans5",
    property_id: "prop4", // GLAMOURISTA Fashion Hub
    unit_id: "unit10", // F-101
    contract_id: "contract5",
    date: "2024-01-05",
    amount: ********, // 28 triệu VND
    type: "income",
    category: "Tiền thuê",
    description: "Tiền thuê tháng 01/2024 - Boutique F-101",
    payment_method: "bank_transfer",
    reference_number: "VTB20240105002",
    created_at: "2024-01-05T00:00:00Z",
    updated_at: "2024-01-05T00:00:00Z",
  },
  {
    id: "trans6",
    property_id: "prop5", // PRIME SPACE
    unit_id: "unit11", // F5-501
    contract_id: "contract6",
    date: "2024-01-01",
    amount: *********, // 180 triệu VND
    type: "income",
    category: "Tiền thuê",
    description: "Tiền thuê tháng 01/2024 - Văn phòng F5-501",
    payment_method: "bank_transfer",
    reference_number: "VTB20240101004",
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },

  // Expense transactions
  {
    id: "trans7",
    property_id: "prop1", // PRIME COMPLEX
    unit_id: "unit5", // Unit under maintenance
    date: "2024-01-12",
    amount: -5000000, // -5 triệu VND (matches maint1 estimated_cost)
    type: "expense",
    category: "Bảo trì",
    description: "Sửa chữa hệ thống điều hòa - Căn A-301",
    payment_method: "bank_transfer",
    reference_number: "EXP20240112001",
    created_at: "2024-01-12T00:00:00Z",
    updated_at: "2024-01-12T00:00:00Z",
  },
  {
    id: "trans8",
    property_id: "prop2", // PARC Mall
    unit_id: "unit7", // L2-015
    date: "2024-01-08",
    amount: -3500000, // -3.5 triệu VND (matches maint2 estimated_cost)
    type: "expense",
    category: "Điện",
    description: "Thay thế hệ thống đèn LED - Quán cafe L2-015",
    payment_method: "bank_transfer",
    reference_number: "EXP20240108001",
    created_at: "2024-01-08T00:00:00Z",
    updated_at: "2024-01-08T00:00:00Z",
  },
  {
    id: "trans9",
    property_id: "prop3", // AVIVA Mall
    unit_id: "unit9", // L3-F12
    date: "2024-01-07",
    amount: -1800000, // -1.8 triệu VND (matches maint3 actual_cost)
    type: "expense",
    category: "Vệ sinh",
    description: "Vệ sinh hệ thống thông gió bếp - Nhà hàng L3-F12",
    payment_method: "cash",
    reference_number: "CASH20240107001",
    created_at: "2024-01-07T00:00:00Z",
    updated_at: "2024-01-07T00:00:00Z",
  },
  {
    id: "trans10",
    property_id: "prop5", // PRIME SPACE
    date: "2024-01-07",
    amount: -********, // -15 triệu VND
    type: "expense",
    category: "Vệ sinh",
    description: "Dịch vụ vệ sinh văn phòng PRIME SPACE tháng 01/2024",
    payment_method: "cash",
    reference_number: "CASH20240107002",
    created_at: "2024-01-07T00:00:00Z",
    updated_at: "2024-01-07T00:00:00Z",
  },
  {
    id: "trans11",
    property_id: "prop1", // PRIME COMPLEX
    date: "2024-01-03",
    amount: -8500000, // -8.5 triệu VND
    type: "expense",
    category: "Bảo trì",
    description: "Sửa chữa thang máy tòa nhà PRIME COMPLEX",
    payment_method: "bank_transfer",
    reference_number: "EXP20240103001",
    created_at: "2024-01-03T00:00:00Z",
    updated_at: "2024-01-03T00:00:00Z",
  },
  {
    id: "trans12",
    property_id: "prop2", // PARC Mall
    date: "2024-01-15",
    amount: -********, // -12 triệu VND
    type: "expense",
    category: "Utilities",
    description: "Hóa đơn điện nước PARC Mall tháng 01/2024",
    payment_method: "bank_transfer",
    reference_number: "EXP20240115001",
    created_at: "2024-01-15T00:00:00Z",
    updated_at: "2024-01-15T00:00:00Z",
  },
];
