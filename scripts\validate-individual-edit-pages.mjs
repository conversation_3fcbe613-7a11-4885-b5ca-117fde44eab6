#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { performLogin } from './utils/auth.mjs';

const testUrls = [
  {
    name: 'Property Edit',
    url: 'http://localhost:3000/property-assets/properties/prop1/edit',
    expectedText: 'PRIME COMPLEX'
  },
  {
    name: 'Contract Edit', 
    url: 'http://localhost:3000/property-assets/contracts/contract1/edit',
    expectedText: 'contract1'
  },
  {
    name: 'Tenant Edit',
    url: 'http://localhost:3000/property-assets/tenants/tenant1/edit', 
    expectedText: '<PERSON>uy<PERSON><PERSON>n <PERSON>'
  },
  {
    name: 'Maintenance Edit',
    url: 'http://localhost:3000/property-assets/maintenance/maint1/edit',
    expectedText: '<PERSON><PERSON><PERSON> chữa hệ thống điều hòa'
  }
];

async function validateEditPages() {
  const browser = await puppeteer.launch({ 
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport({ width: 1920, height: 1080 });
    
    console.log('🔐 Logging into application...');
    await performLogin(page);
    
    const results = [];
    
    for (const test of testUrls) {
      console.log(`\n📝 Testing ${test.name}...`);
      
      try {
        await page.goto(test.url, { waitUntil: 'networkidle0', timeout: 10000 });
        
        // Wait for the page to load
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        // Check if Vietnamese data is loaded
        const pageContent = await page.content();
        const hasExpectedText = pageContent.includes(test.expectedText);
        
        // Take screenshot
        const screenshotPath = `/Users/<USER>/Desktop/Projects/onex-erp/screenshots/edit-${test.name.toLowerCase().replace(' ', '-')}.png`;
        await page.screenshot({ 
          path: screenshotPath, 
          fullPage: true 
        });
        
        results.push({
          name: test.name,
          url: test.url,
          success: hasExpectedText,
          expectedText: test.expectedText,
          found: hasExpectedText ? '✅' : '❌',
          screenshot: screenshotPath
        });
        
        console.log(`   ${hasExpectedText ? '✅' : '❌'} ${test.expectedText} ${hasExpectedText ? 'found' : 'not found'}`);
        
      } catch (error) {
        console.log(`   ❌ Error: ${error.message}`);
        results.push({
          name: test.name,
          url: test.url,
          success: false,
          error: error.message
        });
      }
    }
    
    // Summary report
    console.log('\n📊 EDIT PAGES VALIDATION SUMMARY');
    console.log('='.repeat(50));
    
    const successful = results.filter(r => r.success).length;
    const total = results.length;
    
    results.forEach(result => {
      console.log(`${result.found || '❌'} ${result.name}`);
      if (result.expectedText) {
        console.log(`   Expected: "${result.expectedText}"`);
      }
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
    });
    
    console.log(`\n🎯 Results: ${successful}/${total} edit pages loaded Vietnamese data successfully`);
    
    if (successful === total) {
      console.log('🎉 All individual edit pages are working correctly with Vietnamese data!');
    } else {
      console.log('⚠️  Some edit pages need attention');
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    throw error;
  } finally {
    await browser.close();
  }
}

// Run the validation
validateEditPages().catch(error => {
  console.error('❌ Validation failed:', error);
  process.exit(1);
});