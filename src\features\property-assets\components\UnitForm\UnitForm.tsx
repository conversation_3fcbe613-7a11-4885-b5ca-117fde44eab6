"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2, Upload, X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
// Form and validation
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { authProtectedPaths } from "@/constants/paths";

import { useProperties } from "../../hooks/useProperties";
import { useCreateUnit, useUpdateUnit } from "../../hooks/useUnits";
import type { Unit } from "../../types";
// Unit specific imports
import {
  createUnitSchema,
  updateUnitSchema,
  type CreateUnitFormValues,
  type UpdateUnitFormValues,
} from "../../utils/validators/unit";

interface UnitFormProps {
  initialData?: Unit;
  isEditing?: boolean;
}

export function UnitForm({ initialData, isEditing = false }: UnitFormProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImages, setUploadedImages] = useState<{ name: string; image: string }[]>([]);

  // Mutations
  const createUnitMutation = useCreateUnit();
  const updateUnitMutation = useUpdateUnit();

  // Fetch properties for selection
  const { data: propertiesData, isLoading: propertiesLoading } = useProperties({ limit: 1000 });
  const properties =
    propertiesData?.items?.map((property) => ({
      id: property.id,
      name: property.name,
    })) || [];

  // Form setup
  const form = useZodForm({
    schema: isEditing ? updateUnitSchema : createUnitSchema,
    defaultValues:
      isEditing && initialData
        ? {
            id: initialData.id,
            property_id: initialData.property_id,
            unit_number: initialData.unit_number,
            unit_type: initialData.unit_type,
            floor: initialData.floor,
            square_footage: initialData.square_footage,
            bedrooms: initialData.bedrooms,
            bathrooms: initialData.bathrooms,
            description: initialData.description,
            rent_amount: initialData.rent_amount,
            deposit_amount: initialData.deposit_amount,
            amenities: initialData.amenities || [],
            images: initialData.images?.map((img) => ({ name: img.name, image: img.url })) || [],
          }
        : {
            property_id: "",
            unit_number: "",
            unit_type: "1br" as const,
            floor: 1,
            square_footage: 0,
            bedrooms: undefined,
            bathrooms: undefined,
            description: "",
            rent_amount: 0,
            deposit_amount: 0,
            amenities: [],
            images: [],
          },
  });

  // Image upload handler
  const handleImageUpload = useCallback(
    (files: File[]) => {
      const newImages = files.map((file) => ({
        name: file.name,
        image: URL.createObjectURL(file), // In real implementation, this would be uploaded to server
      }));

      setUploadedImages((prev) => [...prev, ...newImages]);
      form.setValue("images", [...(form.getValues("images") || []), ...newImages]);
    },
    [form]
  );

  // Remove image handler
  const handleRemoveImage = useCallback(
    (index: number) => {
      setUploadedImages((prev) => prev.filter((_, i) => i !== index));
      const currentImages = form.getValues("images") || [];
      form.setValue(
        "images",
        currentImages.filter((image: any, i: number) => i !== index)
      );
    },
    [form]
  );

  // Form submission
  const onSubmit = async (values: CreateUnitFormValues | UpdateUnitFormValues) => {
    setIsSubmitting(true);

    try {
      if (isEditing && "id" in values) {
        await updateUnitMutation.mutateAsync({ id: values.id, data: values } as any);
        toast.success(t("pages.units.updateSuccess"));
      } else {
        await createUnitMutation.mutateAsync(values as CreateUnitFormValues);
        toast.success(t("pages.units.createSuccess"));
      }

      router.push(authProtectedPaths.UNITS as any);
    } catch (error) {
      toast.error(isEditing ? t("pages.units.updateError") : t("pages.units.createError"));
      // TODO: Implement proper error logging
    } finally {
      setIsSubmitting(false);
    }
  };

  // Cancel handler
  const handleCancel = () => {
    router.push(authProtectedPaths.UNITS as any);
  };

  // Unit type options
  const unitTypeOptions = [
    { value: "studio", label: t("pages.units.types.studio") },
    { value: "1br", label: t("pages.units.types.1br") },
    { value: "2br", label: t("pages.units.types.2br") },
    { value: "3br", label: t("pages.units.types.3br") },
    { value: "commercial", label: t("pages.units.types.commercial") },
    { value: "office", label: t("pages.units.types.office") },
    { value: "retail", label: t("pages.units.types.retail") },
  ];

  // Common amenities
  const commonAmenities = [
    "Air Conditioning",
    "Heating",
    "Dishwasher",
    "Washer/Dryer",
    "Parking",
    "Balcony",
    "Fireplace",
    "Hardwood Floors",
    "Pet Friendly",
    "Pool Access",
    "Gym Access",
    "Elevator",
  ];

  const selectedAmenities = form.watch("amenities") || [];

  const handleAmenityChange = (amenity: string, checked: boolean) => {
    const current = selectedAmenities;
    if (checked) {
      form.setValue("amenities", [...current, amenity]);
    } else {
      form.setValue(
        "amenities",
        current.filter((a: string) => a !== amenity)
      );
    }
  };

  return (
    <div className="flex min-h-full flex-col bg-background">
      {/* Header */}
      <div className="flex-none border-b bg-card">
        <div className="px-6 py-4">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              {isEditing ? t("pages.units.editUnit") : t("pages.units.createUnit")}
            </h1>
            <p className="mt-1 text-sm text-muted-foreground">
              {isEditing ? t("pages.units.editDescription") : t("pages.units.createDescription")}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 p-6">
        <Form {...form}>
          <form id="unit-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Basic Information Card */}
            <Card className="border-border shadow-sm">
              <CardHeader className="border-b border-border bg-background py-3">
                <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
                  <div className="size-2 rounded-full bg-primary"></div>
                  {t("pages.units.basicInformation")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 bg-card p-4">
                {/* Property Selection */}
                <FormField
                  control={form.control}
                  name="property_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-foreground">
                        {t("pages.units.headers.property")}{" "}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger className="h-9 border-input focus:border-primary focus:ring-primary/20">
                            <SelectValue placeholder={t("pages.units.placeholders.property")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {propertiesLoading ? (
                            <SelectItem value="loading" disabled>
                              {t("common.loading")}
                            </SelectItem>
                          ) : properties.length === 0 ? (
                            <SelectItem value="no-properties" disabled>
                              {t("pages.units.noPropertiesAvailable")}
                            </SelectItem>
                          ) : (
                            properties.map((property) => (
                              <SelectItem key={property.id} value={property.id}>
                                {property.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage className="text-destructive" />
                    </FormItem>
                  )}
                />

                {/* Unit Number and Type Row */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="unit_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-foreground">
                          {t("pages.units.headers.unitNumber")}{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("pages.units.placeholders.unitNumber")}
                            className="h-9 border-input focus:border-primary focus:ring-primary/20"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-destructive" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="unit_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-foreground">
                          {t("pages.units.headers.type")}{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger className="h-9 border-input focus:border-primary focus:ring-primary/20">
                              <SelectValue placeholder={t("pages.units.placeholders.type")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {unitTypeOptions.map((option) => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage className="text-destructive" />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Description */}
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-foreground">
                        {t("pages.units.headers.description")}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t("pages.units.placeholders.description")}
                          rows={3}
                          className="resize-none border-input focus:border-primary focus:ring-primary/20"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage className="text-destructive" />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Specifications Card */}
            <Card className="border-border shadow-sm">
              <CardHeader className="border-b border-border bg-background py-3">
                <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
                  <div className="size-2 rounded-full bg-success"></div>
                  {t("pages.units.specifications")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 bg-card p-4">
                {/* Floor and Square Footage Row */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="floor"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-foreground">
                          {t("pages.units.headers.floor")}{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("pages.units.placeholders.floor")}
                            className="h-9 border-input focus:border-primary focus:ring-primary/20"
                            {...field}
                            onChange={(e) =>
                              field.onChange(e.target.value ? Number(e.target.value) : 0)
                            }
                          />
                        </FormControl>
                        <FormMessage className="text-destructive" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="square_footage"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-foreground">
                          {t("pages.units.headers.squareFootage")}{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("pages.units.placeholders.squareFootage")}
                            className="h-9 border-input focus:border-primary focus:ring-primary/20"
                            {...field}
                            onChange={(e) =>
                              field.onChange(e.target.value ? Number(e.target.value) : 0)
                            }
                          />
                        </FormControl>
                        <FormMessage className="text-destructive" />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Bedrooms and Bathrooms Row */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="bedrooms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-foreground">
                          {t("pages.units.headers.bedrooms")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("pages.units.placeholders.bedrooms")}
                            className="h-9 border-input focus:border-primary focus:ring-primary/20"
                            {...field}
                            onChange={(e) =>
                              field.onChange(e.target.value ? Number(e.target.value) : undefined)
                            }
                          />
                        </FormControl>
                        <FormMessage className="text-destructive" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bathrooms"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-foreground">
                          {t("pages.units.headers.bathrooms")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.5"
                            placeholder={t("pages.units.placeholders.bathrooms")}
                            className="h-9 border-input focus:border-primary focus:ring-primary/20"
                            {...field}
                            onChange={(e) =>
                              field.onChange(e.target.value ? Number(e.target.value) : undefined)
                            }
                          />
                        </FormControl>
                        <FormMessage className="text-destructive" />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Financial Information Card */}
            <Card className="border-border shadow-sm">
              <CardHeader className="border-b border-border bg-background py-3">
                <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
                  <div className="size-2 rounded-full bg-secondary-foreground"></div>
                  {t("pages.units.financialInformation")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 bg-card p-4">
                {/* Rent and Deposit Row */}
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="rent_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-foreground">
                          {t("pages.units.headers.rentAmount")}{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("pages.units.placeholders.rentAmount")}
                            className="h-9 border-input focus:border-primary focus:ring-primary/20"
                            {...field}
                            onChange={(e) =>
                              field.onChange(e.target.value ? Number(e.target.value) : 0)
                            }
                          />
                        </FormControl>
                        <FormMessage className="text-destructive" />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="deposit_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-sm font-medium text-foreground">
                          {t("pages.units.headers.depositAmount")}{" "}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("pages.units.placeholders.depositAmount")}
                            className="h-9 border-input focus:border-primary focus:ring-primary/20"
                            {...field}
                            onChange={(e) =>
                              field.onChange(e.target.value ? Number(e.target.value) : 0)
                            }
                          />
                        </FormControl>
                        <FormMessage className="text-destructive" />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Amenities Card */}
            <Card className="border-border shadow-sm">
              <CardHeader className="border-b border-border bg-background py-3">
                <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
                  <div className="size-2 rounded-full bg-warning"></div>
                  {t("pages.units.amenities")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 bg-card p-4">
                <div className="grid grid-cols-1 gap-3 md:grid-cols-3">
                  {commonAmenities.map((amenity) => (
                    <div key={amenity} className="flex items-center space-x-2">
                      <Checkbox
                        id={amenity}
                        checked={selectedAmenities.includes(amenity)}
                        onCheckedChange={(checked) =>
                          handleAmenityChange(amenity, checked as boolean)
                        }
                      />
                      <label
                        htmlFor={amenity}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        {amenity}
                      </label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Images Card */}
            <Card className="border-border shadow-sm">
              <CardHeader className="border-b border-border bg-background py-3">
                <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
                  <div className="size-2 rounded-full bg-accent"></div>
                  {t("pages.units.images")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 bg-card p-4">
                {/* Image Upload */}
                <div className="rounded-lg border-2 border-dashed border-input p-6 transition-colors hover:border-accent-foreground">
                  <div className="text-center">
                    <Upload className="mx-auto size-12 text-muted-foreground" />
                    <div className="mt-4">
                      <label htmlFor="image-upload" className="cursor-pointer">
                        <span className="mt-2 block text-base font-medium text-foreground transition-colors hover:text-accent-foreground">
                          {t("pages.units.placeholders.uploadImages")}
                        </span>
                        <span className="mt-1 block text-sm text-muted-foreground">
                          PNG, JPG, GIF up to 10MB each
                        </span>
                        <input
                          id="image-upload"
                          type="file"
                          multiple
                          accept="image/*"
                          className="hidden"
                          onChange={(e) => {
                            if (e.target.files) {
                              handleImageUpload(Array.from(e.target.files));
                            }
                          }}
                        />
                      </label>
                    </div>
                  </div>
                </div>

                {/* Uploaded Images Preview */}
                {uploadedImages.length > 0 && (
                  <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
                    {uploadedImages.map((image, index) => (
                      <div
                        key={index}
                        className="group relative overflow-hidden rounded-lg border border-border">
                        <img
                          src={image.image}
                          alt={image.name}
                          className="h-32 w-full object-cover"
                        />
                        <button
                          type="button"
                          onClick={() => handleRemoveImage(index)}
                          className="absolute right-2 top-2 rounded-full bg-destructive p-1.5 text-destructive-foreground opacity-0 shadow-lg transition-all hover:bg-destructive/90 group-hover:opacity-100">
                          <X className="size-3" />
                        </button>
                        <div className="50 absolute inset-x-0 bottom-0 bg-black p-2 text-white">
                          <p className="truncate text-xs">{image.name}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>

      {/* Sticky Footer - Following OneX ERP Standard - Full Width */}
      <div className="sticky bottom-0 z-50 w-full flex-none border-t border-border bg-card shadow-lg">
        <div className="mx-auto max-w-7xl px-6 py-4">
          <div className="flex justify-end">
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                className="border-border bg-background px-6 text-foreground hover:bg-muted">
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                form="unit-form"
                className="bg-primary px-6 text-primary-foreground hover:bg-primary-hover disabled:opacity-50">
                {isSubmitting && <Loader2 className="mr-2 size-4 animate-spin" />}
                {isEditing ? t("common.update") : t("common.add")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
