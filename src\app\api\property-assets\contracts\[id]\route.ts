import { NextRequest, NextResponse } from "next/server";

import type { Contract, CreateContract } from "@/features/property-assets/types";

import { contractsMockData } from "../../mock-data";

// GET /api/property-assets/contracts/[id]
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const contract = contractsMockData.find((c) => c.id === id);

    if (!contract) {
      return NextResponse.json({ error: "Contract not found" }, { status: 404 });
    }

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 220));

    return NextResponse.json({
      data: contract,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching contract:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PUT /api/property-assets/contracts/[id]
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    const body: Partial<CreateContract> = await request.json();

    const contractIndex = contractsMockData.findIndex((c) => c.id === id);

    if (contractIndex === -1) {
      return NextResponse.json({ error: "Contract not found" }, { status: 404 });
    }

    // If updating unit_id, check for conflicts with other active contracts
    if (body.unit_id && body.unit_id !== contractsMockData[contractIndex].unit_id) {
      const existingContract = contractsMockData.find(
        (contract) =>
          contract.unit_id === body.unit_id && contract.status === "active" && contract.id !== id
      );

      if (existingContract) {
        return NextResponse.json(
          { error: "Unit is already under another active contract" },
          { status: 409 }
        );
      }
    }

    // Validate date logic if dates are being updated
    if (body.start_date || body.end_date) {
      const startDate = new Date(body.start_date || contractsMockData[contractIndex].start_date);
      const endDate = body.end_date
        ? new Date(body.end_date)
        : contractsMockData[contractIndex].end_date
          ? new Date(contractsMockData[contractIndex].end_date!)
          : null;

      if (endDate && endDate <= startDate) {
        return NextResponse.json({ error: "End date must be after start date" }, { status: 400 });
      }
    }

    // Update contract
    const updatedContract: Contract = {
      ...contractsMockData[contractIndex],
      ...body,
      updated_at: new Date().toISOString(),
    };

    contractsMockData[contractIndex] = updatedContract;

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 450));

    return NextResponse.json({
      data: updatedContract,
      success: true,
      message: "Contract updated successfully",
    });
  } catch (error) {
    console.error("Error updating contract:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE /api/property-assets/contracts/[id]
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const contractIndex = contractsMockData.findIndex((c) => c.id === id);

    if (contractIndex === -1) {
      return NextResponse.json({ error: "Contract not found" }, { status: 404 });
    }

    // In a real app, you might want to check for related payments before deletion
    // For now, we'll just remove the contract
    contractsMockData.splice(contractIndex, 1);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 320));

    return NextResponse.json({
      success: true,
      message: "Contract deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting contract:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
