#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the auth utility
const authUtilPath = path.join(__dirname, 'utils', 'auth.mjs');
const { performLogin } = await import(`file://${authUtilPath}`);

async function testNewRoutes() {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Create screenshots directory
    const screenshotsDir = path.join(__dirname, '..', 'screenshots', 'route-testing');
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir, { recursive: true });
    }

    console.log('🔐 Logging in to the application...');
    await performLogin(page);

    // Test routes directly
    const routesToTest = [
      {
        name: 'Payments',
        url: 'http://localhost:3000/property-assets/payments'
      },
      {
        name: 'Asset Categories', 
        url: 'http://localhost:3000/property-assets/asset-categories'
      },
      {
        name: 'Document Management',
        url: 'http://localhost:3000/property-assets/documents'
      },
      {
        name: 'Property Gallery',
        url: 'http://localhost:3000/property-assets/gallery'
      },
      {
        name: 'Property Valuation',
        url: 'http://localhost:3000/property-assets/valuation'
      }
    ];

    console.log('\n🔍 Testing new routes directly...');
    
    for (let i = 0; i < routesToTest.length; i++) {
      const route = routesToTest[i];
      console.log(`\n📋 Testing: ${route.name} - ${route.url}`);
      
      try {
        // Navigate directly to the route
        await page.goto(route.url, { waitUntil: 'networkidle0', timeout: 15000 });
        
        // Wait a moment for content to load
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Check if page loaded successfully (not redirected to error/login)
        const currentUrl = page.url();
        const title = await page.title();
        
        console.log(`   Current URL: ${currentUrl}`);
        console.log(`   Page Title: ${title}`);
        
        if (currentUrl.includes('/property-assets/') && !currentUrl.includes('/login')) {
          console.log(`   ✅ Route accessible: ${route.name}`);
          
          // Take screenshot
          await page.screenshot({
            path: path.join(screenshotsDir, `${String(i + 1).padStart(2, '0')}-${route.name.toLowerCase().replace(/\s+/g, '-')}-page.png`),
            fullPage: true
          });
          
          console.log(`   📸 Screenshot captured`);
        } else {
          console.log(`   ❌ Route failed or redirected: ${route.name}`);
        }
        
      } catch (error) {
        console.error(`   ❌ Error accessing ${route.name}:`, error.message);
        
        // Take error screenshot
        await page.screenshot({
          path: path.join(screenshotsDir, `error-${route.name.toLowerCase().replace(/\s+/g, '-')}.png`),
          fullPage: true
        });
      }
    }

    console.log('\n🎉 Route testing complete! Screenshots saved to:', screenshotsDir);

    // Generate summary report
    const report = `
# Route Testing Report

## Routes Tested:
${routesToTest.map((route, index) => `${index + 1}. ${route.name} - ${route.url}`).join('\n')}

## Test Results:
Check the screenshots in this directory to see the visual results of each route.

## Test Date: ${new Date().toISOString()}

## Instructions:
1. Review each screenshot to ensure pages load correctly
2. Check for proper component rendering
3. Verify navigation and UI elements work as expected
4. Look for any console errors or broken layouts
    `;

    fs.writeFileSync(path.join(screenshotsDir, 'TEST-REPORT.md'), report.trim());
    console.log('📄 Test report generated: TEST-REPORT.md');

  } catch (error) {
    console.error('❌ Error during route testing:', error);
  } finally {
    await browser.close();
  }
}

// Run the route testing
testNewRoutes().catch(console.error);