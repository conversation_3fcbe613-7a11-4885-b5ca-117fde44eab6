"use client";

import { useMemo, useState } from "react";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/property-assets/components/MaintenanceList/column";
import {
  useDeleteMaintenanceRequest,
  useMaintenanceRequests,
} from "@/features/property-assets/hooks/useMaintenance";
import { MaintenanceRequest } from "@/features/property-assets/types";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function MaintenancePage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { data: maintenanceData, isLoading, isFetching, refetch } = useMaintenanceRequests(options);

  const deleteMaintenanceMutation = useDeleteMaintenanceRequest();

  const maintenance = maintenanceData?.items || [];
  const total = maintenanceData?.total || 0;
  const isTableLoading = isLoading || isFetching;

  const filters = useMemo(
    () => [
      {
        id: "status",
        type: "selectBox" as const,
        title: t("pages.maintenance.filters.status"),
        defaultValue: getInitialParams["status"],
        options: [
          { value: "pending", label: t("pages.maintenance.status.pending") },
          { value: "in_progress", label: t("pages.maintenance.status.inProgress") },
          { value: "completed", label: t("pages.maintenance.status.completed") },
          { value: "cancelled", label: t("pages.maintenance.status.cancelled") },
        ],
      },
      {
        id: "priority",
        type: "selectBox" as const,
        title: t("pages.maintenance.filters.priority"),
        defaultValue: getInitialParams["priority"],
        options: [
          { value: "low", label: t("pages.maintenance.priorities.low") },
          { value: "medium", label: t("pages.maintenance.priorities.medium") },
          { value: "high", label: t("pages.maintenance.priorities.high") },
          { value: "urgent", label: t("pages.maintenance.priorities.urgent") },
        ],
      },
      {
        id: "category",
        type: "selectBox" as const,
        title: t("pages.maintenance.filters.category"),
        defaultValue: getInitialParams["category"],
        options: [
          { value: "plumbing", label: t("pages.maintenance.categories.plumbing") },
          { value: "electrical", label: t("pages.maintenance.categories.electrical") },
          { value: "hvac", label: t("pages.maintenance.categories.hvac") },
          { value: "structural", label: t("pages.maintenance.categories.structural") },
          { value: "appliance", label: t("pages.maintenance.categories.appliance") },
          { value: "cleaning", label: t("pages.maintenance.categories.cleaning") },
          { value: "security", label: t("pages.maintenance.categories.security") },
          { value: "general", label: t("pages.maintenance.categories.general") },
        ],
      },
      {
        id: "property_id",
        type: "selectBox" as const,
        title: t("pages.maintenance.filters.property"),
        defaultValue: getInitialParams["property_id"],
        options: [
          // This would typically be populated from a properties list
          { value: "1", label: "Property 1" },
          { value: "2", label: "Property 2" },
        ],
      },
      {
        id: "assigned_to",
        type: "selectBox" as const,
        title: t("pages.maintenance.filters.assignedTo"),
        defaultValue: getInitialParams["assigned_to"],
        options: [
          // This would typically be populated from staff/vendor list
          { value: "staff", label: "Internal Staff" },
          { value: "vendor", label: "External Vendor" },
        ],
      },
      {
        id: "due_date",
        type: "date" as const,
        title: t("pages.maintenance.filters.dueDate"),
        defaultValue: {
          from: getInitialParams["due_date_from"],
          to: getInitialParams["due_date_to"],
        },
      },
      {
        id: "created_at",
        type: "date" as const,
        title: t("pages.maintenance.headers.createdAt"),
        defaultValue: {
          from: getInitialParams["created_at_from"],
          to: getInitialParams["created_at_to"],
        },
      },
      {
        id: "updated_at",
        type: "date" as const,
        title: t("pages.maintenance.headers.updatedAt"),
        defaultValue: {
          from: getInitialParams["updated_at_from"],
          to: getInitialParams["updated_at_to"],
        },
      },
    ],
    [t, getInitialParams]
  );

  const filterConfig = useMemo(
    () => ({
      showSearch: true,
      filterType: "maintenance",
      searchPlaceHolder: t("pages.maintenance.searchPlaceholder"),
      initialValues: getInitialParams,
      listFilter: filters,
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [t, getInitialParams, filters, handleParamSearch, isTableLoading]
  );

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "dropdown" as const,
        title: t("pages.maintenance.add"),
        icon: PlusIcon,
        items: [
          {
            type: "add_manual",
            title: t("pages.maintenance.actions.addManual"),
            icon: PlusIcon,
            href: authProtectedPaths.MAINTENANCE_NEW,
          },
        ],
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.maintenance.title")}
        filterType="maintenance"
        data={maintenance}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(deleteMaintenanceMutation, isFetching, t)}
        data={maintenance}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          // Bulk delete functionality can be added later
          console.log("Bulk delete selected:", listIndexId);
          handleRestRows();
        }}
      />
    </TableCard>
  );
}
