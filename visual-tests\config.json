{"name": "Visual Test Configuration", "description": "Configuration for automated visual testing of OneX ERP components", "version": "1.0.0", "baseUrl": "http://localhost:3000", "outputDir": "./screenshots", "thresholds": {"similarity": 98, "pixelThreshold": 0.1}, "viewports": {"mobile": {"width": 375, "height": 667, "deviceScaleFactor": 2}, "tablet": {"width": 768, "height": 1024, "deviceScaleFactor": 2}, "desktop": {"width": 1200, "height": 800, "deviceScaleFactor": 1}, "wide": {"width": 1920, "height": 1080, "deviceScaleFactor": 1}}, "browsers": ["chrome", "firefox"], "components": [{"name": "button", "variants": ["default", "primary", "secondary", "outline", "ghost"], "testUrl": "/test-components/button", "viewports": ["mobile", "desktop"], "props": {"default": {}, "primary": {"variant": "primary"}, "secondary": {"variant": "secondary"}, "outline": {"variant": "outline"}, "ghost": {"variant": "ghost"}}}, {"name": "input", "variants": ["default", "error", "disabled"], "testUrl": "/test-components/input", "viewports": ["mobile", "desktop"], "props": {"default": {"placeholder": "Enter text..."}, "error": {"placeholder": "Error state", "error": true}, "disabled": {"placeholder": "Disabled", "disabled": true}}}, {"name": "card", "variants": ["default", "hover", "selected"], "testUrl": "/test-components/card", "viewports": ["mobile", "tablet", "desktop"], "props": {"default": {"title": "Sample Card", "description": "This is a sample card for visual testing"}, "hover": {"title": "Hover State Card", "description": "Card in hover state", "hover": true}, "selected": {"title": "Selected Card", "description": "Card in selected state", "selected": true}}}], "comparisons": [{"name": "button-default-desktop", "baselinePath": "./screenshots/baseline/button-default-desktop.png", "currentPath": "./screenshots/current/button-default-desktop.png", "diffPath": "./screenshots/current/diffs/button-default-diff.png"}, {"name": "button-primary-mobile", "baselinePath": "./screenshots/baseline/button-primary-mobile.png", "currentPath": "./screenshots/current/button-primary-mobile.png", "diffPath": "./screenshots/current/diffs/button-primary-mobile-diff.png"}, {"name": "input-default-desktop", "baselinePath": "./screenshots/baseline/input-default-desktop.png", "currentPath": "./screenshots/current/input-default-desktop.png", "diffPath": "./screenshots/current/diffs/input-default-diff.png"}, {"name": "card-default-tablet", "baselinePath": "./screenshots/baseline/card-default-tablet.png", "currentPath": "./screenshots/current/card-default-tablet.png", "diffPath": "./screenshots/current/diffs/card-default-tablet-diff.png"}], "figmaComparison": {"enabled": true, "designsDir": "./designs/figma-exports", "strictMode": true, "threshold": 95}, "automation": {"waitForSelector": "[data-component-ready='true']", "delay": 500, "retryAttempts": 3, "timeout": 10000}, "reporting": {"outputPath": "./visual-tests/reports", "format": ["json", "html"], "includeImages": true, "generateDiffs": true}}