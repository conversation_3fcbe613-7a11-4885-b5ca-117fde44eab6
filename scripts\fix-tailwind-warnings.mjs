#!/usr/bin/env node
import fs from "fs";
import { glob } from "glob";

// Patterns to replace
const patterns = [
  // Size shorthands
  { from: /className="([^"]*)\bh-(\d+)\s+w-\2\b([^"]*)"/g, to: 'className="$1size-$2$3"' },
  { from: /className='([^']*)\bh-(\d+)\s+w-\2\b([^']*)'/g, to: "className='$1size-$2$3'" },

  // Padding shorthands
  { from: /className="([^"]*)\bpx-(\d+)\s+py-\2\b([^"]*)"/g, to: 'className="$1p-$2$3"' },
  { from: /className='([^']*)\bpx-(\d+)\s+py-\2\b([^']*)'/g, to: "className='$1p-$2$3'" },

  // Inset shorthands
  { from: /className="([^"]*)\bleft-0\s+right-0\b([^"]*)"/g, to: 'className="$1inset-x-0$2"' },
  { from: /className='([^']*)\bleft-0\s+right-0\b([^']*)'/g, to: "className='$1inset-x-0$2'" },

  // Remove transform (not needed in Tailwind v3)
  { from: /className="([^"]*)\btransform\b([^"]*)"/g, to: 'className="$1$2"' },
  { from: /className='([^']*)\btransform\b([^']*)'/g, to: "className='$1$2'" },

  // Update opacity classes
  { from: /className="([^"]*)\bbg-opacity-(\d+)\b([^"]*)"/g, to: 'className="$1$2$3"' },
  { from: /className='([^']*)\bbg-opacity-(\d+)\b([^']*)'/g, to: "className='$1$2$3'" },

  // Fix opacity syntax for modern Tailwind
  {
    from: /className="([^"]*)\bbg-black\s+bg-opacity-(\d+)\b([^"]*)"/g,
    to: 'className="$1bg-black/$2$3"',
  },
  {
    from: /className='([^']*)\bbg-black\s+bg-opacity-(\d+)\b([^']*)'/g,
    to: "className='$1bg-black/$2$3'",
  },

  // Fix group-hover opacity
  {
    from: /className="([^"]*)\bgroup-hover:bg-opacity-(\d+)\b([^"]*)"/g,
    to: 'className="$1group-hover:bg-black/$2$3"',
  },
  {
    from: /className='([^']*)\bgroup-hover:bg-opacity-(\d+)\b([^']*)'/g,
    to: "className='$1group-hover:bg-black/$2$3'",
  },

  // Update flex-shrink-0 to shrink-0
  { from: /className="([^"]*)\bflex-shrink-0\b([^"]*)"/g, to: 'className="$1shrink-0$2"' },
  { from: /className='([^']*)\bflex-shrink-0\b([^']*)'/g, to: "className='$1shrink-0$2'" },
];

async function fixTailwindWarnings() {
  try {
    // Find all TypeScript and TSX files
    const files = await glob("src/**/*.{ts,tsx}", { ignore: ["node_modules/**", ".next/**"] });

    let totalFixed = 0;

    for (const file of files) {
      const content = fs.readFileSync(file, "utf8");
      let newContent = content;
      let fileChanged = false;

      // Apply all patterns
      for (const pattern of patterns) {
        const matches = newContent.match(pattern.from);
        if (matches) {
          newContent = newContent.replace(pattern.from, pattern.to);
          fileChanged = true;
        }
      }

      if (fileChanged) {
        fs.writeFileSync(file, newContent);
        console.log(`✅ Fixed: ${file}`);
        totalFixed++;
      }
    }

    console.log(`\n🎉 Fixed ${totalFixed} files!`);
  } catch (error) {
    console.error("Error fixing Tailwind warnings:", error);
    process.exit(1);
  }
}

fixTailwindWarnings();
