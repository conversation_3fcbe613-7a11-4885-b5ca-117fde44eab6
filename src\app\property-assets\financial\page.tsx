import { Suspense } from "react";
import { Metadata } from "next";
import Loading from "@/app/loading";

import { FinancialDashboard } from "@/features/property-assets/components/Financial";

export const metadata: Metadata = {
  title: "Financial Management | OneX ERP",
  description: "Manage property finances, P&L reports, payments, and revenue calculations",
};

export default function FinancialPage() {
  return (
    <div className="p-6">
      <Suspense
        fallback={
          <div className="flex h-64 items-center justify-center">
            <Loading />
          </div>
        }>
        <FinancialDashboard />
      </Suspense>
    </div>
  );
}
