import { Suspense } from "react";
import { Metadata } from "next";

import { ContractDetailsComponent } from "@/features/property-assets/components/ContractDetails";

export const metadata: Metadata = {
  title: "Contract Details | OneX ERP",
  description:
    "View detailed contract information, terms, revenue calculations, and payment history",
};

interface ContractDetailPageProps {
  params: {
    id: string;
  };
}

export default function ContractDetailPage({ params }: ContractDetailPageProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ContractDetailsComponent contractId={params.id} />
    </Suspense>
  );
}
