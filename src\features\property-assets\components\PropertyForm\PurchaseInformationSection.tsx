"use client";

import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";

interface PurchaseInformationSectionProps {
  form: any;
}

export function PurchaseInformationSection({ form }: PurchaseInformationSectionProps) {
  const { t } = useTranslation();

  return (
    <Card className="border-border shadow-sm">
      <CardHeader className="border-b border-border bg-background py-3">
        <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
          <div className="size-2 rounded-full bg-warning"></div>
          {t("pages.properties.purchaseInformation")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 bg-card p-4">
        {/* <PERSON>urchase Price and Date Row */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <FormField
            control={form.control}
            name="purchase_price"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.purchase.price")}
                </FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder={t("pages.properties.placeholders.purchasePrice")}
                    className="h-9 border-input focus:border-primary focus:ring-ring"
                    {...field}
                    onChange={(e) =>
                      field.onChange(e.target.value ? Number(e.target.value) : undefined)
                    }
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="purchase_date"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-foreground">
                  {t("pages.properties.purchase.date")}
                </FormLabel>
                <FormControl>
                  <Input
                    type="date"
                    className="h-9 border-input focus:border-primary focus:ring-ring"
                    {...field}
                  />
                </FormControl>
                <FormMessage className="text-destructive" />
              </FormItem>
            )}
          />
        </div>
      </CardContent>
    </Card>
  );
}
