import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { tenantApi } from "@/lib/apis/property-assets";

import type { CreateTenant, Tenant } from "../types";
import { tenantKeys, type IGetTenantsParams } from "./keys";

// List Tenants Hook
export const useTenants = (params?: IGetTenantsParams) => {
  return useQuery({
    queryKey: tenantKeys.list(params || {}),
    queryFn: () => tenantApi.list(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Get Tenant by ID Hook
export const useTenant = (id: string) => {
  return useQuery({
    queryKey: tenantKeys.detail(id),
    queryFn: () => tenantApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create Tenant Hook
export const useCreateTenant = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateTenant) => tenantApi.create(data),
    onSuccess: (data) => {
      // Invalidate and refetch tenants list
      queryClient.invalidateQueries({ queryKey: tenantKeys.lists() });

      // Add the new tenant to the cache
      queryClient.setQueryData(tenantKeys.detail(data.data.data.id), data);

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};

// Update Tenant Hook
export const useUpdateTenant = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateTenant> }) =>
      tenantApi.update(id, data),
    onSuccess: (data, variables) => {
      // Invalidate and refetch tenants list
      queryClient.invalidateQueries({ queryKey: tenantKeys.lists() });

      // Update the specific tenant in cache
      queryClient.setQueryData(tenantKeys.detail(variables.id), data);

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};

// Delete Tenant Hook
export const useDeleteTenant = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => tenantApi.delete(id),
    onSuccess: (_, id) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: tenantKeys.detail(id) });

      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: tenantKeys.lists() });

      // Toast success message should be handled in component with translation context
    },
    onError: (error: any) => {
      // Toast error message should be handled in component with translation context
      throw error;
    },
  });
};
