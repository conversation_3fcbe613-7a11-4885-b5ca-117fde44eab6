import { NextRequest, NextResponse } from "next/server";

import { financialSummaryMockData } from "../../mock-data";

export const dynamic = "force-dynamic";

// GET /api/property-assets/financial/summary
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    const property_id = searchParams.get("property_id") || "";

    // If property_id is specified, we would calculate summary for that property
    // For now, return the overall summary
    const summary = { ...financialSummaryMockData };

    // In a real implementation, you would filter by property_id and calculate:
    // - Count units for specific property
    // - Calculate occupancy rates
    // - Sum rental income and expenses
    // For mock data, we'll return the global summary

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 400));

    return NextResponse.json({
      data: summary,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching financial summary:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
