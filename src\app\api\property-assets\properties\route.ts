import { NextRequest, NextResponse } from "next/server";

import type { CreateProperty, Property } from "@/features/property-assets/types";

import { propertiesMockData } from "../mock-data";

// GET /api/property-assets/properties
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = request.nextUrl;
    // Handle both 0-based and 1-based pagination
    const pageParam = parseInt(searchParams.get("page") || "1");
    const page = pageParam === 0 ? 1 : pageParam; // Convert 0-based to 1-based
    const limit = parseInt(searchParams.get("limit") || "10");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") as Property["status"] | null;

    let filteredProperties = propertiesMockData;

    // Apply search filter
    if (search) {
      filteredProperties = filteredProperties.filter(
        (property) =>
          property.name.toLowerCase().includes(search.toLowerCase()) ||
          property.address.street.toLowerCase().includes(search.toLowerCase()) ||
          property.address.city.toLowerCase().includes(search.toLowerCase())
      );
    }

    // Apply status filter
    if (status) {
      filteredProperties = filteredProperties.filter((property) => property.status === status);
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedProperties = filteredProperties.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 300));

    return NextResponse.json({
      data: {
        items: paginatedProperties,
        total: filteredProperties.length,
        page,
        limit,
        totalPages: Math.ceil(filteredProperties.length / limit),
      },
    });
  } catch (error) {
    console.error("Error fetching properties:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST /api/property-assets/properties
export async function POST(request: NextRequest) {
  try {
    const body: CreateProperty = await request.json();

    // Validate required fields
    if (!body.name || !body.address || !body.property_type || !body.owner_name) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Transform images to match Property format
    const transformedImages = body.images?.map((img, index) => ({
      id: `img_${Date.now()}_${index}`,
      name: img.name,
      url: img.image,
      is_primary: index === 0,
    }));

    // Create new property
    const { images: _, layouts: __, ...bodyWithoutImages } = body;
    const newProperty: Property = {
      id: `prop_${Date.now()}`,
      ...bodyWithoutImages,
      images: transformedImages,
      total_units: 0, // Will be updated when units are added
      status: "active",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      company_id: "company1", // Mock company ID
    };

    // Add to mock data (in a real app, this would save to database)
    propertiesMockData.push(newProperty);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 400));

    return NextResponse.json({
      data: newProperty,
      success: true,
      message: "Property created successfully",
    });
  } catch (error) {
    console.error("Error creating property:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
