"use client";

import { useCallback, useState } from "react";
import { useRouter } from "next/navigation";
import {
  AlertTriangle,
  Briefcase,
  Calendar,
  Edit,
  FileText,
  Home,
  Mail,
  Phone,
  Shield,
  Trash2,
  User,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { authProtectedPaths } from "@/constants/paths";

import { useContracts } from "../../hooks/useContracts";
import { useDeleteTenant, useTenant } from "../../hooks/useTenants";
import type { Tenant } from "../../types";

interface TenantDetailsComponentProps {
  tenantId: string;
}

export function TenantDetailsComponent({ tenantId }: TenantDetailsComponentProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);

  const { data: tenant, isLoading, error } = useTenant(tenantId);
  const { data: contractsData } = useContracts({ tenant_id: tenantId, limit: 1000 });
  const contracts = contractsData?.items || [];
  const deleteTenantMutation = useDeleteTenant();

  const handleEdit = useCallback(() => {
    router.push(authProtectedPaths.TENANTS_ID_EDIT.replace(":id", tenantId) as any);
  }, [router, tenantId]);

  const handleDelete = useCallback(async () => {
    try {
      await deleteTenantMutation.mutateAsync(tenantId);
      toast.success(t("tenants.messages.deleteSuccess"));
      router.push(authProtectedPaths.TENANTS as any);
    } catch (error) {
      // TODO: Implement proper error logging
      toast.error(t("tenants.messages.deleteError"));
    }
    setShowDeleteDialog(false);
  }, [deleteTenantMutation, tenantId, t, router]);

  if (isLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-muted-foreground">{t("common.loading")}</div>
      </div>
    );
  }

  if (error || !tenant) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-center">
          <AlertTriangle className="mx-auto mb-4 size-12 text-muted-foreground" />
          <h3 className="mb-2 text-lg font-medium text-foreground">
            {t("tenants.errors.notFound")}
          </h3>
          <p className="mb-4 text-muted-foreground">{t("tenants.errors.notFoundDescription")}</p>
          <Button onClick={() => router.push(authProtectedPaths.TENANTS as any)}>
            {t("common.goBack")}
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: Tenant["status"]) => {
    switch (status) {
      case "active":
        return "bg-success/10 text-success border-success/20";
      case "inactive":
        return "bg-muted text-muted-foreground border-muted";
      case "blacklisted":
        return "bg-destructive/10 text-destructive border-destructive/20";
      default:
        return "bg-muted text-muted-foreground border-muted";
    }
  };

  const getEmploymentStatusColor = (status: Tenant["employment_status"]) => {
    switch (status) {
      case "employed":
        return "bg-primary/10 text-primary border-primary/20";
      case "self_employed":
        return "bg-secondary/10 text-secondary-foreground border-secondary/20";
      case "student":
        return "bg-warning/10 text-warning border-warning/20";
      case "unemployed":
        return "bg-muted text-muted-foreground border-muted";
      case "retired":
        return "bg-accent/10 text-accent-foreground border-accent/20";
      default:
        return "bg-muted text-muted-foreground border-muted";
    }
  };

  const activeContracts = contracts.filter((contract) => contract.status === "active");
  const hasActiveContracts = activeContracts.length > 0;

  return (
    <div className="mx-auto max-w-6xl space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-semibold text-foreground">
            {tenant.first_name} {tenant.last_name}
          </h1>
          <p className="text-sm text-muted-foreground">
            {t("tenants.tenantId")}: {tenant.id}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button onClick={handleEdit} variant="outline">
            <Edit className="mr-2 size-4" />
            {t("common.edit")}
          </Button>
          <Button
            onClick={() => setShowDeleteDialog(true)}
            variant="destructive"
            disabled={hasActiveContracts}>
            <Trash2 className="mr-2 size-4" />
            {t("common.delete")}
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Main Info */}
        <div className="space-y-6 lg:col-span-2">
          {/* Personal Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="size-5" />
                <span>{t("tenants.sections.personalInfo")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("tenants.fields.fullName")}
                  </dt>
                  <dd className="text-foreground">
                    {tenant.first_name} {tenant.last_name}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("tenants.fields.status")}
                  </dt>
                  <dd>
                    <Badge className={getStatusColor(tenant.status)}>
                      {t(`tenants.status.${tenant.status}`)}
                    </Badge>
                  </dd>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="flex items-center space-x-2">
                  <Mail className="size-4 text-muted-foreground" />
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">
                      {t("tenants.fields.email")}
                    </dt>
                    <dd className="text-foreground">{tenant.email}</dd>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="size-4 text-muted-foreground" />
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">
                      {t("tenants.fields.phone")}
                    </dt>
                    <dd className="text-foreground">{tenant.phone}</dd>
                  </div>
                </div>
              </div>

              {tenant.date_of_birth && (
                <div className="flex items-center space-x-2">
                  <Calendar className="size-4 text-muted-foreground" />
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">
                      {t("tenants.fields.dateOfBirth")}
                    </dt>
                    <dd className="text-foreground">
                      {new Date(tenant.date_of_birth).toLocaleDateString()}
                    </dd>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Identification */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="size-5" />
                <span>{t("tenants.sections.identification")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("tenants.fields.identificationType")}
                  </dt>
                  <dd className="text-foreground">
                    {t(`tenants.identificationTypes.${tenant.identification_type}`)}
                  </dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("tenants.fields.identificationNumber")}
                  </dt>
                  <dd className="text-foreground">{tenant.identification_number}</dd>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Employment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Briefcase className="size-5" />
                <span>{t("tenants.sections.employment")}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("tenants.fields.employmentStatus")}
                  </dt>
                  <dd>
                    <Badge className={getEmploymentStatusColor(tenant.employment_status)}>
                      {t(`tenants.employmentStatus.${tenant.employment_status}`)}
                    </Badge>
                  </dd>
                </div>
                {tenant.employer_name && (
                  <div>
                    <dt className="text-sm font-medium text-muted-foreground">
                      {t("tenants.fields.employerName")}
                    </dt>
                    <dd className="text-foreground">{tenant.employer_name}</dd>
                  </div>
                )}
              </div>

              {tenant.monthly_income && (
                <div>
                  <dt className="text-sm font-medium text-muted-foreground">
                    {t("tenants.fields.monthlyIncome")}
                  </dt>
                  <dd className="text-lg font-semibold text-foreground">
                    ${tenant.monthly_income.toLocaleString()}
                  </dd>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Emergency Contact */}
          {(tenant.emergency_contact_name || tenant.emergency_contact_phone) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <AlertTriangle className="size-5" />
                  <span>{t("tenants.sections.emergencyContact")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  {tenant.emergency_contact_name && (
                    <div>
                      <dt className="text-sm font-medium text-muted-foreground">
                        {t("tenants.fields.emergencyContactName")}
                      </dt>
                      <dd className="text-foreground">{tenant.emergency_contact_name}</dd>
                    </div>
                  )}
                  {tenant.emergency_contact_phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="size-4 text-muted-foreground" />
                      <div>
                        <dt className="text-sm font-medium text-muted-foreground">
                          {t("tenants.fields.emergencyContactPhone")}
                        </dt>
                        <dd className="text-foreground">{tenant.emergency_contact_phone}</dd>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{t("tenants.quickStats")}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t("tenants.stats.totalContracts")}</span>
                <span className="font-semibold text-foreground">{contracts.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t("tenants.stats.activeContracts")}</span>
                <span className="font-semibold text-foreground">{activeContracts.length}</span>
              </div>
              <Separator />
              <div className="flex justify-between">
                <span className="text-muted-foreground">{t("tenants.stats.joinDate")}</span>
                <span className="font-semibold text-foreground">
                  {new Date(tenant.created_at).toLocaleDateString()}
                </span>
              </div>
            </CardContent>
          </Card>

          {/* Active Contracts */}
          {activeContracts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Home className="size-5" />
                  <span>{t("tenants.activeContracts")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {activeContracts.map((contract) => (
                  <div
                    key={contract.id}
                    className="flex items-center justify-between rounded-lg bg-muted/50 p-3">
                    <div>
                      <div className="font-medium text-foreground">{contract.property?.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {contract.unit?.unit_number} - ${contract.rent_amount.toLocaleString()}
                        /month
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() =>
                        router.push(
                          authProtectedPaths.CONTRACTS_ID.replace(":id", contract.id) as any
                        )
                      }>
                      <FileText className="size-4" />
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Documents */}
          {tenant.documents && tenant.documents.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="size-5" />
                  <span>{t("tenants.documents")}</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {tenant.documents.map((doc) => (
                  <div
                    key={doc.id}
                    className="flex items-center justify-between rounded bg-muted/30 p-2">
                    <div>
                      <div className="text-sm font-medium text-foreground">{doc.file_name}</div>
                      <div className="text-xs text-muted-foreground">{doc.document_type}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("tenants.deleteConfirmation.title")}</AlertDialogTitle>
            <AlertDialogDescription>
              {hasActiveContracts
                ? t("tenants.deleteConfirmation.hasActiveContracts")
                : t("tenants.deleteConfirmation.description")}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={hasActiveContracts || deleteTenantMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              {deleteTenantMutation.isPending ? t("common.deleting") : t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
