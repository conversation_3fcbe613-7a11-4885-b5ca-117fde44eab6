#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import fs from 'fs/promises';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Import authentication helper
const authPath = join(__dirname, 'utils', 'auth.mjs');
let authenticateUser;

try {
  const authModule = await import(authPath);
  authenticateUser = authModule.authenticateUser;
} catch (error) {
  console.error('❌ Authentication helper not found. Creating basic auth function...');
  
  // Basic auth function fallback
  authenticateUser = async (page) => {
    console.log('🔐 Attempting to authenticate...');
    
    // Navigate to login page
    await page.goto('http://localhost:3000/login');
    await page.waitForTimeout(2000);
    
    // Check if already authenticated
    const currentUrl = page.url();
    if (currentUrl.includes('/dashboard')) {
      console.log('✅ Already authenticated');
      return;
    }
    
    // Try to find login form and fill it
    try {
      await page.waitForSelector('input[type="email"], input[name="email"], input[placeholder*="email"]', { timeout: 5000 });
      await page.type('input[type="email"], input[name="email"], input[placeholder*="email"]', '<EMAIL>');
      
      await page.waitForSelector('input[type="password"], input[name="password"]', { timeout: 2000 });
      await page.type('input[type="password"], input[name="password"]', 'password123');
      
      await page.click('button[type="submit"], button:contains("Sign in"), button:contains("Login")');
      await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 });
      
      console.log('✅ Authentication successful');
    } catch (error) {
      console.log('⚠️ Could not find login form or already authenticated');
    }
  };
}

class LayoutUIValidator {
  constructor() {
    this.browser = null;
    this.page = null;
    this.results = {
      passed: 0,
      failed: 0,
      tests: []
    };
  }

  async init() {
    console.log('🚀 Starting Layout UI Validation...');
    
    this.browser = await puppeteer.launch({
      headless: false, // Set to true for CI
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Set user agent
    await this.page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
  }

  async fallbackAuth() {
    console.log('🔐 Attempting basic navigation...');
    
    // Just try to navigate to different pages directly
    try {
      await this.page.goto('http://localhost:3000', { waitUntil: 'domcontentloaded', timeout: 10000 });
      console.log('✅ Server is responding');
    } catch (error) {
      console.log('⚠️ Server connection issue:', error.message);
    }
  }

  async test(name, testFn) {
    console.log(`🧪 Running test: ${name}`);
    
    try {
      await testFn();
      this.results.passed++;
      this.results.tests.push({ name, status: 'PASSED', error: null });
      console.log(`✅ ${name} - PASSED`);
    } catch (error) {
      this.results.failed++;
      this.results.tests.push({ name, status: 'FAILED', error: error.message });
      console.log(`❌ ${name} - FAILED: ${error.message}`);
    }
  }

  async screenshot(name) {
    const screenshotPath = join(__dirname, '..', 'screenshots', `${name}-${Date.now()}.png`);
    
    // Ensure screenshots directory exists
    try {
      await fs.mkdir(join(__dirname, '..', 'screenshots'), { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
    
    await this.page.screenshot({ 
      path: screenshotPath, 
      fullPage: true 
    });
    
    console.log(`📸 Screenshot saved: ${screenshotPath}`);
    return screenshotPath;
  }

  async navigateToPropertyAssets() {
    // Try the basic component path we know exists
    console.log('🔍 Navigating to property assets page...');
    
    try {
      // Try to access the home page first
      await this.page.goto('http://localhost:3000', { waitUntil: 'domcontentloaded', timeout: 10000 });
      await this.page.waitForTimeout(2000);
      
      console.log(`📍 Current URL: ${this.page.url()}`);
      console.log('✅ Basic navigation working');
      
    } catch (error) {
      throw new Error(`Navigation failed: ${error.message}`);
    }
  }

  async validateLayoutManagementPage() {
    await this.test('Navigate to Layout Management', async () => {
      await this.navigateToPropertyAssets();
      await this.page.waitForTimeout(2000);
    });

    await this.test('Check Page Loads', async () => {
      // Just verify the page loaded without errors
      const title = await this.page.title();
      const bodyText = await this.page.evaluate(() => document.body.textContent);
      
      if (!title || title.includes('Error')) {
        throw new Error('Page failed to load properly');
      }
      
      if (bodyText.length < 100) {
        throw new Error('Page content seems empty');
      }
      
      console.log(`ℹ️ Page title: ${title}`);
    });

    await this.test('Check Basic UI Elements', async () => {
      // Look for common UI elements
      const hasCards = await this.page.$('.card, [class*="card"]');
      const hasButtons = await this.page.$('button');
      const hasText = await this.page.evaluate(() => document.body.textContent.length > 50);
      
      if (!hasCards && !hasButtons && !hasText) {
        throw new Error('No basic UI elements found');
      }
      
      console.log('ℹ️ Basic UI elements detected');
    });

    await this.screenshot('layout-management-overview');
  }

  async validateUnitMappingInterface() {
    await this.test('Check React Components', async () => {
      // Check if React is working by looking for React-specific patterns
      const hasReactElements = await this.page.evaluate(() => {
        // Look for React fibers or React-specific attributes
        return document.querySelector('[data-reactroot], #__next, [data-react]') !== null ||
               window.React !== undefined ||
               document.body.innerHTML.includes('react');
      });
      
      console.log(`ℹ️ React detected: ${hasReactElements}`);
    });

    await this.screenshot('unit-mapping-interface');
  }

  async validateResponsiveDesign() {
    await this.test('Test Mobile Viewport', async () => {
      await this.page.setViewport({ width: 375, height: 667 });
      await this.page.waitForTimeout(1000);
      
      // Check if layout adapts
      const mobileLayout = await this.page.evaluate(() => {
        const elements = document.querySelectorAll('.grid, .flex');
        return Array.from(elements).some(el => 
          window.getComputedStyle(el).gridTemplateColumns?.includes('1fr') ||
          window.getComputedStyle(el).flexDirection === 'column'
        );
      });
      
      if (!mobileLayout) {
        throw new Error('Layout does not appear to be responsive');
      }
      
      await this.screenshot('mobile-responsive');
    });

    await this.test('Test Tablet Viewport', async () => {
      await this.page.setViewport({ width: 768, height: 1024 });
      await this.page.waitForTimeout(1000);
      await this.screenshot('tablet-responsive');
    });

    await this.test('Test Desktop Viewport', async () => {
      await this.page.setViewport({ width: 1920, height: 1080 });
      await this.page.waitForTimeout(1000);
      await this.screenshot('desktop-responsive');
    });
  }

  async validateLoadingStates() {
    await this.test('Check Skeleton Loaders', async () => {
      // Reload page to potentially see loading states
      await this.page.reload({ waitUntil: 'domcontentloaded' });
      
      // Look for skeleton loaders (they might appear briefly)
      try {
        await this.page.waitForSelector('.animate-pulse, [data-testid="skeleton"]', { timeout: 2000 });
        console.log('🎯 Skeleton loaders detected');
      } catch (error) {
        console.log('ℹ️ Skeleton loaders may have loaded too quickly to detect');
      }
      
      // Wait for final content
      await this.page.waitForSelector('.card, [data-testid="content-loaded"]', { timeout: 10000 });
    });
  }

  async validateErrorStates() {
    await this.test('Check Error Handling', async () => {
      // Look for any visible error messages or states
      const errorElements = await this.page.$$('.text-red-500, .text-destructive, [role="alert"], .error');
      
      // This is informational - errors might not be present
      console.log(`ℹ️ Found ${errorElements.length} error-related elements`);
    });
  }

  async validateTranslations() {
    await this.test('Check Text Content', async () => {
      // Look for missing translations (text that looks like keys)
      const textContent = await this.page.evaluate(() => document.body.textContent);
      
      const suspiciousText = [
        'pages.layouts.',
        'common.error.',
        'undefined',
        'null',
        '[object Object]'
      ];
      
      for (const suspicious of suspiciousText) {
        if (textContent.includes(suspicious)) {
          throw new Error(`Potential translation issue found: "${suspicious}"`);
        }
      }
    });
  }

  async generateReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        total: this.results.passed + this.results.failed,
        passed: this.results.passed,
        failed: this.results.failed,
        success_rate: ((this.results.passed / (this.results.passed + this.results.failed)) * 100).toFixed(1)
      },
      tests: this.results.tests,
      environment: {
        url: this.page.url(),
        viewport: await this.page.viewport(),
        userAgent: await this.page.evaluate(() => navigator.userAgent)
      }
    };

    const reportPath = join(__dirname, '..', 'test-reports', `layout-ui-validation-${Date.now()}.json`);
    
    try {
      await fs.mkdir(join(__dirname, '..', 'test-reports'), { recursive: true });
    } catch (error) {
      // Directory might already exist
    }
    
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 VALIDATION REPORT');
    console.log('===================');
    console.log(`✅ Passed: ${report.summary.passed}`);
    console.log(`❌ Failed: ${report.summary.failed}`);
    console.log(`📈 Success Rate: ${report.summary.success_rate}%`);
    console.log(`📄 Report saved: ${reportPath}`);
    
    return report;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async run() {
    try {
      await this.init();
      
      // Authenticate
      if (typeof authenticateUser === 'function') {
        await authenticateUser(this.page);
      } else {
        console.log('⚠️ Using fallback authentication');
        await this.fallbackAuth();
      }
      
      // Run validation tests
      await this.validateLayoutManagementPage();
      await this.validateUnitMappingInterface();
      await this.validateResponsiveDesign();
      await this.validateLoadingStates();
      await this.validateErrorStates();
      await this.validateTranslations();
      
      // Generate final report
      const report = await this.generateReport();
      
      // Exit with appropriate code
      process.exit(report.summary.failed > 0 ? 1 : 0);
      
    } catch (error) {
      console.error('💥 Validation failed:', error);
      await this.screenshot('validation-error');
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new LayoutUIValidator();
  validator.run();
}

export { LayoutUIValidator };