#!/usr/bin/env node

import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

(async () => {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1400, height: 900 }
  });
  const page = await browser.newPage();
  
  try {
    console.log('🚀 Testing layout shape centering...');
    
    // Navigate with authentication
    const success = await navigateWithAuth(page, 'http://localhost:3000/property-assets/layout');
    
    if (!success) {
      throw new Error('Failed to navigate to layout page');
    }
    
    // Wait for the page to fully load
    await page.waitForFunction(() => document.readyState === 'complete');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📸 Taking initial screenshot...');
    await page.screenshot({ 
      path: 'screenshots/layout-centering-test.png',
      fullPage: true 
    });
    
    console.log('✅ Screenshots saved successfully!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await browser.close();
  }
})();