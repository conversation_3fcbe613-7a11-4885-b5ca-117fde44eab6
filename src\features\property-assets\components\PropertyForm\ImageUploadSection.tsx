"use client";

import { useCallback } from "react";
import { Upload, X } from "lucide-react";
import { useTranslation } from "react-i18next";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface ImageUploadSectionProps {
  uploadedImages: { name: string; image: string }[];
  onImageUpload: (files: File[]) => void;
  onRemoveImage: (index: number) => void;
}

export function ImageUploadSection({
  uploadedImages,
  onImageUpload,
  onRemoveImage,
}: ImageUploadSectionProps) {
  const { t } = useTranslation();

  const handleFileUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      if (e.target.files) {
        onImageUpload(Array.from(e.target.files));
      }
    },
    [onImageUpload]
  );

  return (
    <Card className="border-border shadow-sm">
      <CardHeader className="border-b border-border bg-background py-3">
        <CardTitle className="flex items-center gap-2 text-base font-medium text-foreground">
          <div className="size-2 rounded-full bg-accent"></div>
          {t("pages.properties.images")}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4 bg-card p-4">
        {/* Image Upload */}
        <div className="rounded-lg border-2 border-dashed border-input p-6 transition-colors hover:border-accent-foreground">
          <div className="text-center">
            <Upload className="mx-auto size-12 text-muted-foreground" />
            <div className="mt-4">
              <label htmlFor="image-upload" className="cursor-pointer">
                <span className="mt-2 block text-base font-medium text-foreground transition-colors hover:text-accent-foreground">
                  {t("pages.properties.placeholders.uploadImages")}
                </span>
                <span className="mt-1 block text-sm text-muted-foreground">
                  PNG, JPG, GIF up to 10MB each
                </span>
                <input
                  id="image-upload"
                  type="file"
                  multiple
                  accept="image/*"
                  className="hidden"
                  onChange={handleFileUpload}
                />
              </label>
            </div>
          </div>
        </div>

        {/* Uploaded Images Preview */}
        {uploadedImages.length > 0 && (
          <div className="grid grid-cols-2 gap-3 md:grid-cols-4">
            {uploadedImages.map((image, index) => (
              <div
                key={index}
                className="group relative overflow-hidden rounded-lg border border-border">
                <img src={image.image} alt={image.name} className="h-32 w-full object-cover" />
                <button
                  type="button"
                  onClick={() => onRemoveImage(index)}
                  className="absolute right-2 top-2 rounded-full bg-destructive p-1.5 text-destructive-foreground opacity-0 shadow-lg transition-all hover:bg-destructive/90 group-hover:opacity-100">
                  <X className="size-3" />
                </button>
                <div className="50 absolute inset-x-0 bottom-0 bg-black p-2 text-white">
                  <p className="truncate text-xs">{image.name}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
