/**
 * Number formatting utilities with internationalization support
 * Handles Vietnamese and English number abbreviations
 */

interface NumberAbbreviations {
  thousand: string;
  million: string;
  billion: string;
  trillion: string;
}

const abbreviations: Record<string, NumberAbbreviations> = {
  en: {
    thousand: "K",
    million: "M",
    billion: "B",
    trillion: "T",
  },
  vi: {
    thousand: "K",
    million: "tr",
    billion: "tỷ",
    trillion: "nghìn tỷ",
  },
};

/**
 * Formats a number into a short form with locale-specific abbreviations
 * @param value - The number to format
 * @param locale - The locale ('en' or 'vi')
 * @param currency - Whether to show currency symbol
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted string
 */
export function formatNumberShort(
  value: number | undefined | null,
  locale: string = "en",
  currency: boolean = false,
  decimals: number = 1
): string {
  if (value === undefined || value === null || isNaN(value)) {
    return currency ? "$0" : "0";
  }

  const absValue = Math.abs(value);
  const isNegative = value < 0;
  const currencySymbol = currency ? "$" : "";
  const sign = isNegative ? "-" : "";

  const localeKey = locale === "vi" ? "vi" : "en";
  const abbrev = abbreviations[localeKey];

  let formattedValue: string;

  if (absValue >= 1_000_000_000_000) {
    // Trillion
    formattedValue = (absValue / 1_000_000_000_000).toFixed(decimals) + abbrev.trillion;
  } else if (absValue >= 1_000_000_000) {
    // Billion
    formattedValue = (absValue / 1_000_000_000).toFixed(decimals) + abbrev.billion;
  } else if (absValue >= 1_000_000) {
    // Million
    formattedValue = (absValue / 1_000_000).toFixed(decimals) + abbrev.million;
  } else if (absValue >= 1_000) {
    // Thousand
    formattedValue = (absValue / 1_000).toFixed(decimals) + abbrev.thousand;
  } else {
    // Less than thousand - show full number
    formattedValue = absValue.toLocaleString(locale);
  }

  // Remove unnecessary .0 decimals
  formattedValue = formattedValue.replace(/\.0+([KMBTtrỳ]|nghìn tỷ)/, "$1");

  return `${sign}${currencySymbol}${formattedValue}`;
}

/**
 * Formats a percentage value
 * @param value - The percentage value (0.1234 = 12.34%)
 * @param decimals - Number of decimal places (default: 1)
 * @returns Formatted percentage string
 */
export function formatPercentage(value: number | undefined | null, decimals: number = 1): string {
  if (value === undefined || value === null || isNaN(value)) {
    return "0%";
  }

  return `${(value * 100).toFixed(decimals)}%`;
}

/**
 * Formats currency with locale support
 * @param value - The currency value
 * @param locale - The locale ('en' or 'vi')
 * @param currency - Currency code (default: 'USD' for en, 'VND' for vi)
 * @returns Formatted currency string
 */
export function formatCurrency(
  value: number | undefined | null,
  locale: string = "en",
  currency?: string
): string {
  if (value === undefined || value === null || isNaN(value)) {
    return locale === "vi" ? "0 VND" : "$0";
  }

  const currencyCode = currency || (locale === "vi" ? "VND" : "USD");

  try {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  } catch (error) {
    // Fallback if Intl is not supported
    if (locale === "vi") {
      return `${value.toLocaleString()} VND`;
    }
    return `$${value.toLocaleString()}`;
  }
}
