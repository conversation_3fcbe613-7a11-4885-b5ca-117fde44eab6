import { NextRequest, NextResponse } from "next/server";

import type { CreateTenant, Tenant } from "@/features/property-assets/types";

import { tenantsMockData } from "../../mock-data";

// GET /api/property-assets/tenants/[id]
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const tenant = tenantsMockData.find((t) => t.id === id);

    if (!tenant) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 200));

    return NextResponse.json({
      data: tenant,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching tenant:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PUT /api/property-assets/tenants/[id]
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    const body: Partial<CreateTenant> = await request.json();

    const tenantIndex = tenantsMockData.findIndex((t) => t.id === id);

    if (tenantIndex === -1) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    // If updating email, check for duplicates
    if (body.email) {
      const existingTenant = tenantsMockData.find(
        (tenant) => tenant.email.toLowerCase() === body.email!.toLowerCase() && tenant.id !== id
      );

      if (existingTenant) {
        return NextResponse.json({ error: "Email already exists" }, { status: 409 });
      }
    }

    // If updating phone, check for duplicates
    if (body.phone) {
      const existingPhone = tenantsMockData.find(
        (tenant) => tenant.phone === body.phone && tenant.id !== id
      );

      if (existingPhone) {
        return NextResponse.json({ error: "Phone number already exists" }, { status: 409 });
      }
    }

    // Handle document transformation if documents are being updated
    let transformedDocuments;
    if (body.documents) {
      transformedDocuments = body.documents.map((doc, index) => ({
        id: `doc_${Date.now()}_${index}`,
        document_type: doc.document_type as "id" | "income_proof" | "reference" | "other",
        file_name: doc.file_name,
        file_url: doc.file_data,
        uploaded_at: new Date().toISOString(),
      }));
    }

    // Update tenant
    const { documents: _, ...bodyWithoutDocuments } = body;
    const updatedTenant: Tenant = {
      ...tenantsMockData[tenantIndex],
      ...bodyWithoutDocuments,
      ...(transformedDocuments && { documents: transformedDocuments }),
      updated_at: new Date().toISOString(),
    };

    tenantsMockData[tenantIndex] = updatedTenant;

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 400));

    return NextResponse.json({
      data: updatedTenant,
      success: true,
      message: "Tenant updated successfully",
    });
  } catch (error) {
    console.error("Error updating tenant:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE /api/property-assets/tenants/[id]
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const tenantIndex = tenantsMockData.findIndex((t) => t.id === id);

    if (tenantIndex === -1) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    // Check if tenant has active contracts (in a real app, you'd check the database)
    // For now, we'll just remove the tenant
    tenantsMockData.splice(tenantIndex, 1);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 300));

    return NextResponse.json({
      success: true,
      message: "Tenant deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting tenant:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
