#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';
import { navigateWithAuth } from './utils/auth.mjs';

// Configuration
const CONFIG = {
  VIEWPORTS: {
    desktop: { width: 1400, height: 900 }
  },
  SCREENSHOT_DIR: './screenshots/button-validation',
  FORMS: {
    properties: 'http://localhost:3000/property-assets/properties/new',
    products: 'http://localhost:3000/products/new'
  }
};

// Ensure screenshot directory exists
if (!fs.existsSync(CONFIG.SCREENSHOT_DIR)) {
  fs.mkdirSync(CONFIG.SCREENSHOT_DIR, { recursive: true });
}

async function analyzeFormButtons(page, url, formName) {
  console.log(`\n📋 Analyzing ${formName} form buttons...`);
  
  try {
    // Navigate with authentication
    const navigationSuccess = await navigateWithAuth(page, url);
    if (!navigationSuccess) {
      throw new Error(`Failed to navigate to ${url}`);
    }
    
    // Wait for page to fully render
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Scroll to bottom to see buttons
    await page.evaluate(() => {
      window.scrollTo(0, document.body.scrollHeight);
    });
    
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Analyze form buttons
    const buttonData = await page.evaluate(() => {
      const formButtons = Array.from(document.querySelectorAll('button')).filter(btn => {
        const text = btn.textContent?.trim();
        const isFormButton = text === 'Cancel' || text === 'Add' || text === 'Create' || 
                           text === 'Update' || text === 'Save' || btn.type === 'submit' || 
                           btn.form || btn.closest('form');
        
        // Also check for buttons in button containers/form sections
        const inFormSection = btn.closest('form') || 
                             btn.closest('[class*="form"]') ||
                             btn.closest('[class*="submit"]') ||
                             btn.closest('[class*="button"]');
        
        return isFormButton || inFormSection;
      });
      
      return formButtons.map(btn => {
        const rect = btn.getBoundingClientRect();
        const styles = window.getComputedStyle(btn);
        
        return {
          text: btn.textContent?.trim(),
          type: btn.type,
          classes: btn.className,
          visible: btn.offsetWidth > 0 && btn.offsetHeight > 0,
          position: {
            top: rect.top,
            left: rect.left,
            bottom: rect.bottom,
            right: rect.right
          },
          styles: {
            backgroundColor: styles.backgroundColor,
            color: styles.color,
            width: styles.width,
            height: styles.height,
            borderRadius: styles.borderRadius,
            padding: styles.padding,
            margin: styles.margin,
            fontSize: styles.fontSize,
            fontWeight: styles.fontWeight
          },
          parent: {
            classes: btn.parentElement?.className || '',
            tagName: btn.parentElement?.tagName || ''
          }
        };
      });
    });
    
    // Take screenshot of the form bottom area
    const screenshotPath = `${CONFIG.SCREENSHOT_DIR}/${formName}-buttons.png`;
    await page.screenshot({ 
      path: screenshotPath,
      clip: {
        x: 0,
        y: Math.max(0, await page.evaluate(() => document.body.scrollHeight - 400)),
        width: 1400,
        height: 400
      }
    });
    
    console.log(`   📸 Screenshot saved: ${screenshotPath}`);
    console.log(`   🎯 Found ${buttonData.length} buttons`);
    
    return {
      success: true,
      buttons: buttonData,
      screenshot: screenshotPath
    };
    
  } catch (error) {
    console.log(`   ❌ Failed to analyze ${formName}: ${error.message}`);
    return {
      success: false,
      error: error.message,
      buttons: []
    };
  }
}

function compareButtons(propertiesData, productsData) {
  console.log('\n🔍 BUTTON STANDARDS COMPARISON');
  console.log('=' .repeat(60));
  
  if (!propertiesData.success || !productsData.success) {
    console.log('❌ Cannot compare - one or both forms failed to analyze');
    return { standardsCompliant: false, issues: ['Analysis failed'] };
  }
  
  const propertiesButtons = propertiesData.buttons;
  const productsButtons = productsData.buttons;
  
  // Find primary action buttons (Add/Create/Submit)
  const propertiesPrimary = propertiesButtons.find(btn => 
    btn.text === 'Add' || btn.text === 'Create' || btn.type === 'submit' ||
    btn.text?.toLowerCase().includes('save')
  );
  
  const productsPrimary = productsButtons.find(btn => 
    btn.text === 'Add' || btn.text === 'Create' || btn.type === 'submit' ||
    btn.text?.toLowerCase().includes('save')
  );
  
  // Find cancel buttons
  const propertiesCancel = propertiesButtons.find(btn => 
    btn.text?.toLowerCase() === 'cancel'
  );
  
  const productsCancel = productsButtons.find(btn => 
    btn.text?.toLowerCase() === 'cancel'
  );
  
  const issues = [];
  let standardsCompliant = true;
  
  console.log('\n📊 PROPERTIES FORM BUTTONS:');
  propertiesButtons.forEach((btn, i) => {
    if (btn.visible) {
      console.log(`  ${i + 1}. "${btn.text}" (${btn.type})`);
      console.log(`     Background: ${btn.styles.backgroundColor}`);
      console.log(`     Color: ${btn.styles.color}`);
      console.log(`     Size: ${btn.styles.width} x ${btn.styles.height}`);
      console.log('');
    }
  });
  
  console.log('\n📊 PRODUCTS FORM BUTTONS:');
  productsButtons.forEach((btn, i) => {
    if (btn.visible) {
      console.log(`  ${i + 1}. "${btn.text}" (${btn.type})`);
      console.log(`     Background: ${btn.styles.backgroundColor}`);
      console.log(`     Color: ${btn.styles.color}`);
      console.log(`     Size: ${btn.styles.width} x ${btn.styles.height}`);
      console.log('');
    }
  });
  
  // Compare primary buttons
  if (propertiesPrimary && productsPrimary) {
    console.log('\n🔍 PRIMARY BUTTON COMPARISON:');
    
    const bgMatch = propertiesPrimary.styles.backgroundColor === productsPrimary.styles.backgroundColor;
    const colorMatch = propertiesPrimary.styles.color === productsPrimary.styles.color;
    
    console.log(`   Background Colors: ${bgMatch ? '✅' : '❌'}`);
    console.log(`     Properties: ${propertiesPrimary.styles.backgroundColor}`);
    console.log(`     Products: ${productsPrimary.styles.backgroundColor}`);
    
    console.log(`   Text Colors: ${colorMatch ? '✅' : '❌'}`);
    console.log(`     Properties: ${propertiesPrimary.styles.color}`);
    console.log(`     Products: ${productsPrimary.styles.color}`);
    
    if (!bgMatch) {
      issues.push('Primary button background colors do not match');
      standardsCompliant = false;
    }
    
    if (!colorMatch) {
      issues.push('Primary button text colors do not match');
      standardsCompliant = false;
    }
  } else {
    issues.push('Primary button not found in one or both forms');
    standardsCompliant = false;
  }
  
  // Compare cancel buttons
  if (propertiesCancel && productsCancel) {
    console.log('\n🔍 CANCEL BUTTON COMPARISON:');
    
    const cancelBgMatch = propertiesCancel.styles.backgroundColor === productsCancel.styles.backgroundColor;
    const cancelColorMatch = propertiesCancel.styles.color === productsCancel.styles.color;
    
    console.log(`   Background Colors: ${cancelBgMatch ? '✅' : '❌'}`);
    console.log(`     Properties: ${propertiesCancel.styles.backgroundColor}`);
    console.log(`     Products: ${productsCancel.styles.backgroundColor}`);
    
    console.log(`   Text Colors: ${cancelColorMatch ? '✅' : '❌'}`);
    console.log(`     Properties: ${propertiesCancel.styles.color}`);
    console.log(`     Products: ${productsCancel.styles.color}`);
    
    if (!cancelBgMatch) {
      issues.push('Cancel button background colors do not match');
      standardsCompliant = false;
    }
    
    if (!cancelColorMatch) {
      issues.push('Cancel button text colors do not match');
      standardsCompliant = false;
    }
  }
  
  return {
    standardsCompliant,
    issues,
    propertiesPrimary,
    productsPrimary,
    propertiesCancel,
    productsCancel
  };
}

async function validateButtonStandards() {
  console.log('🚀 Starting Button Standards Validation...');
  
  const browser = await puppeteer.launch({
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    await page.setViewport(CONFIG.VIEWPORTS.desktop);
    
    // Analyze both forms
    const propertiesData = await analyzeFormButtons(
      page, 
      CONFIG.FORMS.properties, 
      'Properties'
    );
    
    const productsData = await analyzeFormButtons(
      page, 
      CONFIG.FORMS.products, 
      'Products'
    );
    
    // Compare the buttons
    const comparison = compareButtons(propertiesData, productsData);
    
    // Generate final report
    const report = {
      timestamp: new Date().toISOString(),
      properties: propertiesData,
      products: productsData,
      comparison,
      summary: {
        standardsCompliant: comparison.standardsCompliant,
        totalIssues: comparison.issues.length,
        issues: comparison.issues
      }
    };
    
    // Save detailed report
    const reportPath = `${CONFIG.SCREENSHOT_DIR}/validation-report.json`;
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📋 FINAL SUMMARY');
    console.log('=' .repeat(40));
    console.log(`Standards Compliant: ${comparison.standardsCompliant ? '✅ YES' : '❌ NO'}`);
    console.log(`Total Issues: ${comparison.issues.length}`);
    
    if (comparison.issues.length > 0) {
      console.log('\n🚨 Issues Found:');
      comparison.issues.forEach((issue, i) => {
        console.log(`  ${i + 1}. ${issue}`);
      });
    }
    
    console.log(`\n📄 Detailed report saved: ${reportPath}`);
    
    // Keep browser open briefly for manual inspection
    console.log('\n⏳ Keeping browser open for 8 seconds...');
    await new Promise(resolve => setTimeout(resolve, 8000));
    
  } catch (error) {
    console.error('❌ Validation failed:', error);
  } finally {
    await browser.close();
  }
}

validateButtonStandards().catch(console.error);