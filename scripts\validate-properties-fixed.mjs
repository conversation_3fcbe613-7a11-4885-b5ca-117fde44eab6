import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

async function validatePropertiesFixed() {
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  try {
    const page = await browser.newPage();
    
    console.log('🔐 Authenticating and navigating to properties page...');
    await navigateWithAuth(page, 'http://localhost:3000/property-assets/properties');
    
    // Wait for page to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    console.log('📸 Taking final properties screenshot...');
    await page.screenshot({ 
      path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-fixed-final.png',
      fullPage: true 
    });
    
    // Check for remaining translation issues
    const translationIssues = await page.evaluate(() => {
      const text = document.body.innerText;
      const issues = [];
      
      // Look for specific translation issues we know about
      if (text.includes('pages.properties.add')) issues.push('pages.properties.add still visible');
      if (text.includes('pages.properties.filters.search.placeholder')) issues.push('search placeholder still visible');
      if (text.includes('pages.properties.filters.type')) issues.push('filter type still visible');
      if (text.includes('pages.properties.filters.status')) issues.push('filter status still visible');
      if (text.includes('pages.properties.headers.propertyInfo')) issues.push('propertyInfo header still visible');
      if (text.includes('pages.properties.headers.address')) issues.push('address header still visible');
      if (text.includes('pages.properties.headers.status')) issues.push('status header still visible');
      if (text.includes('pages.properties.headers.updatedAt')) issues.push('updatedAt header still visible');
      
      // Look for any other translation key patterns
      const keyPatterns = [
        /pages\.properties\.\w+/g,
        /common\.\w+/g
      ];
      
      keyPatterns.forEach(pattern => {
        const matches = text.match(pattern);
        if (matches) {
          matches.forEach(match => {
            if (!issues.includes(match)) {
              issues.push(match);
            }
          });
        }
      });
      
      return issues;
    });
    
    console.log('🔍 Translation issues found:', translationIssues);
    
    // Capture specific UI elements to verify translations
    const uiElements = await page.evaluate(() => {
      const elements = {};
      
      // Search input placeholder
      const searchInput = document.querySelector('input[placeholder]');
      if (searchInput) {
        elements.searchPlaceholder = searchInput.placeholder;
      }
      
      // Add button text
      const addButton = document.querySelector('button[class*="add"], button:contains("Add"), [class*="button"]:contains("pages.properties.add")');
      if (addButton) {
        elements.addButtonText = addButton.textContent?.trim();
      }
      
      // Table headers
      const headers = Array.from(document.querySelectorAll('th'))
        .map(th => th.textContent?.trim())
        .filter(text => text && text.length > 0);
      elements.tableHeaders = headers;
      
      // Filter dropdown texts
      const filterElements = Array.from(document.querySelectorAll('[class*="filter"], select'))
        .map(el => el.textContent?.trim())
        .filter(text => text && text.length > 0 && !text.includes('pages.'));
      elements.filterTexts = filterElements.slice(0, 5);
      
      return elements;
    });
    
    console.log('🎯 UI Elements after fix:', uiElements);
    
    // Test mobile responsive
    console.log('📱 Testing mobile responsive design...');
    await page.setViewport({ width: 375, height: 667 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-fixed-mobile.png',
      fullPage: true 
    });
    
    // Test tablet responsive
    console.log('📟 Testing tablet responsive design...');
    await page.setViewport({ width: 768, height: 1024 });
    await new Promise(resolve => setTimeout(resolve, 1000));
    await page.screenshot({ 
      path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/properties-fixed-tablet.png',
      fullPage: true 
    });
    
    console.log('✅ Properties translation fix validation completed!');
    
    return {
      success: true,
      translationIssues,
      uiElements,
      screenshots: [
        'properties-fixed-final.png',
        'properties-fixed-mobile.png',
        'properties-fixed-tablet.png'
      ]
    };
    
  } catch (error) {
    console.error('❌ Error validating properties translation fixes:', error);
    return { success: false, error: error.message };
  } finally {
    await browser.close();
  }
}

// Run validation
validatePropertiesFixed().then(result => {
  if (result.success) {
    console.log('\n🎉 Properties translation fix validation COMPLETED!');
    console.log(`✅ Screenshots captured: ${result.screenshots.length}`);
    
    if (result.translationIssues.length === 0) {
      console.log('✅ NO translation issues found - All fixed!');
    } else {
      console.log('⚠️ Remaining translation issues:');
      result.translationIssues.forEach(issue => {
        console.log(`   - ${issue}`);
      });
    }
    
    console.log('\n📋 UI Elements Analysis:');
    console.log(`   Search Placeholder: ${result.uiElements.searchPlaceholder}`);
    console.log(`   Add Button: ${result.uiElements.addButtonText}`);
    console.log(`   Table Headers: ${result.uiElements.tableHeaders?.join(', ')}`);
    console.log(`   Filter Texts: ${result.uiElements.filterTexts?.join(', ')}`);
    
  } else {
    console.log('\n❌ Properties translation fix validation FAILED!');
    console.log(`Error: ${result.error}`);
  }
}).catch(error => {
  console.error('❌ Script execution failed:', error);
});