"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2, Upload, X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
// Form and validation
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { authProtectedPaths } from "@/constants/paths";

import { useCreateTenant, useUpdateTenant } from "../../hooks/useTenants";
import type { Tenant } from "../../types";
// Tenant specific imports
import {
  createTenantSchema,
  updateTenantSchema,
  type CreateTenantFormValues,
  type UpdateTenantFormValues,
} from "../../utils/validators/tenant";

interface TenantFormProps {
  initialData?: Tenant;
  isEditing?: boolean;
}

export function TenantForm({ initialData, isEditing = false }: TenantFormProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Mutations
  const createTenantMutation = useCreateTenant();
  const updateTenantMutation = useUpdateTenant();

  // Form setup
  const form = useZodForm({
    schema: isEditing ? updateTenantSchema : createTenantSchema,
    defaultValues:
      isEditing && initialData
        ? {
            id: initialData.id,
            first_name: initialData.first_name,
            last_name: initialData.last_name,
            email: initialData.email,
            phone: initialData.phone,
            emergency_contact_name: initialData.emergency_contact_name || "",
            emergency_contact_phone: initialData.emergency_contact_phone || "",
            identification_type: initialData.identification_type,
            identification_number: initialData.identification_number,
            date_of_birth: initialData.date_of_birth || "",
            employment_status: initialData.employment_status,
            employer_name: initialData.employer_name || "",
            monthly_income: initialData.monthly_income || undefined,
          }
        : {
            first_name: "",
            last_name: "",
            email: "",
            phone: "",
            emergency_contact_name: "",
            emergency_contact_phone: "",
            identification_type: "national_id" as const,
            identification_number: "",
            date_of_birth: "",
            employment_status: "employed" as const,
            employer_name: "",
            monthly_income: undefined,
            documents: [],
          },
  });

  const onSubmit = useCallback(
    async (values: CreateTenantFormValues | UpdateTenantFormValues) => {
      if (isSubmitting) return;

      setIsSubmitting(true);
      try {
        if (isEditing && "id" in values) {
          await updateTenantMutation.mutateAsync({ id: values.id, data: values } as any);
          toast.success(t("tenants.messages.updateSuccess"));
          router.push(authProtectedPaths.TENANTS_ID.replace(":id", values.id) as any);
        } else {
          const result = await createTenantMutation.mutateAsync(values as CreateTenantFormValues);
          toast.success(t("tenants.messages.createSuccess"));
          router.push(authProtectedPaths.TENANTS_ID.replace(":id", result.id) as any);
        }
      } catch (error) {
        // TODO: Implement proper error logging
        toast.error(
          isEditing ? t("tenants.messages.updateError") : t("tenants.messages.createError")
        );
      } finally {
        setIsSubmitting(false);
      }
    },
    [isSubmitting, isEditing, updateTenantMutation, createTenantMutation, t, router]
  );

  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <div className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              {isEditing ? t("tenants.editTenant") : t("tenants.createTenant")}
            </h1>
            <p className="text-sm text-muted-foreground">
              {isEditing ? t("tenants.editDescription") : t("tenants.createDescription")}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 px-6 pb-6">
        <Form {...form}>
          <form id="tenant-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("tenants.sections.personalInfo")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="first_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.firstName")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("tenants.placeholders.firstName")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="last_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.lastName")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("tenants.placeholders.lastName")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.email")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder={t("tenants.placeholders.email")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.phone")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("tenants.placeholders.phone")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="date_of_birth"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("tenants.fields.dateOfBirth")}
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="date"
                          className="border-input bg-background text-foreground"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Identification */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("tenants.sections.identification")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="identification_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.identificationType")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue
                                placeholder={t("tenants.placeholders.identificationType")}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="passport">
                              {t("tenants.identificationTypes.passport")}
                            </SelectItem>
                            <SelectItem value="driver_license">
                              {t("tenants.identificationTypes.driverLicense")}
                            </SelectItem>
                            <SelectItem value="national_id">
                              {t("tenants.identificationTypes.nationalId")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="identification_number"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.identificationNumber")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("tenants.placeholders.identificationNumber")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Employment Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("tenants.sections.employment")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={form.control}
                  name="employment_status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("tenants.fields.employmentStatus")}
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className="border-input bg-background text-foreground">
                            <SelectValue placeholder={t("tenants.placeholders.employmentStatus")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="employed">
                            {t("tenants.employmentStatus.employed")}
                          </SelectItem>
                          <SelectItem value="self_employed">
                            {t("tenants.employmentStatus.selfEmployed")}
                          </SelectItem>
                          <SelectItem value="student">
                            {t("tenants.employmentStatus.student")}
                          </SelectItem>
                          <SelectItem value="unemployed">
                            {t("tenants.employmentStatus.unemployed")}
                          </SelectItem>
                          <SelectItem value="retired">
                            {t("tenants.employmentStatus.retired")}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="employer_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.employerName")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("tenants.placeholders.employerName")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="monthly_income"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.monthlyIncome")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder={t("tenants.placeholders.monthlyIncome")}
                            className="border-input bg-background text-foreground"
                            {...field}
                            onChange={(e) =>
                              field.onChange(
                                e.target.value ? parseFloat(e.target.value) : undefined
                              )
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Emergency Contact */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("tenants.sections.emergencyContact")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="emergency_contact_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.emergencyContactName")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("tenants.placeholders.emergencyContactName")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="emergency_contact_phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("tenants.fields.emergencyContactPhone")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder={t("tenants.placeholders.emergencyContactPhone")}
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>

      {/* Sticky Footer - Following OneX ERP Standard - Full Width */}
      <div className="sticky bottom-0 z-50 w-full flex-none border-t bg-card shadow-lg">
        <div className="mx-auto max-w-7xl px-6 py-4">
          <div className="flex justify-end">
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                className="px-6">
                {t("common.cancel")}
              </Button>
              <Button type="submit" disabled={isSubmitting} form="tenant-form" className="px-6">
                {isSubmitting && <Loader2 className="mr-2 size-4 animate-spin" />}
                {isEditing ? t("common.update") : t("common.create")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
