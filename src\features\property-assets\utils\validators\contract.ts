import { z } from "zod";

// Base schema without validation refinements
const baseContractSchema = z.object({
  property_id: z.string().min(1, "validation.propertyRequired"),
  unit_id: z.string().min(1, "validation.unitRequired"),
  tenant_id: z.string().min(1, "validation.tenantRequired"),
  contract_type: z.enum(["monthly", "annual", "profit_sharing", "revenue_sharing"], {
    errorMap: () => ({ message: "validation.contractTypeRequired" }),
  }),
  start_date: z.string().min(1, "validation.startDateRequired"),
  end_date: z.string().optional(),
  rent_amount: z.number().positive("validation.rentAmountMustBePositive"),
  deposit_amount: z.number().min(0, "validation.depositAmountMustBePositive"),
  late_fee_amount: z.number().min(0, "validation.lateFeeAmountMustBePositive").optional(),
  rent_due_day: z.number().min(1, "validation.rentDueDayMin").max(31, "validation.rentDueDayMax"),
  terms_and_conditions: z.string().optional(),
  profit_sharing_percentage: z.number().min(0).max(100, "validation.percentageMax").optional(),
  revenue_sharing_percentage: z.number().min(0).max(100, "validation.percentageMax").optional(),
  auto_renewal: z.boolean().default(false),
  notice_period_days: z.number().min(0, "validation.noticePeriodMustBePositive"),
});

// Create schema with refinements
export const createContractSchema = baseContractSchema.refine(
  (data) => {
    // If contract type is profit_sharing, profit_sharing_percentage is required
    if (data.contract_type === "profit_sharing" && !data.profit_sharing_percentage) {
      return false;
    }
    // If contract type is revenue_sharing, revenue_sharing_percentage is required
    if (data.contract_type === "revenue_sharing" && !data.revenue_sharing_percentage) {
      return false;
    }
    return true;
  },
  {
    message: "validation.sharingPercentageRequired",
    path: ["profit_sharing_percentage", "revenue_sharing_percentage"],
  }
);

// Update schema using base schema (without refinements) to allow .partial()
export const updateContractSchema = baseContractSchema
  .partial()
  .extend({
    id: z.string().min(1, "validation.idRequired"),
  })
  .required({
    id: true,
  })
  .refine(
    (data) => {
      // Same validation for update schema
      if (data.contract_type === "profit_sharing" && !data.profit_sharing_percentage) {
        return false;
      }
      if (data.contract_type === "revenue_sharing" && !data.revenue_sharing_percentage) {
        return false;
      }
      return true;
    },
    {
      message: "validation.sharingPercentageRequired",
      path: ["profit_sharing_percentage", "revenue_sharing_percentage"],
    }
  );

export const contractFilterSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  query: z.string().optional(),
  status: z.enum(["active", "terminated", "expired", "pending"]).optional(),
  contract_type: z.enum(["monthly", "annual", "profit_sharing", "revenue_sharing"]).optional(),
  property_id: z.string().optional(),
  unit_id: z.string().optional(),
});

export type CreateContractFormValues = z.infer<typeof createContractSchema>;
export type UpdateContractFormValues = z.infer<typeof updateContractSchema>;
export type ContractFilterValues = z.infer<typeof contractFilterSchema>;
