#!/usr/bin/env node

/**
 * Visual validation script for Vietnamese property data UI
 * Tests all property management interfaces to ensure Vietnamese data displays correctly
 */

import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

const PROPERTY_PAGES = [
  {
    name: 'Properties List',
    url: 'http://localhost:3000/property-assets',
    expectedElements: [
      '<PERSON><PERSON> phức hợp PRIME COMPLEX',
      'Trung tâm thương mại PARC',
      'Trung tâm thương mại AVIVA',
      'GLAMOURISTA FASHION HUB',
      'Văn phòng PRIME SPACE'
    ]
  },
  {
    name: 'Units Management',
    url: 'http://localhost:3000/property-assets/units',
    expectedElements: [
      '<PERSON><PERSON><PERSON> hộ',
      'Văn phòng',
      'VND'
    ]
  },
  {
    name: 'Tenants Management', 
    url: 'http://localhost:3000/property-assets/tenants',
    expectedElements: [
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>'
    ]
  },
  {
    name: 'Contracts Management',
    url: 'http://localhost:3000/property-assets/contracts',
    expectedElements: [
      '<PERSON><PERSON><PERSON> đồng thuê'
    ]
  }
];

async function validatePage(page, pageConfig) {
  console.log(`\n📋 Testing: ${pageConfig.name}`);
  console.log(`   🔗 URL: ${pageConfig.url}`);

  try {
    // Navigate to the page
    const success = await navigateWithAuth(page, pageConfig.url);
    if (!success) {
      console.log(`   ❌ Failed to navigate to ${pageConfig.name}`);
      return false;
    }

    // Wait for content to load
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Take screenshot
    const screenshotPath = `/Users/<USER>/Desktop/Projects/onex-erp/screenshots/vietnamese-ui-${pageConfig.name.toLowerCase().replace(/\s+/g, '-')}.png`;
    await page.screenshot({ 
      path: screenshotPath, 
      fullPage: true 
    });
    console.log(`   📸 Screenshot saved: ${screenshotPath}`);

    // Check for expected Vietnamese elements
    let foundElements = 0;
    for (const element of pageConfig.expectedElements) {
      try {
        const content = await page.content();
        if (content.includes(element)) {
          console.log(`   ✅ Found: "${element}"`);
          foundElements++;
        } else {
          console.log(`   ⚠️  Not found: "${element}"`);
        }
      } catch (error) {
        console.log(`   ❌ Error checking element "${element}": ${error.message}`);
      }
    }

    const successRate = (foundElements / pageConfig.expectedElements.length) * 100;
    console.log(`   📊 Vietnamese content success rate: ${successRate.toFixed(1)}% (${foundElements}/${pageConfig.expectedElements.length})`);

    return successRate >= 50; // Consider 50%+ success as passing

  } catch (error) {
    console.log(`   ❌ Error validating ${pageConfig.name}: ${error.message}`);
    return false;
  }
}

async function checkPropertyDetail(page) {
  console.log(`\n📋 Testing: Property Detail View`);
  
  try {
    // Navigate to properties list first
    const success = await navigateWithAuth(page, 'http://localhost:3000/property-assets');
    if (!success) {
      return false;
    }

    await new Promise(resolve => setTimeout(resolve, 3000));

    // Look for and click on first property (PRIME COMPLEX)
    try {
      // Try different selectors for property links
      const linkSelectors = [
        'a[href*="property-assets"]',
        '[data-testid*="property"]',
        '.property-item',
        'tr td a',
        'button:has-text("View")',
        'button:has-text("Chi tiết")'
      ];

      let linkFound = false;
      for (const selector of linkSelectors) {
        try {
          await page.waitForSelector(selector, { timeout: 2000 });
          await page.click(selector);
          linkFound = true;
          console.log(`   ✅ Clicked property link using selector: ${selector}`);
          break;
        } catch (e) {
          // Try next selector
        }
      }

      if (linkFound) {
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Take screenshot of detail view
        await page.screenshot({ 
          path: '/Users/<USER>/Desktop/Projects/onex-erp/screenshots/vietnamese-ui-property-detail.png',
          fullPage: true 
        });

        // Check for Vietnamese content in detail view
        const content = await page.content();
        const detailChecks = [
          'PRIME COMPLEX',
          'Đường Nguyễn Huệ',
          'Quận 1',
          'TP.HCM',
          'Vietnam'
        ];

        let found = 0;
        detailChecks.forEach(item => {
          if (content.includes(item)) {
            console.log(`   ✅ Found in detail: "${item}"`);
            found++;
          }
        });

        console.log(`   📊 Detail view success: ${found}/${detailChecks.length} elements found`);
        return found > 0;
      } else {
        console.log(`   ⚠️  No property links found to click`);
        return false;
      }

    } catch (error) {
      console.log(`   ⚠️  Could not access property detail: ${error.message}`);
      return false;
    }

  } catch (error) {
    console.log(`   ❌ Error in property detail check: ${error.message}`);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting Vietnamese Property UI Validation');
  console.log('=' .repeat(60));

  const browser = await puppeteer.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-dev-shm-usage'],
    defaultViewport: { width: 1200, height: 800 }
  });

  try {
    const page = await browser.newPage();
    
    let totalTests = 0;
    let passedTests = 0;

    // Test each property page
    for (const pageConfig of PROPERTY_PAGES) {
      totalTests++;
      const passed = await validatePage(page, pageConfig);
      if (passed) passedTests++;
    }

    // Test property detail view
    totalTests++;
    const detailPassed = await checkPropertyDetail(page);
    if (detailPassed) passedTests++;

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 VIETNAMESE UI VALIDATION SUMMARY');
    console.log('=' .repeat(60));
    console.log(`Total tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Success rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
      console.log('\n🎉 ALL TESTS PASSED! Vietnamese property data displays correctly.');
    } else if (passedTests >= totalTests * 0.7) {
      console.log('\n✅ MOSTLY SUCCESSFUL! Most Vietnamese data displays correctly.');
    } else {
      console.log('\n⚠️  ISSUES DETECTED! Some Vietnamese data may not display properly.');
    }

    console.log(`\n📸 Screenshots saved in: /Users/<USER>/Desktop/Projects/onex-erp/screenshots/`);

  } catch (error) {
    console.error('❌ Validation failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the validation
main().catch(console.error);