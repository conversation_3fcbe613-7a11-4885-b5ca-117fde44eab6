"use client";

import { useMemo, useState } from "react";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/property-assets/components/PropertyList/column";
import { useDeleteProperty, useProperties } from "@/features/property-assets/hooks/property";
import { Property } from "@/features/property-assets/types";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { EFilterType, FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function PropertiesPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { data: propertiesData, isLoading, isFetching, refetch } = useProperties(options);

  const deletePropertyMutation = useDeleteProperty();

  const properties = propertiesData?.data?.items || [];
  const total = propertiesData?.data?.total || 0;

  const isTableLoading = isLoading || isFetching;

  const listFilter: FilterType[] = useMemo(
    () => [
      {
        id: "property_type",
        type: EFilterType.SELECT_BOX,
        title: t("pages.properties.filters.type"),
        defaultValue: getInitialParams["property_type"] as string,
        dataOption: [
          { value: "residential", label: t("pages.properties.types.residential") },
          { value: "commercial", label: t("pages.properties.types.commercial") },
          { value: "mixed", label: t("pages.properties.types.mixed") },
        ],
      },
      {
        id: "status",
        type: EFilterType.SELECT_BOX,
        title: t("pages.properties.filters.status"),
        defaultValue: getInitialParams["status"] as string,
        dataOption: [
          { value: "active", label: t("common.status.active") },
          { value: "inactive", label: t("common.status.inactive") },
        ],
      },
      {
        id: "created_at",
        type: EFilterType.DATE,
        title: t("pages.properties.filters.createdAt"),
        defaultValue: getInitialParams["created_at_from"] as string,
      },
      {
        id: "updated_at",
        type: EFilterType.DATE,
        title: t("pages.properties.filters.updatedAt"),
        defaultValue: getInitialParams["updated_at_from"] as string,
      },
    ],
    [t, getInitialParams]
  );

  const filterConfig: FilterTableProps = useMemo(
    () => ({
      showSearch: true,
      filterType: "properties",
      searchPlaceHolder: t("pages.properties.filters.search.placeholder"),
      initialValues: getInitialParams,
      listFilter,
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [t, getInitialParams, listFilter, handleParamSearch, isTableLoading]
  );

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "dropdown" as const,
        title: t("pages.properties.add"),
        icon: PlusIcon,
        items: [
          {
            type: "add_manual",
            title: t("pages.properties.actions.addManual"),
            icon: PlusIcon,
            href: authProtectedPaths.PROPERTIES_NEW,
          },
        ],
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.properties.title")}
        filterType="properties"
        data={properties}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(deletePropertyMutation, isFetching, t)}
        data={properties}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          // TODO: Implement bulk delete functionality
          // For now, just reset the selected rows
          handleRestRows();
        }}
      />
    </TableCard>
  );
}
