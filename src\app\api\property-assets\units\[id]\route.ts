import { NextRequest, NextResponse } from "next/server";

import type { CreateUnit, Unit } from "@/features/property-assets/types";

import { unitsMockData } from "../../mock-data";

// GET /api/property-assets/units/[id]
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const unit = unitsMockData.find((u) => u.id === id);

    if (!unit) {
      return NextResponse.json({ error: "Unit not found" }, { status: 404 });
    }

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 200));

    return NextResponse.json({
      data: unit,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching unit:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PUT /api/property-assets/units/[id]
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    const body: Partial<CreateUnit> = await request.json();

    const unitIndex = unitsMockData.findIndex((u) => u.id === id);

    if (unitIndex === -1) {
      return NextResponse.json({ error: "Unit not found" }, { status: 404 });
    }

    // If updating unit number, check for duplicates
    if (body.unit_number) {
      const existingUnit = unitsMockData.find(
        (unit) =>
          unit.property_id === unitsMockData[unitIndex].property_id &&
          unit.unit_number === body.unit_number &&
          unit.id !== id
      );

      if (existingUnit) {
        return NextResponse.json(
          { error: "Unit number already exists for this property" },
          { status: 409 }
        );
      }
    }

    // Handle image transformation if images are being updated
    let transformedImages;
    if (body.images) {
      transformedImages = body.images.map((img, index) => ({
        id: `img_${Date.now()}_${index}`,
        name: img.name,
        url: img.image,
        is_primary: index === 0,
      }));
    }

    // Update unit
    const { images: _, ...bodyWithoutImages } = body;
    const updatedUnit: Unit = {
      ...unitsMockData[unitIndex],
      ...bodyWithoutImages,
      ...(transformedImages && { images: transformedImages }),
      updated_at: new Date().toISOString(),
    };

    unitsMockData[unitIndex] = updatedUnit;

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 400));

    return NextResponse.json({
      data: updatedUnit,
      success: true,
      message: "Unit updated successfully",
    });
  } catch (error) {
    console.error("Error updating unit:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE /api/property-assets/units/[id]
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const unitIndex = unitsMockData.findIndex((u) => u.id === id);

    if (unitIndex === -1) {
      return NextResponse.json({ error: "Unit not found" }, { status: 404 });
    }

    // Check if unit has active contracts (in a real app, you'd check the database)
    // For now, we'll just remove the unit
    unitsMockData.splice(unitIndex, 1);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 300));

    return NextResponse.json({
      success: true,
      message: "Unit deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting unit:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
