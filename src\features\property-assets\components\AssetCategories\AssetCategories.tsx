"use client";

import { useState } from "react";
import {
  BarChart3,
  Building,
  Car,
  DollarSign,
  Edit,
  Eye,
  Filter,
  FolderOpen,
  Home,
  Lightbulb,
  MoreHorizontal,
  Plus,
  Search,
  Settings,
  Shield,
  Trash2,
  TreePine,
  TrendingDown,
  TrendingUp,
  Users,
  Wifi,
  Wrench,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Legend,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from "recharts";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Textarea } from "@/components/ui/textarea";

interface AssetCategoriesProps {
  className?: string;
}

export function AssetCategories({ className }: AssetCategoriesProps) {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddCategoryOpen, setIsAddCategoryOpen] = useState(false);
  const [isEditCategoryOpen, setIsEditCategoryOpen] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<any>(null);

  // Mock asset categories data
  const categories = [
    {
      id: "1",
      name: "Residential Units",
      description: "Individual apartment units and living spaces",
      icon: "Home",
      color: "#8884d8",
      parentId: null,
      assetCount: 48,
      totalValue: 3200000,
      avgValue: 66667,
      performanceScore: 85,
      trend: "up",
      trendValue: 12.5,
      isActive: true,
      createdDate: "2023-01-15",
      subcategories: ["Studio", "1-Bedroom", "2-Bedroom", "3-Bedroom"],
    },
    {
      id: "2",
      name: "Common Areas",
      description: "Shared spaces like lobbies, hallways, and amenities",
      icon: "Building",
      color: "#82ca9d",
      parentId: null,
      assetCount: 15,
      totalValue: 850000,
      avgValue: 56667,
      performanceScore: 78,
      trend: "up",
      trendValue: 8.2,
      isActive: true,
      createdDate: "2023-01-15",
      subcategories: ["Lobby", "Hallways", "Gym", "Rooftop"],
    },
    {
      id: "3",
      name: "Parking Spaces",
      description: "Vehicle parking areas and garages",
      icon: "Car",
      color: "#ffc658",
      parentId: null,
      assetCount: 62,
      totalValue: 620000,
      avgValue: 10000,
      performanceScore: 92,
      trend: "up",
      trendValue: 15.3,
      isActive: true,
      createdDate: "2023-02-01",
      subcategories: ["Covered", "Open", "Reserved", "Visitor"],
    },
    {
      id: "4",
      name: "HVAC Systems",
      description: "Heating, ventilation, and air conditioning equipment",
      icon: "Wrench",
      color: "#ff7300",
      parentId: null,
      assetCount: 25,
      totalValue: 450000,
      avgValue: 18000,
      performanceScore: 72,
      trend: "down",
      trendValue: -3.1,
      isActive: true,
      createdDate: "2023-01-20",
      subcategories: ["Central AC", "Heat Pumps", "Boilers", "Ventilation"],
    },
    {
      id: "5",
      name: "Electrical Systems",
      description: "Electrical infrastructure and equipment",
      icon: "Lightbulb",
      color: "#8dd1e1",
      parentId: null,
      assetCount: 18,
      totalValue: 320000,
      avgValue: 17778,
      performanceScore: 88,
      trend: "up",
      trendValue: 6.7,
      isActive: true,
      createdDate: "2023-01-25",
      subcategories: ["Main Panels", "Outlets", "Lighting", "Emergency Systems"],
    },
    {
      id: "6",
      name: "Security Systems",
      description: "Security and surveillance equipment",
      icon: "Shield",
      color: "#d084d0",
      parentId: null,
      assetCount: 12,
      totalValue: 180000,
      avgValue: 15000,
      performanceScore: 94,
      trend: "up",
      trendValue: 18.9,
      isActive: true,
      createdDate: "2023-02-10",
      subcategories: ["Cameras", "Access Control", "Alarms", "Intercoms"],
    },
    {
      id: "7",
      name: "Landscaping",
      description: "Outdoor spaces, gardens, and landscape features",
      icon: "TreePine",
      color: "#00C49F",
      parentId: null,
      assetCount: 8,
      totalValue: 120000,
      avgValue: 15000,
      performanceScore: 81,
      trend: "up",
      trendValue: 4.2,
      isActive: true,
      createdDate: "2023-03-01",
      subcategories: ["Gardens", "Irrigation", "Lighting", "Hardscaping"],
    },
    {
      id: "8",
      name: "Technology Infrastructure",
      description: "Internet, network, and smart building systems",
      icon: "Wifi",
      color: "#FF8042",
      parentId: null,
      assetCount: 22,
      totalValue: 280000,
      avgValue: 12727,
      performanceScore: 89,
      trend: "up",
      trendValue: 22.1,
      isActive: true,
      createdDate: "2023-02-15",
      subcategories: ["Network Equipment", "Smart Devices", "Fiber", "Wifi Systems"],
    },
  ];

  const getIconComponent = (iconName: string) => {
    const iconMap = {
      Home,
      Building,
      Car,
      Wrench,
      Lightbulb,
      Shield,
      TreePine,
      Wifi,
    };
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || FolderOpen;
    return <IconComponent className="size-8" />;
  };

  const getSmallIconComponent = (iconName: string) => {
    const iconMap = {
      Home,
      Building,
      Car,
      Wrench,
      Lightbulb,
      Shield,
      TreePine,
      Wifi,
    };
    const IconComponent = iconMap[iconName as keyof typeof iconMap] || FolderOpen;
    return <IconComponent className="size-4" />;
  };

  const filteredCategories = categories.filter(
    (category) =>
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalAssets = categories.reduce((sum, cat) => sum + cat.assetCount, 0);
  const totalValue = categories.reduce((sum, cat) => sum + cat.totalValue, 0);
  const avgPerformance =
    categories.reduce((sum, cat) => sum + cat.performanceScore, 0) / categories.length;
  const activeCategories = categories.filter((cat) => cat.isActive).length;

  // Data for charts
  const pieChartData = categories.map((cat) => ({
    name: cat.name,
    value: cat.assetCount,
    color: cat.color,
  }));

  const performanceData = categories.map((cat) => ({
    name: cat.name.length > 15 ? cat.name.substring(0, 15) + "..." : cat.name,
    performance: cat.performanceScore,
    assets: cat.assetCount,
  }));

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-xl font-semibold text-foreground">Asset Categories Management</h2>
          <p className="text-sm text-muted-foreground">
            Organize and manage asset categories with performance tracking
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isAddCategoryOpen} onOpenChange={setIsAddCategoryOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 size-4" />
                Add Category
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New Category</DialogTitle>
                <DialogDescription>
                  Add a new asset category to organize your properties
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Category Name</Label>
                  <Input placeholder="Enter category name" />
                </div>
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea placeholder="Describe this category" />
                </div>
                <div>
                  <Label htmlFor="icon">Icon</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an icon" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Home">🏠 Home</SelectItem>
                      <SelectItem value="Building">🏢 Building</SelectItem>
                      <SelectItem value="Car">🚗 Car</SelectItem>
                      <SelectItem value="Wrench">🔧 Wrench</SelectItem>
                      <SelectItem value="Lightbulb">💡 Lightbulb</SelectItem>
                      <SelectItem value="Shield">🛡️ Shield</SelectItem>
                      <SelectItem value="TreePine">🌲 Tree</SelectItem>
                      <SelectItem value="Wifi">📶 Wifi</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="color">Color</Label>
                  <div className="flex space-x-2">
                    {["#8884d8", "#82ca9d", "#ffc658", "#ff7300", "#8dd1e1", "#d084d0"].map(
                      (color) => (
                        <div
                          key={color}
                          className="size-8 cursor-pointer rounded border-2"
                          style={{ backgroundColor: color }}
                        />
                      )
                    )}
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddCategoryOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={() => setIsAddCategoryOpen(false)}>Create Category</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Categories</p>
                <p className="text-2xl font-bold">{categories.length}</p>
                <p className="text-xs text-green-600">{activeCategories} active</p>
              </div>
              <FolderOpen className="size-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Assets</p>
                <p className="text-2xl font-bold">{totalAssets}</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-green-600" />
                  <span className="text-xs text-green-600">+5.2%</span>
                </div>
              </div>
              <Home className="size-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Value</p>
                <p className="text-2xl font-bold">${(totalValue / 1000000).toFixed(1)}M</p>
                <div className="mt-1 flex items-center space-x-1">
                  <TrendingUp className="size-3 text-green-600" />
                  <span className="text-xs text-green-600">+12.8%</span>
                </div>
              </div>
              <DollarSign className="size-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Performance</p>
                <p className="text-2xl font-bold">{avgPerformance.toFixed(0)}%</p>
                <div className="mt-1 flex items-center space-x-1">
                  <BarChart3 className="size-3 text-blue-600" />
                  <span className="text-xs text-blue-600">Score</span>
                </div>
              </div>
              <BarChart3 className="size-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Categories List */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Categories</CardTitle>
                <div className="flex items-center space-x-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 size-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Search categories..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-64 pl-10"
                    />
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredCategories.map((category) => (
                  <Card key={category.id} className="transition-shadow hover:shadow-md">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div
                            className="rounded-lg p-3"
                            style={{
                              backgroundColor: `${category.color}20`,
                              color: category.color,
                            }}>
                            {getIconComponent(category.icon)}
                          </div>
                          <div className="space-y-1">
                            <div className="flex items-center space-x-2">
                              <h4 className="font-medium">{category.name}</h4>
                              <Badge variant={category.isActive ? "default" : "secondary"}>
                                {category.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                            <p className="text-sm text-muted-foreground">{category.description}</p>
                            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                              <span>{category.assetCount} assets</span>
                              <span>${category.totalValue.toLocaleString()}</span>
                              <span>Avg: ${category.avgValue.toLocaleString()}</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <div className="mb-1 flex items-center space-x-1">
                              {category.trend === "up" ? (
                                <TrendingUp className="size-3 text-green-600" />
                              ) : (
                                <TrendingDown className="size-3 text-red-600" />
                              )}
                              <span
                                className={`text-xs ${category.trend === "up" ? "text-green-600" : "text-red-600"}`}>
                                {category.trend === "up" ? "+" : ""}
                                {category.trendValue}%
                              </span>
                            </div>
                            <div className="text-sm font-medium">
                              {category.performanceScore}% Performance
                            </div>
                            <Progress value={category.performanceScore} className="mt-1 h-2 w-20" />
                          </div>

                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="size-8 p-0">
                                <MoreHorizontal className="size-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem
                                onClick={() => {
                                  setSelectedCategory(category);
                                  setIsEditCategoryOpen(true);
                                }}>
                                <Edit className="mr-2 size-4" />
                                Edit Category
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Eye className="mr-2 size-4" />
                                View Assets
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <BarChart3 className="mr-2 size-4" />
                                View Analytics
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 size-4" />
                                Delete Category
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </div>

                      {/* Subcategories */}
                      {category.subcategories.length > 0 && (
                        <div className="mt-3 border-t pt-3">
                          <div className="flex items-center space-x-2">
                            <span className="text-xs text-muted-foreground">Subcategories:</span>
                            {category.subcategories.slice(0, 3).map((sub, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {sub}
                              </Badge>
                            ))}
                            {category.subcategories.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{category.subcategories.length - 3} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Sidebar */}
        <div className="space-y-6">
          {/* Asset Distribution Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Asset Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={pieChartData}
                    cx="50%"
                    cy="50%"
                    outerRadius={60}
                    dataKey="value"
                    label={({ name, percent }) => `${(percent * 100).toFixed(0)}%`}>
                    {pieChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Performance Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Performance Scores</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={250}>
                <BarChart data={performanceData} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" domain={[0, 100]} />
                  <YAxis dataKey="name" type="category" width={80} />
                  <Tooltip />
                  <Bar dataKey="performance" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Category Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Category Statistics</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Highest Performance</span>
                <span className="font-medium">Security Systems (94%)</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Most Assets</span>
                <span className="font-medium">Parking Spaces (62)</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Highest Value</span>
                <span className="font-medium">Residential Units</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Best Growth</span>
                <span className="font-medium">Technology (+22.1%)</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Edit Category Dialog */}
      <Dialog open={isEditCategoryOpen} onOpenChange={setIsEditCategoryOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Category</DialogTitle>
            <DialogDescription>Update category information and settings</DialogDescription>
          </DialogHeader>
          {selectedCategory && (
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Category Name</Label>
                <Input defaultValue={selectedCategory.name} />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea defaultValue={selectedCategory.description} />
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select defaultValue={selectedCategory.isActive ? "active" : "inactive"}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditCategoryOpen(false)}>
              Cancel
            </Button>
            <Button onClick={() => setIsEditCategoryOpen(false)}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
