import { NextRequest, NextResponse } from "next/server";

import type { CreatePayment, Payment } from "@/features/property-assets/types";

import { paymentsMockData } from "../../mock-data";

// GET /api/property-assets/payments/[id]
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const payment = paymentsMockData.find((p) => p.id === id);

    if (!payment) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 180));

    return NextResponse.json({
      data: payment,
      success: true,
    });
  } catch (error) {
    console.error("Error fetching payment:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PUT /api/property-assets/payments/[id]
export async function PUT(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;
    const body: Partial<CreatePayment> = await request.json();

    const paymentIndex = paymentsMockData.findIndex((p) => p.id === id);

    if (paymentIndex === -1) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Validate amount if being updated
    if (body.amount !== undefined && body.amount <= 0) {
      return NextResponse.json({ error: "Payment amount must be positive" }, { status: 400 });
    }

    // Validate payment date if being updated
    if (body.payment_date) {
      const paymentDate = new Date(body.payment_date);
      const today = new Date();
      today.setHours(23, 59, 59, 999); // End of today

      if (paymentDate > today) {
        return NextResponse.json(
          { error: "Payment date cannot be in the future" },
          { status: 400 }
        );
      }
    }

    // Check if payment is already completed and trying to change critical fields
    const currentPayment = paymentsMockData[paymentIndex];
    if (
      currentPayment.status === "completed" &&
      (body.amount !== undefined || body.payment_date !== undefined)
    ) {
      return NextResponse.json(
        { error: "Cannot modify amount or date of completed payment" },
        { status: 400 }
      );
    }

    // Update payment
    const updatedPayment: Payment = {
      ...paymentsMockData[paymentIndex],
      ...body,
      updated_at: new Date().toISOString(),
    };

    paymentsMockData[paymentIndex] = updatedPayment;

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 380));

    return NextResponse.json({
      data: updatedPayment,
      success: true,
      message: "Payment updated successfully",
    });
  } catch (error) {
    console.error("Error updating payment:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// DELETE /api/property-assets/payments/[id]
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id } = params;

    const paymentIndex = paymentsMockData.findIndex((p) => p.id === id);

    if (paymentIndex === -1) {
      return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    // Check if payment is completed (in a real app, you might restrict this)
    const payment = paymentsMockData[paymentIndex];
    if (payment.status === "completed") {
      return NextResponse.json({ error: "Cannot delete completed payment" }, { status: 400 });
    }

    // Remove payment
    paymentsMockData.splice(paymentIndex, 1);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 280));

    return NextResponse.json({
      success: true,
      message: "Payment deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting payment:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
