"use client";

import { use<PERSON><PERSON>back, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
// Form and validation
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  useZodForm,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { authProtectedPaths } from "@/constants/paths";

import { useCreateContract, useUpdateContract } from "../../hooks/useContracts";
import { useProperties } from "../../hooks/useProperties";
import { useTenants } from "../../hooks/useTenants";
import { useUnits } from "../../hooks/useUnits";
import type { Contract } from "../../types";
// Contract specific imports
import {
  createContractSchema,
  updateContractSchema,
  type CreateContractFormValues,
  type UpdateContractFormValues,
} from "../../utils/validators/contract";

interface ContractFormProps {
  initialData?: Contract;
  isEditing?: boolean;
  preselectedPropertyId?: string;
  preselectedUnitId?: string;
  preselectedTenantId?: string;
}

export function ContractForm({
  initialData,
  isEditing = false,
  preselectedPropertyId,
  preselectedUnitId,
  preselectedTenantId,
}: ContractFormProps) {
  const { t } = useTranslation();
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>(
    preselectedPropertyId || initialData?.property_id || ""
  );

  // Data hooks
  const { data: propertiesData, isLoading: isLoadingProperties } = useProperties({ limit: 1000 });
  const { data: unitsData, isLoading: isLoadingUnits } = useUnits(selectedPropertyId, {
    limit: 1000,
  });
  const { data: tenantsData, isLoading: isLoadingTenants } = useTenants({ limit: 1000 });

  // Extract arrays from response data
  const properties = propertiesData?.items || [];
  const units = unitsData?.items || [];
  const tenants = tenantsData?.items || [];

  // Mutations
  const createContractMutation = useCreateContract();
  const updateContractMutation = useUpdateContract();

  // Form setup
  const form = useZodForm({
    schema: isEditing ? updateContractSchema : createContractSchema,
    defaultValues:
      isEditing && initialData
        ? {
            id: initialData.id,
            property_id: initialData.property_id,
            unit_id: initialData.unit_id,
            tenant_id: initialData.tenant_id,
            contract_type: initialData.contract_type,
            start_date: initialData.start_date,
            end_date: initialData.end_date || "",
            rent_amount: initialData.rent_amount,
            deposit_amount: initialData.deposit_amount,
            late_fee_amount: initialData.late_fee_amount || undefined,
            rent_due_day: initialData.rent_due_day,
            terms_and_conditions: initialData.terms_and_conditions || "",
            profit_sharing_percentage: initialData.profit_sharing_percentage || undefined,
            revenue_sharing_percentage: initialData.revenue_sharing_percentage || undefined,
            auto_renewal: initialData.auto_renewal,
            notice_period_days: initialData.notice_period_days,
          }
        : {
            property_id: preselectedPropertyId || "",
            unit_id: preselectedUnitId || "",
            tenant_id: preselectedTenantId || "",
            contract_type: "monthly" as const,
            start_date: "",
            end_date: "",
            rent_amount: 0,
            deposit_amount: 0,
            late_fee_amount: undefined,
            rent_due_day: 1,
            terms_and_conditions: "",
            profit_sharing_percentage: undefined,
            revenue_sharing_percentage: undefined,
            auto_renewal: false,
            notice_period_days: 30,
          },
  });

  const watchedContractType = form.watch("contract_type");
  const watchedPropertyId = form.watch("property_id");

  // Update selectedPropertyId when form value changes
  useEffect(() => {
    if (watchedPropertyId !== selectedPropertyId) {
      setSelectedPropertyId(watchedPropertyId);
      // Reset unit selection when property changes
      if (!isEditing) {
        form.setValue("unit_id", "");
      }
    }
  }, [watchedPropertyId, selectedPropertyId, form, isEditing]);

  // Ensure form values are set properly when editing and data loads
  useEffect(() => {
    if (isEditing && initialData && properties.length > 0) {
      // Verify the property exists in the loaded data
      const propertyExists = properties.some((p) => p.id === initialData.property_id);
      if (propertyExists && form.getValues("property_id") !== initialData.property_id) {
        form.setValue("property_id", initialData.property_id);
      }
    }
  }, [isEditing, initialData, properties, form]);

  // Ensure tenant value is set properly when data loads
  useEffect(() => {
    if (isEditing && initialData && tenants.length > 0) {
      const tenantExists = tenants.some((t) => t.id === initialData.tenant_id);
      if (tenantExists && form.getValues("tenant_id") !== initialData.tenant_id) {
        form.setValue("tenant_id", initialData.tenant_id);
      }
    }
  }, [isEditing, initialData, tenants, form]);

  const onSubmit = useCallback(
    async (values: CreateContractFormValues | UpdateContractFormValues) => {
      if (isSubmitting) return;

      setIsSubmitting(true);
      try {
        if (isEditing && "id" in values) {
          await updateContractMutation.mutateAsync({ id: values.id, data: values } as any);
          toast.success(t("pages.contracts.messages.updateSuccess"));
          router.push(authProtectedPaths.CONTRACTS_ID.replace(":id", values.id) as any);
        } else {
          const result = await createContractMutation.mutateAsync(
            values as CreateContractFormValues
          );
          toast.success(t("pages.contracts.messages.createSuccess"));
          router.push(authProtectedPaths.CONTRACTS_ID.replace(":id", result.id) as any);
        }
      } catch (error) {
        // TODO: Implement proper error logging
        toast.error(
          isEditing
            ? t("pages.contracts.messages.updateError")
            : t("pages.contracts.messages.createError")
        );
      } finally {
        setIsSubmitting(false);
      }
    },
    [isSubmitting, isEditing, updateContractMutation, createContractMutation, t, router]
  );

  // Show loading state while essential data is loading
  if (isLoadingProperties || isLoadingTenants) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              {isEditing ? t("pages.contracts.editContract") : t("pages.contracts.createContract")}
            </h1>
            <p className="text-sm text-muted-foreground">Loading form data...</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <Loader2 className="size-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="flex min-h-screen flex-col">
      {/* Header */}
      <div className="flex-none border-b bg-card">
        <div className="px-6 py-4">
          <div>
            <h1 className="text-2xl font-semibold text-foreground">
              {isEditing ? t("pages.contracts.editContract") : t("pages.contracts.createContract")}
            </h1>
            <p className="mt-1 text-sm text-muted-foreground">
              {isEditing
                ? t("pages.contracts.editDescription")
                : t("pages.contracts.createDescription")}
            </p>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="flex-1 bg-background p-6">
        <Form {...form}>
          <form id="contract-form" onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Property and Unit Selection */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.contracts.sections.propertyUnit")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="property_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.property")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue
                                placeholder={t("pages.contracts.placeholders.property")}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {properties.map((property) => (
                              <SelectItem key={property.id} value={property.id}>
                                {property.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="unit_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.unit")}
                        </FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                          disabled={!selectedPropertyId}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue placeholder={t("pages.contracts.placeholders.unit")} />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {units.map((unit) => (
                              <SelectItem key={unit.id} value={unit.id}>
                                {unit.unit_number} - {unit.unit_type}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="tenant_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("pages.contracts.fields.tenant")}
                      </FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className="border-input bg-background text-foreground">
                            <SelectValue placeholder={t("pages.contracts.placeholders.tenant")} />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {tenants.map((tenant) => (
                            <SelectItem key={tenant.id} value={tenant.id}>
                              {tenant.first_name} {tenant.last_name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Contract Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.contracts.sections.contractDetails")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="contract_type"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.contractType")}
                        </FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger className="border-input bg-background text-foreground">
                              <SelectValue
                                placeholder={t("pages.contracts.placeholders.contractType")}
                              />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="monthly">
                              {t("pages.contracts.contractTypes.monthly")}
                            </SelectItem>
                            <SelectItem value="annual">
                              {t("pages.contracts.contractTypes.annual")}
                            </SelectItem>
                            <SelectItem value="profit_sharing">
                              {t("pages.contracts.contractTypes.profitSharing")}
                            </SelectItem>
                            <SelectItem value="revenue_sharing">
                              {t("pages.contracts.contractTypes.revenueSharing")}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="rent_due_day"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.rentDueDay")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            max="31"
                            placeholder={t("pages.contracts.placeholders.rentDueDay")}
                            className="border-input bg-background text-foreground"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="start_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.startDate")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="end_date"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.endDate")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            className="border-input bg-background text-foreground"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Financial Terms */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.contracts.sections.financialTerms")}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="rent_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.rentAmount")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={t("pages.contracts.placeholders.rentAmount")}
                            className="border-input bg-background text-foreground"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="deposit_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.depositAmount")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={t("pages.contracts.placeholders.depositAmount")}
                            className="border-input bg-background text-foreground"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                  <FormField
                    control={form.control}
                    name="late_fee_amount"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.lateFeeAmount")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder={t("pages.contracts.placeholders.lateFeeAmount")}
                            className="border-input bg-background text-foreground"
                            {...field}
                            onChange={(e) =>
                              field.onChange(
                                e.target.value ? parseFloat(e.target.value) : undefined
                              )
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="notice_period_days"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.noticePeriodDays")}
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            placeholder={t("pages.contracts.placeholders.noticePeriodDays")}
                            className="border-input bg-background text-foreground"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Sharing Percentages - conditional based on contract type */}
                {(watchedContractType === "profit_sharing" ||
                  watchedContractType === "revenue_sharing") && (
                  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                    {watchedContractType === "profit_sharing" && (
                      <FormField
                        control={form.control}
                        name="profit_sharing_percentage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-foreground">
                              {t("pages.contracts.fields.profitSharingPercentage")}
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                step="0.1"
                                placeholder={t(
                                  "pages.contracts.placeholders.profitSharingPercentage"
                                )}
                                className="border-input bg-background text-foreground"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value ? parseFloat(e.target.value) : undefined
                                  )
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}

                    {watchedContractType === "revenue_sharing" && (
                      <FormField
                        control={form.control}
                        name="revenue_sharing_percentage"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-foreground">
                              {t("pages.contracts.fields.revenueSharingPercentage")}
                            </FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                min="0"
                                max="100"
                                step="0.1"
                                placeholder={t(
                                  "pages.contracts.placeholders.revenueSharingPercentage"
                                )}
                                className="border-input bg-background text-foreground"
                                {...field}
                                onChange={(e) =>
                                  field.onChange(
                                    e.target.value ? parseFloat(e.target.value) : undefined
                                  )
                                }
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="auto_renewal"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="border-input"
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel className="text-foreground">
                          {t("pages.contracts.fields.autoRenewal")}
                        </FormLabel>
                        <p className="text-sm text-muted-foreground">
                          {t("pages.contracts.descriptions.autoRenewal")}
                        </p>
                      </div>
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Terms and Conditions */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg font-medium">
                  {t("pages.contracts.sections.termsConditions")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <FormField
                  control={form.control}
                  name="terms_and_conditions"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-foreground">
                        {t("pages.contracts.fields.termsConditions")}
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder={t("pages.contracts.placeholders.termsConditions")}
                          className="min-h-[120px] border-input bg-background text-foreground"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </form>
        </Form>
      </div>

      {/* Sticky Footer - Following OneX ERP Standard - Full Width */}
      <div className="sticky bottom-0 z-50 w-full flex-none border-t border-border bg-card shadow-lg">
        <div className="mx-auto max-w-7xl px-6 py-4">
          <div className="flex justify-end">
            <div className="flex gap-3">
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
                className="border-border bg-background px-6 text-foreground hover:bg-muted">
                {t("common.cancel")}
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                form="contract-form"
                className="bg-primary px-6 text-primary-foreground hover:bg-primary-hover disabled:opacity-50">
                {isSubmitting && <Loader2 className="mr-2 size-4 animate-spin" />}
                {isEditing ? t("common.update") : t("common.create")}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
