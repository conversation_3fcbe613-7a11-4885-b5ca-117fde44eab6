import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Payment Details | OneX ERP",
  description: "View payment transaction details and history",
};

interface PaymentDetailsPageProps {
  params: {
    id: string;
  };
}

export default function PaymentDetailsPage({ params }: PaymentDetailsPageProps) {
  return (
    <div className="container mx-auto py-6">
      <div className="mb-6 flex items-center justify-between">
        <h1 className="text-2xl font-bold text-foreground">Payment Details</h1>
      </div>

      <div className="rounded-lg border bg-card p-6">
        <div className="text-center text-muted-foreground">
          <p>Payment Details - Coming Soon</p>
          <p className="mt-2 text-sm">Payment ID: {params.id}</p>
          <p className="mt-1 text-sm">
            Payment details with transaction information will be available here.
          </p>
        </div>
      </div>
    </div>
  );
}
