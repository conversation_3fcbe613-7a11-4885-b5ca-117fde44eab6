#!/usr/bin/env node

import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Import the auth utility
const authUtilPath = path.join(__dirname, 'utils', 'auth.mjs');
const { performLogin } = await import(`file://${authUtilPath}`);

async function visualReviewSidebar() {
  const browser = await puppeteer.launch({
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Create screenshots directory
    const screenshotsDir = path.join(__dirname, '..', 'screenshots', 'sidebar-review');
    if (!fs.existsSync(screenshotsDir)) {
      fs.mkdirSync(screenshotsDir, { recursive: true });
    }

    console.log('🔐 Logging in to the application...');
    await performLogin(page);

    // Wait for sidebar to be visible
    await page.waitForSelector('[data-sidebar="sidebar"]', { timeout: 10000 });
    
    console.log('📸 Taking initial sidebar screenshot...');
    await page.screenshot({
      path: path.join(screenshotsDir, '01-sidebar-overview.png'),
      fullPage: true
    });

    // Property Assets navigation items to test
    const navigationItems = [
      {
        name: 'Payments',
        url: '/property-assets/payments',
        selector: 'a[href="/property-assets/payments"]'
      },
      {
        name: 'Asset Categories', 
        url: '/property-assets/asset-categories',
        selector: 'a[href="/property-assets/asset-categories"]'
      },
      {
        name: 'Document Management',
        url: '/property-assets/documents', 
        selector: 'a[href="/property-assets/documents"]'
      },
      {
        name: 'Property Gallery',
        url: '/property-assets/gallery',
        selector: 'a[href="/property-assets/gallery"]'
      },
      {
        name: 'Property Valuation',
        url: '/property-assets/valuation',
        selector: 'a[href="/property-assets/valuation"]'
      }
    ];

    console.log('🔍 Testing new sidebar navigation items...');
    
    for (let i = 0; i < navigationItems.length; i++) {
      const item = navigationItems[i];
      console.log(`\n📋 Testing: ${item.name}`);
      
      try {
        // Look for the navigation link in the sidebar
        const navLink = await page.$(item.selector);
        
        if (navLink) {
          console.log(`✅ Found navigation link for ${item.name}`);
          
          // Click the navigation item
          await navLink.click();
          
          // Wait for navigation to complete
          await page.waitForNavigation({ waitUntil: 'networkidle0', timeout: 10000 });
          
          // Check if we're on the correct URL
          const currentUrl = page.url();
          if (currentUrl.includes(item.url)) {
            console.log(`✅ Successfully navigated to ${item.name} page`);
            
            // Wait for content to load
            await page.waitForTimeout(2000);
            
            // Take screenshot
            await page.screenshot({
              path: path.join(screenshotsDir, `${String(i + 2).padStart(2, '0')}-${item.name.toLowerCase().replace(/\s+/g, '-')}.png`),
              fullPage: true
            });
            
            console.log(`📸 Screenshot captured for ${item.name}`);
          } else {
            console.log(`❌ URL mismatch for ${item.name}. Expected: ${item.url}, Got: ${currentUrl}`);
          }
        } else {
          console.log(`❌ Navigation link not found for ${item.name}`);
          // Take screenshot of current state for debugging
          await page.screenshot({
            path: path.join(screenshotsDir, `error-${item.name.toLowerCase().replace(/\s+/g, '-')}-not-found.png`),
            fullPage: true
          });
        }
      } catch (error) {
        console.error(`❌ Error testing ${item.name}:`, error.message);
        
        // Take error screenshot
        await page.screenshot({
          path: path.join(screenshotsDir, `error-${item.name.toLowerCase().replace(/\s+/g, '-')}.png`),
          fullPage: true
        });
      }
    }

    // Test the Property Assets Dashboard as well
    console.log('\n📋 Testing Property Assets Dashboard...');
    try {
      await page.goto('http://localhost:3000/property-assets-dashboard', { waitUntil: 'networkidle0' });
      await page.waitForTimeout(2000);
      
      await page.screenshot({
        path: path.join(screenshotsDir, '07-property-assets-dashboard.png'),
        fullPage: true
      });
      console.log('📸 Property Assets Dashboard screenshot captured');
    } catch (error) {
      console.error('❌ Error accessing Property Assets Dashboard:', error.message);
    }

    console.log('\n🎉 Visual review complete! Screenshots saved to:', screenshotsDir);

    // Generate summary report
    const report = `
# Sidebar Visual Review Report

## Navigation Items Tested:
${navigationItems.map((item, index) => `${index + 1}. ${item.name} - ${item.url}`).join('\n')}

## Screenshots Generated:
- 01-sidebar-overview.png - Full sidebar view
${navigationItems.map((item, index) => `- ${String(index + 2).padStart(2, '0')}-${item.name.toLowerCase().replace(/\s+/g, '-')}.png - ${item.name} page`).join('\n')}
- 07-property-assets-dashboard.png - Property Assets Dashboard

## Review Date: ${new Date().toISOString()}
    `;

    fs.writeFileSync(path.join(screenshotsDir, 'REVIEW-REPORT.md'), report.trim());
    console.log('📄 Review report generated: REVIEW-REPORT.md');

  } catch (error) {
    console.error('❌ Error during visual review:', error);
  } finally {
    await browser.close();
  }
}

// Run the visual review
visualReviewSidebar().catch(console.error);