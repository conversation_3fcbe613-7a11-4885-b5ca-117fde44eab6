"use client";

import { useMemo, useState } from "react";
import { PlusIcon } from "lucide-react";
import { useTranslation } from "react-i18next";

import { columns } from "@/features/property-assets/components/TenantList/column";
import { useDeleteTenant, useTenants } from "@/features/property-assets/hooks/useTenants";
import { Tenant } from "@/features/property-assets/types";

import { TableContainer } from "@/components/custom-table/container/table-container";
import GroupButton, { GroupButtonProps } from "@/components/custom-table/header/group-button";
import TableHeader from "@/components/custom-table/header/table-header";
import useDatatable from "@/components/custom-table/hooks/use-data-table";
import TableCard from "@/components/data-table/data-table-card";
import { FilterTableProps, FilterType } from "@/components/data-table/types";
import { authProtectedPaths } from "@/constants/paths";

export default function TenantsPage() {
  const { t } = useTranslation();
  const { getInitialParams, handleParamSearch } = useDatatable();

  const options = useMemo(
    () => ({ limit: Number(getInitialParams.limit), ...getInitialParams }),
    [getInitialParams]
  );

  const { data: tenantsData, isLoading, isFetching, refetch } = useTenants(options);

  const deleteTenantMutation = useDeleteTenant();

  const tenants = tenantsData?.items || [];
  const total = tenantsData?.total || 0;
  const isTableLoading = isLoading || isFetching;

  const filters = useMemo(
    () => [
      {
        id: "lease_status",
        type: "selectBox" as const,
        title: t("pages.tenants.filters.leaseStatus"),
        defaultValue: getInitialParams["lease_status"],
        options: [
          { value: "active", label: t("pages.tenants.status.active") },
          { value: "inactive", label: t("pages.tenants.status.inactive") },
          { value: "expired", label: t("pages.tenants.status.expired") },
          { value: "pending", label: t("pages.tenants.status.pending") },
        ],
      },
      {
        id: "property_id",
        type: "selectBox" as const,
        title: t("pages.tenants.filters.property"),
        defaultValue: getInitialParams["property_id"],
        options: [
          // This would typically be populated from a properties list
          { value: "1", label: "Property 1" },
          { value: "2", label: "Property 2" },
        ],
      },
      {
        id: "created_at",
        type: "date" as const,
        title: t("pages.tenants.headers.joinDate"),
        defaultValue: {
          from: getInitialParams["created_at_from"],
          to: getInitialParams["created_at_to"],
        },
      },
      {
        id: "updated_at",
        type: "date" as const,
        title: t("pages.tenants.headers.updatedAt"),
        defaultValue: {
          from: getInitialParams["updated_at_from"],
          to: getInitialParams["updated_at_to"],
        },
      },
    ],
    [t, getInitialParams]
  );

  const filterConfig = useMemo(
    () => ({
      showSearch: true,
      filterType: "tenants",
      searchPlaceHolder: t("pages.tenants.searchPlaceholder"),
      initialValues: getInitialParams,
      listFilter: filters,
      handleParamSearch,
      listLoading: isTableLoading,
    }),
    [t, getInitialParams, filters, handleParamSearch, isTableLoading]
  );

  const groupButtonConfig: GroupButtonProps = {
    buttons: [
      {
        type: "dropdown" as const,
        title: t("pages.tenants.add"),
        icon: PlusIcon,
        items: [
          {
            type: "add_manual",
            title: t("pages.tenants.actions.addManual"),
            icon: PlusIcon,
            href: authProtectedPaths.TENANTS_NEW,
          },
        ],
      },
    ],
    onRefresh: () => refetch(),
    isRefreshLoading: isFetching,
  };

  return (
    <TableCard>
      <TableHeader
        title={t("pages.tenants.title")}
        filterType="tenants"
        data={tenants}
        filterProps={filterConfig as FilterTableProps}
        rightComponent={<GroupButton {...groupButtonConfig} />}
      />
      <TableContainer
        columns={columns(deleteTenantMutation, isFetching, t)}
        data={tenants}
        loading={isTableLoading}
        total={total}
        pageSize={Number(getInitialParams.limit)}
        currentPage={Number(getInitialParams.page)}
        onHandleDelete={async (listIndexId: number[], handleRestRows) => {
          // Bulk delete functionality can be added later
          console.log("Bulk delete selected:", listIndexId);
          handleRestRows();
        }}
      />
    </TableCard>
  );
}
