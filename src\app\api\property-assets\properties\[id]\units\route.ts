import { NextRequest, NextResponse } from "next/server";

import type { Unit } from "@/features/property-assets/types";

import { unitsMockData } from "../../../mock-data";

// GET /api/property-assets/properties/[id]/units
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { id: propertyId } = params;
    const { searchParams } = request.nextUrl;
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") as Unit["status"] | null;
    const unit_type = searchParams.get("unit_type") as Unit["unit_type"] | null;

    // Get units for specific property
    let filteredUnits = unitsMockData.filter((unit) => unit.property_id === propertyId);

    // Apply search filter
    if (search) {
      filteredUnits = filteredUnits.filter(
        (unit) =>
          unit.unit_number.toLowerCase().includes(search.toLowerCase()) ||
          unit.unit_type.toLowerCase().includes(search.toLowerCase()) ||
          (unit.description && unit.description.toLowerCase().includes(search.toLowerCase()))
      );
    }

    // Apply status filter
    if (status) {
      filteredUnits = filteredUnits.filter((unit) => unit.status === status);
    }

    // Apply unit type filter
    if (unit_type) {
      filteredUnits = filteredUnits.filter((unit) => unit.unit_type === unit_type);
    }

    // Apply pagination
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedUnits = filteredUnits.slice(startIndex, endIndex);

    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 300));

    return NextResponse.json({
      items: paginatedUnits,
      total: filteredUnits.length,
      page,
      limit,
      totalPages: Math.ceil(filteredUnits.length / limit),
    });
  } catch (error) {
    console.error("Error fetching property units:", error);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
