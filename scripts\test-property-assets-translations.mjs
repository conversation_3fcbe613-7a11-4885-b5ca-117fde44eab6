#!/usr/bin/env node

/**
 * Test Property Assets Translations
 * Verifies that all translations are working correctly in both English and Vietnamese
 */

import puppeteer from 'puppeteer';
import { navigateWithAuth } from './utils/auth.mjs';

const SCREENSHOTS_DIR = 'screenshots/property-assets-translations';
const TEST_URLS = {
  properties: 'http://localhost:3000/property-assets/properties',
  newProperty: 'http://localhost:3000/property-assets/properties/new',
  units: 'http://localhost:3000/property-assets/units',
  newUnit: 'http://localhost:3000/property-assets/units/new',
  contracts: 'http://localhost:3000/property-assets/contracts',
  newContract: 'http://localhost:3000/property-assets/contracts/new'
};

async function switchLanguage(page, language) {
  console.log(`🌐 Switching to ${language}...`);
  
  try {
    // Look for language switcher - common patterns
    const languageSwitcher = await page.$('[data-testid="language-switcher"]') ||
                            await page.$('button[aria-label*="language"]') ||
                            await page.$('button[title*="language"]') ||
                            await page.$('select[name="language"]') ||
                            await page.$('.language-selector');
    
    if (languageSwitcher) {
      await languageSwitcher.click();
      await page.waitForTimeout(1000);
      
      // Try to find language option
      const langOption = await page.$(`[data-value="${language}"]`) ||
                        await page.$(`option[value="${language}"]`) ||
                        await page.$(`button:has-text("${language === 'vi' ? 'Vietnamese' : 'English'}")`);
      
      if (langOption) {
        await langOption.click();
        await page.waitForTimeout(2000);
        console.log(`   ✅ Switched to ${language}`);
        return true;
      }
    }
    
    // Alternative: try URL parameter
    const currentUrl = page.url();
    const url = new URL(currentUrl);
    url.searchParams.set('lang', language);
    await page.goto(url.toString(), { waitUntil: 'networkidle2' });
    
    console.log(`   ✅ Switched to ${language} via URL parameter`);
    return true;
  } catch (error) {
    console.log(`   ⚠️ Could not switch language: ${error.message}`);
    return false;
  }
}

async function takeScreenshot(page, name, language) {
  const filename = `${SCREENSHOTS_DIR}/${language}/${name}.png`;
  await page.screenshot({ 
    path: filename, 
    fullPage: true,
    captureBeyondViewport: false 
  });
  console.log(`   📸 Screenshot saved: ${filename}`);
}

async function testTranslations() {
  console.log('🎯 Testing Property Assets Translations');
  console.log('=====================================\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 }
  });
  
  try {
    const page = await browser.newPage();
    
    // Test both languages
    for (const language of ['en', 'vi']) {
      console.log(`\n🌐 Testing ${language.toUpperCase()} translations:`);
      console.log('─'.repeat(40));
      
      // Navigate to dashboard first and switch language
      const dashboardSuccess = await navigateWithAuth(page, 'http://localhost:3000/dashboard');
      if (!dashboardSuccess) {
        console.log('❌ Failed to access dashboard');
        continue;
      }
      
      await switchLanguage(page, language);
      
      // Test each property-assets page
      for (const [pageName, url] of Object.entries(TEST_URLS)) {
        console.log(`\n📍 Testing ${pageName}:`);
        
        try {
          await page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
          await page.waitForTimeout(2000); // Wait for content to load
          
          // Check for key translation elements
          const translationChecks = {
            hasPageTitle: await page.$('h1, h2, [data-testid="page-title"]'),
            hasButtons: await page.$$('button'),
            hasLabels: await page.$$('label'),
            hasPlaceholders: await page.$$('input[placeholder], textarea[placeholder]')
          };
          
          console.log(`   📊 Elements found:`);
          console.log(`      - Page title: ${translationChecks.hasPageTitle ? '✅' : '❌'}`);
          console.log(`      - Buttons: ${translationChecks.hasButtons.length}`);
          console.log(`      - Labels: ${translationChecks.hasLabels.length}`);
          console.log(`      - Placeholders: ${translationChecks.hasPlaceholders.length}`);
          
          // Take screenshot
          await takeScreenshot(page, pageName, language);
          
          // Check for untranslated content (hardcoded English in Vietnamese mode)
          if (language === 'vi') {
            const pageText = await page.evaluate(() => document.body.innerText);
            const englishPatterns = [
              /\bProperties\b/, /\bUnits\b/, /\bContracts\b/,
              /\bEdit\b/, /\bDelete\b/, /\bAdd\b/, /\bUpdate\b/,
              /\bCreate Property\b/, /\bCreate Unit\b/, /\bCreate Contract\b/
            ];
            
            const untranslatedFound = englishPatterns.some(pattern => pattern.test(pageText));
            if (untranslatedFound) {
              console.log(`   ⚠️ Possible untranslated content detected`);
            } else {
              console.log(`   ✅ No obvious untranslated content found`);
            }
          }
          
        } catch (error) {
          console.log(`   ❌ Error testing ${pageName}: ${error.message}`);
        }
      }
    }
    
    console.log('\n🎉 Translation testing completed!');
    console.log(`📁 Screenshots saved in: ${SCREENSHOTS_DIR}/`);
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Create screenshots directory
import { mkdirSync } from 'fs';
try {
  mkdirSync(`${SCREENSHOTS_DIR}/en`, { recursive: true });
  mkdirSync(`${SCREENSHOTS_DIR}/vi`, { recursive: true });
} catch (error) {
  // Directory already exists
}

// Run the test
testTranslations().catch(console.error);