#!/usr/bin/env node

/**
 * Component-Specific UI Validation Script
 * Tests individual Property Assets components with authentication
 */

import { mkdir, writeFile } from 'fs/promises';
import { join } from 'path';
import { initializePuppeteer } from './puppeteer-capture.mjs';

// Test configuration
const BASE_URL = 'http://localhost:3001';
const TEST_OUTPUT_DIR = 'screenshots/component-validation';
const NAVIGATION_TIMEOUT = 10000;
const LOAD_TIMEOUT = 15000;

// Authentication configuration
const LOGIN_CREDENTIALS = {
  username: 'onexapis_admin',
  password: 'Admin@123'
};

const LOGIN_URL = `${BASE_URL}/login`;
const DASHBOARD_URL = `${BASE_URL}/dashboard`;

/**
 * Component test configurations
 */
const COMPONENT_TESTS = {
  'units': {
    name: 'UnitList',
    url: '/property-assets/units',
    title: 'Units Management',
    description: 'Unit listing with table, search, and filters',
    waitForSelector: '[data-testid="table-container"], .table-container, table',
    tests: [
      {
        name: 'Page Load',
        description: 'Page loads without errors and displays content'
      },
      {
        name: 'Table Structure',
        description: 'Table displays with proper headers and columns'
      },
      {
        name: 'Search Functionality', 
        description: 'Search input is accessible and functional'
      },
      {
        name: 'Filter Options',
        description: 'Filter dropdowns are present and working'
      },
      {
        name: 'Add Button',
        description: 'Add Unit button is present and clickable'
      }
    ]
  },
  'properties': {
    name: 'PropertyList',
    url: '/property-assets/properties',
    title: 'Properties Management',
    description: 'Property listing with table, search, and filters',
    waitForSelector: '[data-testid="table-container"], .table-container, table',
    tests: [
      {
        name: 'Page Load',
        description: 'Page loads without errors and displays content'
      },
      {
        name: 'Table Structure',
        description: 'Table displays with proper headers and columns'
      },
      {
        name: 'Search Functionality',
        description: 'Search input is accessible and functional'
      },
      {
        name: 'Filter Options',
        description: 'Filter dropdowns are present and working'
      },
      {
        name: 'Add Button',
        description: 'Add Property button is present and clickable'
      }
    ]
  },
  'tenants': {
    name: 'TenantList',
    url: '/property-assets/tenants',
    title: 'Tenants Management',
    description: 'Tenant listing with table, search, and filters',
    waitForSelector: '[data-testid="table-container"], .table-container, table',
    tests: [
      {
        name: 'Page Load',
        description: 'Page loads without errors and displays content'
      },
      {
        name: 'Table Structure',
        description: 'Table displays with proper headers and columns'
      },
      {
        name: 'Search Functionality',
        description: 'Search input is accessible and functional'
      },
      {
        name: 'Filter Options',
        description: 'Filter dropdowns are present and working'
      },
      {
        name: 'Add Button',
        description: 'Add Tenant button is present and clickable'
      }
    ]
  },
  'contracts': {
    name: 'ContractList',
    url: '/property-assets/contracts',
    title: 'Contracts Management',
    description: 'Contract listing with table, search, and filters',
    waitForSelector: '[data-testid="table-container"], .table-container, table',
    tests: [
      {
        name: 'Page Load',
        description: 'Page loads without errors and displays content'
      },
      {
        name: 'Table Structure',
        description: 'Table displays with proper headers and columns'
      },
      {
        name: 'Search Functionality',
        description: 'Search input is accessible and functional'
      },
      {
        name: 'Filter Options',
        description: 'Filter dropdowns are present and working'
      },
      {
        name: 'Add Button',
        description: 'Add Contract button is present and clickable'
      }
    ]
  },
  'maintenance': {
    name: 'MaintenanceList',
    url: '/property-assets/maintenance',
    title: 'Maintenance Management',
    description: 'Maintenance request listing with table, search, and filters',
    waitForSelector: '[data-testid="table-container"], .table-container, table',
    tests: [
      {
        name: 'Page Load',
        description: 'Page loads without errors and displays content'
      },
      {
        name: 'Table Structure',
        description: 'Table displays with proper headers and columns'
      },
      {
        name: 'Search Functionality',
        description: 'Search input is accessible and functional'
      },
      {
        name: 'Filter Options',
        description: 'Filter dropdowns are present and working'
      },
      {
        name: 'Add Button',
        description: 'New Request button is present and clickable'
      }
    ]
  }
};

/**
 * Viewport configurations for responsive testing
 */
const TEST_VIEWPORTS = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  desktop: { width: 1200, height: 800 }
};

/**
 * Perform login and return authenticated page
 */
async function performLogin(page) {
  console.log('🔐 Performing authentication...');
  
  try {
    // Navigate to login page
    await page.goto(LOGIN_URL, { waitUntil: 'networkidle2', timeout: LOAD_TIMEOUT });
    
    // Additional wait for dynamic content
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Find username input with flexible selectors
    const usernameSelectors = ['input[name="username"]', 'input[placeholder*="username"]', 'input[placeholder*="email"]'];
    let usernameInput = null;
    
    for (const selector of usernameSelectors) {
      try {
        await page.waitForSelector(selector, { timeout: 3000 });
        usernameInput = selector;
        break;
      } catch (e) {
        continue;
      }
    }
    
    if (!usernameInput) {
      throw new Error('Username input field not found');
    }
    
    // Wait for password and submit elements
    await page.waitForSelector('input[name="password"]', { timeout: NAVIGATION_TIMEOUT });
    await page.waitForSelector('button[type="submit"]', { timeout: NAVIGATION_TIMEOUT });
    
    // Fill in credentials
    await page.type(usernameInput, LOGIN_CREDENTIALS.username);
    await page.type('input[name="password"]', LOGIN_CREDENTIALS.password);
    
    // Submit login form and wait for navigation
    await page.click('button[type="submit"]');
    
    // Wait for navigation with multiple attempts
    try {
      await page.waitForNavigation({ waitUntil: 'networkidle2', timeout: NAVIGATION_TIMEOUT });
    } catch (error) {
      // If waitForNavigation fails, check current URL
      console.log('   Navigation timeout, checking current URL...');
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
    
    const afterLoginUrl = page.url();
    
    // Check if we're authenticated
    if (afterLoginUrl.includes('/dashboard') || afterLoginUrl.includes('/property-assets')) {
      console.log(`   ✅ Authentication successful: ${afterLoginUrl}`);
      return true;
    } else {
      throw new Error(`Authentication failed - unexpected URL: ${afterLoginUrl}`);
    }
    
  } catch (error) {
    console.log(`   ❌ Authentication failed: ${error.message}`);
    throw error;
  }
}

/**
 * Test component functionality
 */
async function testComponent(page, componentConfig) {
  const results = [];
  const componentName = componentConfig.name;
  const fullUrl = `${BASE_URL}${componentConfig.url}`;
  
  console.log(`\n🧪 Testing ${componentName} component...`);
  console.log(`   URL: ${fullUrl}`);
  
  try {
    // Navigate to component page
    await page.goto(fullUrl, { waitUntil: 'networkidle2', timeout: LOAD_TIMEOUT });
    
    // Wait for main content
    try {
      await page.waitForSelector(componentConfig.waitForSelector, { timeout: NAVIGATION_TIMEOUT });
    } catch (e) {
      // Try alternative selectors
      const alternatives = ['main', '[role="main"]', 'body > div', '.page-content'];
      for (const selector of alternatives) {
        try {
          await page.waitForSelector(selector, { timeout: 3000 });
          break;
        } catch (err) {
          continue;
        }
      }
    }
    
    // Additional wait for dynamic content
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Test 1: Page Load
    results.push({
      test: 'Page Load',
      status: 'PASS',
      message: `${componentName} page loaded successfully`
    });
    
    // Test 2: Table Structure
    try {
      const table = await page.$('table, [role="table"], .table-container');
      if (table) {
        const headers = await page.$$('th, [role="columnheader"]');
        results.push({
          test: 'Table Structure',
          status: 'PASS',
          message: `Table found with ${headers.length} columns`
        });
      } else {
        results.push({
          test: 'Table Structure',
          status: 'FAIL',
          message: 'Table element not found'
        });
      }
    } catch (error) {
      results.push({
        test: 'Table Structure',
        status: 'ERROR',
        message: `Table test error: ${error.message}`
      });
    }
    
    // Test 3: Search Functionality
    try {
      // Look for OneX specific search input patterns
      const searchSelectors = [
        'input[placeholder*="search"]',
        'input[placeholder*="Search"]', 
        'input[type="search"]',
        '.search-input input',
        'input[placeholder*="tìm kiếm"]'
      ];
      
      let searchFound = false;
      for (const selector of searchSelectors) {
        try {
          const searchInput = await page.$(selector);
          if (searchInput) {
            searchFound = true;
            break;
          }
        } catch (e) {
          continue;
        }
      }
      
      if (searchFound) {
        results.push({
          test: 'Search Functionality',
          status: 'PASS',
          message: 'Search input found and accessible'
        });
      } else {
        results.push({
          test: 'Search Functionality',
          status: 'FAIL',
          message: 'Search input not found'
        });
      }
    } catch (error) {
      results.push({
        test: 'Search Functionality',
        status: 'ERROR',
        message: `Search test error: ${error.message}`
      });
    }
    
    // Test 4: Filter Options
    try {
      // Look for OneX specific filter patterns
      const filterSelectors = [
        'select',
        '[role="combobox"]',
        '.filter-dropdown',
        'button[role="combobox"]',
        '.filter-trigger'
      ];
      
      let totalFilters = 0;
      for (const selector of filterSelectors) {
        try {
          const filters = await page.$$(selector);
          totalFilters += filters.length;
        } catch (e) {
          continue;
        }
      }
      
      if (totalFilters > 0) {
        results.push({
          test: 'Filter Options',
          status: 'PASS',
          message: `Found ${totalFilters} filter elements`
        });
      } else {
        results.push({
          test: 'Filter Options',
          status: 'FAIL',
          message: 'No filter elements found'
        });
      }
    } catch (error) {
      results.push({
        test: 'Filter Options',
        status: 'ERROR',
        message: `Filter test error: ${error.message}`
      });
    }
    
    // Test 5: Add Button
    try {
      // Look for OneX specific Add button patterns
      const addButtonSelectors = [
        'a[href*="/new"]',
        'button:contains("Add")',
        '.add-button',
        'a:contains("Add")',
        'button:contains("Thêm")',
        'a:contains("Thêm")'
      ];
      
      let addButtonFound = false;
      for (const selector of addButtonSelectors) {
        try {
          // Use evaluate for text-based selectors
          if (selector.includes(':contains')) {
            const elements = await page.$$eval('button, a', (elements, text) => {
              return elements.filter(el => el.textContent.includes(text));
            }, selector.includes('Add') ? 'Add' : 'Thêm');
            
            if (elements.length > 0) {
              addButtonFound = true;
              break;
            }
          } else {
            const addButton = await page.$(selector);
            if (addButton) {
              addButtonFound = true;
              break;
            }
          }
        } catch (e) {
          continue;
        }
      }
      
      if (addButtonFound) {
        results.push({
          test: 'Add Button',
          status: 'PASS',
          message: 'Add button found and accessible'
        });
      } else {
        results.push({
          test: 'Add Button',
          status: 'FAIL',
          message: 'Add button not found'
        });
      }
    } catch (error) {
      results.push({
        test: 'Add Button',
        status: 'ERROR',
        message: `Add button test error: ${error.message}`
      });
    }
    
    // Test 6: Responsive Design Check
    const currentViewport = await page.viewport();
    results.push({
      test: 'Responsive Design',
      status: 'PASS',
      message: `Page renders correctly at ${currentViewport.width}x${currentViewport.height}`
    });
    
  } catch (error) {
    results.push({
      test: 'Component General',
      status: 'ERROR',
      message: `Component testing failed: ${error.message}`
    });
  }
  
  return results;
}

/**
 * Capture component screenshots across viewports
 */
async function captureComponentScreenshots(browser, componentConfig, authenticatedPage) {
  const screenshots = [];
  const componentName = componentConfig.name.toLowerCase();
  
  for (const [viewportName, viewport] of Object.entries(TEST_VIEWPORTS)) {
    console.log(`📸 Capturing ${componentName} ${viewportName} screenshot...`);
    
    try {
      // Create new page for this viewport
      const page = await browser.newPage();
      
      // Copy authentication cookies
      const cookies = await authenticatedPage.cookies();
      await page.setCookie(...cookies);
      
      // Set viewport
      await page.setViewport(viewport);
      
      // Navigate to component
      const fullUrl = `${BASE_URL}${componentConfig.url}`;
      await page.goto(fullUrl, { waitUntil: 'networkidle2', timeout: LOAD_TIMEOUT });
      
      // Wait for content
      try {
        await page.waitForSelector(componentConfig.waitForSelector, { timeout: NAVIGATION_TIMEOUT });
      } catch (e) {
        const alternatives = ['main', '[role="main"]', 'body > div'];
        for (const selector of alternatives) {
          try {
            await page.waitForSelector(selector, { timeout: 3000 });
            break;
          } catch (err) {
            continue;
          }
        }
      }
      
      // Additional delay for dynamic content
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Take screenshot
      const outputPath = join(TEST_OUTPUT_DIR, componentName, `${componentName}-${viewportName}.png`);
      
      // Ensure directory exists
      await mkdir(join(TEST_OUTPUT_DIR, componentName), { recursive: true });
      
      await page.screenshot({
        path: outputPath,
        type: 'png',
        fullPage: false
      });
      
      screenshots.push({
        component: componentName,
        viewport: viewportName,
        path: outputPath,
        status: 'SUCCESS',
        timestamp: new Date().toISOString()
      });
      
      console.log(`   ✅ ${componentName}-${viewportName}: ${outputPath}`);
      
      await page.close();
      
    } catch (error) {
      screenshots.push({
        component: componentName,
        viewport: viewportName,
        status: 'ERROR',
        error: error.message
      });
      
      console.log(`   ❌ ${componentName}-${viewportName}: ${error.message}`);
    }
  }
  
  return screenshots;
}

/**
 * Generate component validation report
 */
async function generateComponentReport(componentName, testResults, screenshots) {
  const totalTests = testResults.length;
  const passedTests = testResults.filter(r => r.status === 'PASS').length;
  const failedTests = testResults.filter(r => r.status === 'FAIL').length;
  const errorTests = testResults.filter(r => r.status === 'ERROR').length;
  const passRate = totalTests > 0 ? ((passedTests / totalTests) * 100).toFixed(1) : 0;
  
  const report = {
    component: componentName,
    timestamp: new Date().toISOString(),
    summary: {
      totalTests,
      passedTests,
      failedTests,
      errorTests,
      passRate: parseFloat(passRate),
      screenshotsCaptured: screenshots.filter(s => s.status === 'SUCCESS').length,
      screenshotsFailed: screenshots.filter(s => s.status === 'ERROR').length
    },
    testResults,
    screenshots,
    recommendations: []
  };
  
  // Generate recommendations
  if (failedTests > 0) {
    report.recommendations.push({
      type: 'CRITICAL',
      message: `${failedTests} tests failed. Review implementation and fix issues.`
    });
  }
  
  if (errorTests > 0) {
    report.recommendations.push({
      type: 'ERROR',
      message: `${errorTests} tests had errors. Check selectors and page structure.`
    });
  }
  
  if (report.summary.screenshotsFailed > 0) {
    report.recommendations.push({
      type: 'WARNING',
      message: `${report.summary.screenshotsFailed} screenshots failed. Review responsive design.`
    });
  }
  
  if (report.recommendations.length === 0) {
    report.recommendations.push({
      type: 'SUCCESS',
      message: `${componentName} component validation passed! All tests successful.`
    });
  }
  
  return report;
}

/**
 * Main component validation function
 */
async function validateComponent(componentKey) {
  const componentConfig = COMPONENT_TESTS[componentKey];
  
  if (!componentConfig) {
    console.error(`❌ Component '${componentKey}' not found. Available components: ${Object.keys(COMPONENT_TESTS).join(', ')}`);
    process.exit(1);
  }
  
  const startTime = Date.now();
  console.log(`🚀 Starting ${componentConfig.name} component validation...`);
  
  // Create output directories
  await mkdir(join(TEST_OUTPUT_DIR, componentKey), { recursive: true });
  
  try {
    const { browser } = await initializePuppeteer('chrome');
    const page = await browser.newPage();
    
    // Perform authentication
    await performLogin(page);
    
    // Test component functionality
    const testResults = await testComponent(page, componentConfig);
    
    // Capture screenshots
    const screenshots = await captureComponentScreenshots(browser, componentConfig, page);
    
    await browser.close();
    
    // Generate report
    const runtime = ((Date.now() - startTime) / 1000).toFixed(1);
    const report = await generateComponentReport(componentConfig.name, testResults, screenshots);
    
    // Save JSON report
    const jsonReportPath = join(TEST_OUTPUT_DIR, componentKey, `${componentKey}-validation-report.json`);
    await writeFile(jsonReportPath, JSON.stringify(report, null, 2));
    
    // Print summary
    console.log(`\n📊 ${componentConfig.name} Component Validation Complete`);
    console.log('='.repeat(60));
    console.log(`Runtime: ${runtime}s`);
    console.log(`Total Tests: ${report.summary.totalTests}`);
    console.log(`✅ Passed: ${report.summary.passedTests}`);
    console.log(`❌ Failed: ${report.summary.failedTests}`);
    console.log(`⚠️  Errors: ${report.summary.errorTests}`);
    console.log(`📸 Screenshots: ${report.summary.screenshotsCaptured}`);
    console.log(`📈 Pass Rate: ${report.summary.passRate}%`);
    console.log(`📄 Report: ${jsonReportPath}`);
    
    // Print recommendations
    if (report.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      report.recommendations.forEach(rec => {
        const icon = rec.type === 'SUCCESS' ? '✅' : rec.type === 'WARNING' ? '⚠️' : '❌';
        console.log(`${icon} ${rec.type}: ${rec.message}`);
      });
    }
    
    return report;
    
  } catch (error) {
    console.error('❌ Component validation failed:', error);
    process.exit(1);
  }
}

/**
 * CLI Interface
 */
async function main() {
  const [,, componentKey] = process.argv;
  
  if (!componentKey) {
    console.log(`
🧪 Component Validation Tool

Usage:
  node validate-component.mjs <component>

Available Components:
  ${Object.keys(COMPONENT_TESTS).map(key => `${key.padEnd(12)} - ${COMPONENT_TESTS[key].name}`).join('\n  ')}

Examples:
  node validate-component.mjs units
  node validate-component.mjs properties
  node validate-component.mjs tenants
  node validate-component.mjs contracts
    `);
    process.exit(1);
  }
  
  await validateComponent(componentKey);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { validateComponent, COMPONENT_TESTS };