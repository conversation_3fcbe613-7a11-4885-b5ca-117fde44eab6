"use client";

import { useTranslation } from "react-i18next";

import { PropertyGallery } from "@/features/property-assets/components/PropertyImageGallery/PropertyGallery";

import { CustomBreadcrumb } from "@/components/Layout/CustomBreadCrumb/custom-breadcrumb";

export default function PropertyGalleryPage() {
  const { t } = useTranslation();

  return (
    <div className="p-6">
      {/* Header with Breadcrumb */}
      <div className="mb-6">
        <CustomBreadcrumb />
        <h1 className="mt-2 text-2xl font-bold text-foreground">
          {t("nav.gallery") || "Property Gallery"}
        </h1>
        <p className="text-sm text-muted-foreground">
          {t("pages.gallery.description") ||
            "Manage and organize property photos with advanced gallery features"}
        </p>
      </div>

      {/* Gallery Component */}
      <PropertyGallery />
    </div>
  );
}
