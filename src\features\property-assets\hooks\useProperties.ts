import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

// Types
import type { ApiResponse, CreateProperty, PaginatedResponse, Property } from "../types";

// API functions (connecting to Vietnamese property API endpoints)
const propertiesApi = {
  // Get all properties
  getProperties: async (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: Property["status"];
  }): Promise<PaginatedResponse<Property>> => {
    const searchParams = new URLSearchParams();

    if (params?.page) searchParams.append("page", params.page.toString());
    if (params?.limit) searchParams.append("limit", params.limit.toString());
    if (params?.search) searchParams.append("search", params.search);
    if (params?.status) searchParams.append("status", params.status);

    const response = await fetch(`/api/property-assets/properties?${searchParams.toString()}`);
    if (!response.ok) {
      throw new Error("Failed to fetch properties");
    }

    const result = await response.json();
    return result.data;
  },

  // Get property by ID
  getProperty: async (id: string): Promise<Property> => {
    const response = await fetch(`/api/property-assets/properties/${id}`);
    if (!response.ok) {
      throw new Error("Failed to fetch property");
    }

    const result = await response.json();
    return result.data;
  },

  // Create property
  createProperty: async (data: CreateProperty): Promise<Property> => {
    const response = await fetch("/api/property-assets/properties", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to create property");
    }

    const result = await response.json();
    return result.data;
  },

  // Update property
  updateProperty: async (id: string, data: Partial<CreateProperty>): Promise<Property> => {
    const response = await fetch(`/api/property-assets/properties/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error("Failed to update property");
    }

    const result = await response.json();
    return result.data;
  },

  // Delete property
  deleteProperty: async (id: string): Promise<void> => {
    const response = await fetch(`/api/property-assets/properties/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error("Failed to delete property");
    }
  },
};

// Query keys
export const propertiesKeys = {
  all: ["properties"] as const,
  lists: () => [...propertiesKeys.all, "list"] as const,
  list: (params?: any) => [...propertiesKeys.lists(), params] as const,
  details: () => [...propertiesKeys.all, "detail"] as const,
  detail: (id: string) => [...propertiesKeys.details(), id] as const,
};

// Hooks
export const useProperties = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: Property["status"];
}) => {
  return useQuery({
    queryKey: propertiesKeys.list(params),
    queryFn: () => propertiesApi.getProperties(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useProperty = (id: string, enabled: boolean = true) => {
  return useQuery({
    queryKey: propertiesKeys.detail(id),
    queryFn: () => propertiesApi.getProperty(id),
    enabled: enabled && !!id,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: propertiesApi.createProperty,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: propertiesKeys.lists() });
      toast.success("Property created successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to create property: ${error.message}`);
    },
  });
};

export const useUpdateProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: Partial<CreateProperty> }) =>
      propertiesApi.updateProperty(id, data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: propertiesKeys.lists() });
      queryClient.setQueryData(propertiesKeys.detail(data.id), data);
      toast.success("Property updated successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to update property: ${error.message}`);
    },
  });
};

export const useDeleteProperty = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: propertiesApi.deleteProperty,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: propertiesKeys.lists() });
      toast.success("Property deleted successfully");
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete property: ${error.message}`);
    },
  });
};
