import { Suspense } from "react";
import { Metadata } from "next";
import Loading from "@/app/loading";

import { ReportsComponent } from "@/features/property-assets/components/Reports";

export const metadata: Metadata = {
  title: "Property Reports | OneX ERP",
  description: "Generate comprehensive reports and analytics for property performance",
};

export default function ReportsPage() {
  return (
    <div className="p-6">
      <Suspense
        fallback={
          <div className="flex h-64 items-center justify-center">
            <Loading />
          </div>
        }>
        <ReportsComponent />
      </Suspense>
    </div>
  );
}
