# Jira Ticket Templates

This folder contains comprehensive templates for different types of Jira tickets to ensure every ticket has sufficient information for successful implementation.

## 📋 Available Templates

### 1. [BUG_TEMPLATE.md](./BUG_TEMPLATE.md) 🐛
**Use for**: Defects, errors, broken functionality
**Ensures**: Clear reproduction steps, environment details, acceptance criteria for fixes
**Key Sections**: Reproduction steps, affected components, error logs, acceptance criteria

### 2. [STORY_TEMPLATE.md](./STORY_TEMPLATE.md) 📖  
**Use for**: New features, user-facing functionality
**Ensures**: User value, business justification, UI/UX requirements, technical specifications
**Key Sections**: User story, acceptance criteria, technical requirements, test scenarios

### 3. [TASK_TEMPLATE.md](./TASK_TEMPLATE.md) 🔧
**Use for**: Technical work, refactoring, infrastructure, documentation
**Ensures**: Clear technical objectives, implementation approach, success metrics
**Key Sections**: Technical specifications, implementation plan, testing strategy

### 4. [EPIC_TEMPLATE.md](./EPIC_TEMPLATE.md) 🚀
**Use for**: Large initiatives spanning multiple stories/tasks
**Ensures**: Strategic alignment, comprehensive scope, stakeholder alignment
**Key Sections**: Business objectives, epic breakdown, success criteria, timeline

### 5. [SPIKE_TEMPLATE.md](./SPIKE_TEMPLATE.md) 🔍
**Use for**: Research, investigation, proof of concept work
**Ensures**: Clear research questions, methodology, time boxing, deliverables
**Key Sections**: Research objectives, methodology, evaluation criteria, deliverables

## 🎯 Template Selection Guide

### When to Use Each Template

```mermaid
flowchart TD
    A[Need to create Jira ticket] --> B{What type of work?}
    
    B -->|Something is broken| C[BUG_TEMPLATE]
    B -->|New user feature| D[STORY_TEMPLATE]  
    B -->|Technical work| E[TASK_TEMPLATE]
    B -->|Large initiative| F[EPIC_TEMPLATE]
    B -->|Need to research| G[SPIKE_TEMPLATE]
    
    C --> H[Clear reproduction steps?]
    D --> I[User value defined?]
    E --> J[Technical scope clear?]
    F --> K[Business objectives clear?]
    G --> L[Research questions defined?]
    
    H -->|No| M[Use template checklist]
    I -->|No| M
    J -->|No| M  
    K -->|No| M
    L -->|No| M
    
    H -->|Yes| N[Ready for implementation]
    I -->|Yes| N
    J -->|Yes| N
    K -->|Yes| N
    L -->|Yes| N
```

## 🔄 Implementation Readiness Workflow

### Step 1: Choose Template
Select the appropriate template based on work type.

### Step 2: Fill Template
Complete all sections of the template. **Don't skip sections** - each is designed to ensure implementation readiness.

**For UI Features**: Ensure Figma links are provided and accessible via MCP.

### Step 3: Review Checklist
Each template has a checklist at the bottom. Ensure all items are checked before submitting.

### Step 4: Analyze Readiness
Use `/project:jira:analyze-ticket` to verify the ticket has sufficient information.

### Step 5: Implement with Automated Validation
- **🟢 Ready**: Start implementation with appropriate workflow
  - **UI Features**: Use `/project:ui-implement` for Figma → Puppeteer workflow
  - **Other Work**: Use `/project:bugfix` or standard implementation
- **🟡 Needs Clarification**: Ask specific questions  
- **🔴 Not Ready**: Request comprehensive requirements

### Step 6: Automated Quality Assurance (UI Features)
- **Visual Validation**: Use `/project:visual-validate` for Puppeteer comparison
- **Iterative Refinement**: Continue until >98% visual similarity achieved
- **Cross-browser Testing**: Validate consistency across browsers

## 📊 Template Benefits

### For Product Managers
- **Consistent Requirements**: Every story has user value and acceptance criteria
- **Business Alignment**: Clear connection between features and business goals  
- **Stakeholder Communication**: Structured information for all stakeholders

### For Developers
- **Clear Specifications**: No ambiguity about what to build
- **Technical Guidance**: Implementation approach defined upfront
- **Testing Clarity**: Clear acceptance criteria and test scenarios

### For QA Teams
- **Test Planning**: Comprehensive test scenarios included
- **Acceptance Criteria**: Clear definition of "done"
- **Environment Details**: Specific testing requirements

### For Business Stakeholders  
- **Value Visibility**: Clear business justification for all work
- **Progress Tracking**: Well-defined milestones and success criteria
- **Risk Management**: Identified risks and mitigation strategies

## 🚫 Common Pitfalls to Avoid

### ❌ Template Shortcuts
**Don't**: Skip sections because they "don't apply"
**Do**: Consider why the section exists and how it might be relevant

### ❌ Vague Requirements
**Don't**: Use phrases like "improve performance" or "make it better"
**Do**: Define specific, measurable outcomes

### ❌ Missing Context
**Don't**: Assume everyone knows the background
**Do**: Provide sufficient context for someone new to understand

### ❌ No Acceptance Criteria
**Don't**: Leave acceptance criteria undefined or vague
**Do**: Define specific, testable criteria for completion

## 📖 Template Usage Examples

### Example 1: Bug Report (OWS-770 Analysis)
**Original Ticket**: "[DESIGN] Add sort column for Kanban (CRM)"
**Problem**: Description only said "Title:" - no actual requirements

**Using BUG_TEMPLATE would have caught**:
- Missing detailed description
- No steps to reproduce the issue
- No acceptance criteria
- No affected components identified

### Example 2: Story Enhancement
**Original**: "Users want better product search"
**With STORY_TEMPLATE**:
- **User Story**: "As a store manager, I want to search products by multiple criteria so that I can quickly find items during order entry"
- **Acceptance Criteria**: Specific search capabilities defined
- **Technical Requirements**: Search API and UI specifications
- **Test Scenarios**: Comprehensive test cases

### Example 3: Technical Task Improvement
**Original**: "Improve system performance"
**With TASK_TEMPLATE**:
- **Objective**: "Reduce product list API response time from 2s to 500ms"
- **Technical Approach**: Specific optimization strategies
- **Success Metrics**: Measurable performance targets
- **Implementation Plan**: Phased approach with milestones

## 🔧 Integration with OneX Workflow

### Standard Workflow Integration
```bash
# 1. Create ticket using template
# Copy template content to Jira description

# 2. Analyze ticket readiness  
/project:jira:analyze-ticket TICKET-ID

# 3. If ready, proceed with implementation
/project:bugfix TICKET-ID  # for bugs and general work
# or other appropriate commands

# 4. Update ticket during implementation
node scripts/jira-api.mjs add-comment TICKET-ID "Implementation update"
```

### UI Implementation Workflow (New)
```bash
# 1. Create UI ticket using STORY_TEMPLATE or TASK_TEMPLATE
# Ensure Figma file URL and frame links are included

# 2. Check template compliance
/project:jira:template-check TICKET-ID

# 3. Analyze implementation readiness
/project:jira:analyze-ticket TICKET-ID

# 4. Implement UI with Figma integration
/project:ui-implement TICKET-ID

# 5. Automated visual validation
/project:visual-validate COMPONENT-NAME

# 6. Iterate until perfect
# Repeat steps 4-5 until >98% visual similarity achieved
```

### Quality Gates
Each template ensures these quality gates are met:
- **Business Value**: Clear justification for the work
- **User Impact**: Understanding of who benefits and how
- **Technical Clarity**: Specific implementation requirements
- **Success Criteria**: Measurable definition of completion
- **Test Strategy**: Plan for validating the solution

### Enhanced UI Quality Gates (New)
For UI features, additional quality gates include:
- **Figma Integration**: Design files accessible via MCP for automated extraction
- **Visual Accuracy**: >98% pixel similarity to design specifications
- **Responsive Validation**: Automated testing across all breakpoints
- **Interactive States**: All component states properly implemented and tested
- **Cross-browser Consistency**: Automated validation across Chrome, Firefox, Safari

## 📚 OneX ERP Specific Considerations

### Multi-Tenant Architecture
Templates include sections for:
- **Tenant Impact**: Which tenants (OneX/OnexSync/OnexBots) are affected
- **Configuration**: Tenant-specific settings or behavior
- **Testing**: Cross-tenant testing requirements

### Feature Modules
Templates reference OneX feature structure:
- `src/features/products/` - Product management
- `src/features/orders/` - Order processing
- `src/features/customers/` - Customer management
- `src/features/integration/` - E-commerce integrations
- `src/features/bots/` - AI chatbot system
- `src/features/auth/` - Authentication

### Technology Stack
Templates consider OneX tech stack:
- **Frontend**: Next.js 14, TypeScript, React, Tailwind CSS
- **State Management**: Redux Toolkit, TanStack Query
- **Testing**: Jest, Testing Library, Playwright
- **APIs**: RESTful APIs with TypeScript interfaces

## 🎓 Training & Adoption

### For New Team Members
1. **Read Templates**: Understand the structure and purpose
2. **Review Examples**: Look at well-written tickets using templates
3. **Practice**: Create sample tickets using templates
4. **Get Feedback**: Have experienced team members review

### For Existing Team Members
1. **Gradual Adoption**: Start with one template type
2. **Template Champions**: Designate template advocates in each role
3. **Regular Reviews**: Assess template effectiveness in retrospectives
4. **Continuous Improvement**: Update templates based on learnings

### Success Metrics
Track these metrics to measure template effectiveness:
- **Requirement Clarity**: Fewer clarification requests during implementation
- **Rework Reduction**: Less scope creep and requirement changes
- **Implementation Speed**: Faster development due to clear requirements
- **Quality Improvement**: Better final outcomes and user satisfaction

---

## 📝 Quick Reference

### Template Checklist
Before submitting any ticket:
- [ ] Appropriate template used
- [ ] All sections completed (no "TBD" or empty sections)
- [ ] Acceptance criteria specific and testable
- [ ] Business value clearly articulated
- [ ] Technical approach feasible
- [ ] Dependencies identified
- [ ] Timeline realistic

### Getting Help
- **Template Questions**: Contact Product Management team
- **Technical Guidance**: Contact Engineering leads
- **Process Issues**: Contact Project Management
- **Tool Support**: Contact DevOps team

Remember: **Better templates lead to better tickets, which lead to better software!**