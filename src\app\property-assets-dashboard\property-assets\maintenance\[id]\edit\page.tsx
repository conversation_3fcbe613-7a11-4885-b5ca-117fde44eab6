"use client";

import { Suspense } from "react";
import { Metadata } from "next";

import { MaintenanceForm } from "@/features/property-assets/components/MaintenanceForm";
import { useMaintenanceRequest } from "@/features/property-assets/hooks/useMaintenance";

interface EditMaintenancePageProps {
  params: {
    id: string;
  };
}

function EditMaintenanceContent({ maintenanceId }: { maintenanceId: string }) {
  const { data: maintenance, isLoading, error } = useMaintenanceRequest(maintenanceId);

  if (isLoading) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">Loading maintenance request data...</div>
      </div>
    );
  }

  if (error || !maintenance) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center text-red-500">
          Error loading maintenance request data. Please try again.
        </div>
      </div>
    );
  }

  return <MaintenanceForm initialData={maintenance} isEditing={true} />;
}

export default function EditMaintenancePage({ params }: EditMaintenancePageProps) {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <EditMaintenanceContent maintenanceId={params.id} />
    </Suspense>
  );
}
